import argparse
import os, sys, glob
import numpy as np
from collections import OrderedDict

def parse_data(filename=None, fmt=None):
    """ Parsing space-separated data
    Parameters
    ----------
    filename: str
        file name of *.coord or *.dipoRavg
    fmt: str
        [None]: auto-detection, 'coord'|'dipoRavg'|'modulation'|'defects'|'localfield'

    Returns
    -------
    out: numpy.array
        named array of data, column number depends on the raw data
    """
    with open (filename, 'r') as fr:
        raw = fr.readlines()
    data_list = [tuple(map(float,c)) for c in list(map(lambda line: line.strip().split(), raw))]
    nrow, ncol = np.shape(data_list)
    # format
    FMT = {18: "x y z ux uy uz ppx ppy ppz ddx ddy ddz arx ary arz apx apy apz",
           6: "x y z ux uy uz",
           4: "x y z mod"}
    if fmt in ['coord']:
        nc = 18
    elif fmt in ['dipoRavg', 'defects', 'localfield']:
        nc = 6
    elif fmt in ['modulation']:
        nc = 4
    elif fmt is None:
        if ncol in FMT:
            nc = ncol
        else:
            print(f'[Error]: undefined format!')
    else:
        print(f'[Error]: format {fmt} not found!')
    # trim data
    if ncol!=nc:
        data_list = [_[:nc] for _ in data_list]
    columns = FMT[nc]
    dt = [(_c, np.dtype(int)) for _c in "x y z".split()]+[(_c, np.dtype(float)) for _c in columns.split()[3:]]
    return np.array(data_list, dtype=dt)

def read_dump(f_dump):
    """ Reading customized dump format file for Feram

    Parameters
    ----------
    f_dump: str
        filename of dump file

    Returns
    -------
    Dump: dict
        dict of coords for different timesteps
    """
    # read dump
    with open(f_dump, 'r') as fr:
        raw = fr.readlines()
    # parse dump
    Dump = OrderedDict()
    i,n = 0,0
    lxyz = []
    cols = []
    dtype = []
    for line in raw:
        items = line.strip().split()
        if items[0]=='ITEM:' and items[1]=='TIMESTEP':
            i,n = 0,0
        if i in [0,2,4]:
            pass
        elif i==1:
            key = int(items[0])
            data = []
        elif i==3:
            n = int(items[0])
        elif i in [5,6,7]:
            lxyz.append( int(items[-1]) )
        elif i==8:
            cols = items[2:]
            dtype = [(_c, np.dtype(int)) for _c in cols[:6]]+[(_c, np.dtype(float)) for _c in cols[6:]]
        else:
            if i>8:
                data.append(tuple(map(float, items)))
            if i==8+n:
                Dump[key] = np.array(data, dtype=dtype)
        i+=1
    return Dump

def feram2dump(path='./', ext='.coord', step=1, reverse=False, size=(10,10,10), f_mod=None):
    """ Converting Feram outputs to dump format

    Parameters
    ----------
    path: str
        path for the converted dump file
    ext: str
        extension of the Feram files
    step: int
        [1]: to use all files; n means to keep files only by every n steps
    reverse: bool
        True if output in reverse order
    size: tuple
        (lx, ly, lz) of simulation box
    f_mod: str
        [None], file name of the modulation, where the values will be assigned to v_mod

    Returns
    -------
    None
    """
    # main program
    f_out='dump_feram_{}'.format(ext[1:]) if ext[0]=='.' else f'dump_feram_{ext}'
    f_dipos=sorted(glob.glob(os.path.join(path, '*'+ext)), reverse=reverse)[::step]
    if len(f_dipos)==0:
        print('[Warning] No *.{} file found!'.format(ext))
        exit()
    
    if f_mod!=None:
        mod=parse_data(f_mod, fmt='modulation')
        xm, ym, zm, mm = mod['x'], mod['y'], mod['z'], mod['mod'].astype(int)
    else:
        mm=[0]
    
    Typ={}
    for _i,_t in enumerate(np.unique(mm)):
        if _t not in Typ:
            Typ[_t]=_i+1
    
    with open(f_out, 'w') as fw:
        for _i, _f in enumerate(f_dipos):
            print(_f)
            data = parse_data(filename=_f)
            add_items = ''
            x, y, z = data['x'], data['y'], data['z']
            if size is None:
                lx, ly, lz = x.max()+1, y.max()+1, z.max()+1
            else:
                lx, ly, lz = size
            if 'mod' in data.dtype.names:
                ux = uy = uz = data['mod']
            else:
                ux, uy, uz = data['ux'], data['uy'], data['uz']
            mm = mm if f_mod!=None else [0 for _ in range(len(x))]
            ncell=len(data)
            fw.write('ITEM: TIMESTEP\n')
            fw.write('%d\t%s\n'%(_i, _f))
            fw.write('ITEM: NUMBER OF ATOMS\n')
            fw.write('%d\n'%ncell)
            fw.write('ITEM: BOX BOUNDS pp pp pp\n')
            fw.write('0 %d\n'%(lx))
            fw.write('0 %d\n'%(ly))
            fw.write('0 %d\n'%(lz))
            fw.write('ITEM: ATOMS id type v_mod xu yu zu fx fy fz {}\n'.format(add_items))
            for _j in range(len(data)):
                fw.write('{:d} {:d} {:d} '.format(_j+1, Typ[mm[_j]], mm[_j]))
                fw.write('{:d} {:d} {:d} '.format(int(x[_j]), int(y[_j]), int(z[_j])))
                fw.write('{:.6f} {:.6f} {:.6f} '.format(ux[_j], uy[_j], uz[_j]))
                fw.write('\n')
    print('FINISH: {}'.format(f_out))

def feram2xyz(filename=None, size=(1,1,1)):
    """ Converting Feram outputs to dump format

    Parameters
    ----------
    filename: str
        name of the Feram file
    size: tuple
        (lx, ly, lz) of simulation box

    Returns
    -------
    None
    """
    # main program
    if not os.path.exists(filename):
        print('[Warning] {} not found!'.format(filename))
        exit()
    print(filename)
    data = parse_data(filename)
    n=len(data)
    x, y, z = data['x'], data['y'], data['z']
    if size is None:
        xsize, ysize, zsize = x.max()+1, y.max()+1, z.max()+1
    else:
        xsize, ysize, zsize = size
    if 'mod' in data.dtype.names:
        ux = uy = uz = data['mod']
        M = data['mod']
        add_items = 'mod:R:1'
    else:
        ux, uy, uz = data['ux'], data['uy'], data['uz']
        M = np.zeros(n)
        add_items = 'u:R:3'

    f_out = filename+'.xyz'
    i=0
    TYP={}
    with open(f_out, 'w') as fw:
        fw.write('%d\n'%n)
        fw.write('Lattice="%d 0.0 0.0 0.0 %d 0.0 0.0 0.0 %d"'%(xsize,ysize,zsize))
        fw.write(f' Properties=species:S:1:pos:R:3:{add_items} Time=0.0\n')
        for n,row in enumerate(data):
            line = ' '.join(list(map(str,row)))
            if M[n] not in TYP:
                TYP[M[n]]='O%d '%i
                i+=1
            fw.write(TYP[M[n]]+line+'\n')
    print('FINISH: {}'.format(f_out))

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='Transform Feram output to dump format.')
    parser.add_argument('extension', metavar='ext', type=str, nargs=1,
                        help='file extension or filename if -o xyz')
    parser.add_argument('-p', dest='path', type=str, default='./',
                        help='set working directory (default=./)')
    parser.add_argument('-s', dest='step_size', type=int, default=1,
                        help='select files by a certain step size (default=1)')
    parser.add_argument('-r', dest='reverse', action='store_true', default=False,
                        help='sort files in reverse order (default=False)')
    parser.add_argument('-m', dest='modulation', type=str, default=None,
                        help='[optional] add modulation information to dump file (default=None)')
    choices = ['dump', 'xyz']
    parser.add_argument('-o', dest='options', type=str, default='dump', choices=choices,
                        help='to dump or xyz format (default=dump)')
    parser.add_argument('--size', dest='size', nargs='+', type=int, default=None,
                        help='size of the simulation box, e.g. 10 or 10 10 10 (default=None)')

    args = parser.parse_args()
    ext=args.extension[0]
    path=args.path
    step=args.step_size
    reverse=args.reverse
    f_mod=args.modulation
    f_opt=args.options
    size=args.size
    if size is not None and len(size) not in [1,3]:
        print('[Error]: Incorrect arguments for --size!')
    if f_opt=='dump':
        feram2dump(path=path, ext=ext, step=step, reverse=reverse, size=size, f_mod=f_mod)
    elif f_opt=='xyz':
        feram2xyz(filename=ext, size=size)
