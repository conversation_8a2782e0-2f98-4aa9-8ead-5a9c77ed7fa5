import numpy as np

class DomainWall():
    def __init__(self, Lbox=None, w_domain=1, n_domain=1, plane_norm=(1,0,0)):
        if Lbox==None:
            self.Lx = self.Ly = self.Lz = 2*n_domain*w_domain
        elif isinstance(Lbox, tuple) and len(Lbox)==3:
            self.Lx = Lbox[0]
            self.Ly = Lbox[1]
            self.Lz = Lbox[2]
        self.w_domain = w_domain if w_domain!=None else max(Lbox)//n_domain
        self.n_domain = n_domain
        self.plane_norm = plane_norm
        self.setp = False

    def set_L(self, axis=0, length=1):
        if axis==0:
            self.Lx = length
        elif axis==1:
            self.Ly = length
        elif axis==2:
            self.Lz = length

    def set_P_domain(self, *args):
        self.P_domain = [p for p in args]
        self.setp = True

    def assign_domain(self):
        dd = 0.5 if self.Lx%2==0 else 0
        f_p100 = lambda i,j,k: 1*sum([(i >= (n+dd)*self.w_domain)*(i < (n+1+dd)*self.w_domain) 
                                      for n in range(0, self.Lx//self.w_domain, 2)])
        f_p110 = lambda i,j,k: 1*sum([(i-j >= 1-n*self.w_domain)*(i-j < 1+(1-n)*self.w_domain) or 
                                      (i-j >= 1+n*self.w_domain)*(i-j < 1+(1+n)*self.w_domain) 
                                      for n in range(0, max(self.Lx,self.Ly)//self.w_domain+1, 2)])
        Planes = {(1,0,0): f_p100, (1,1,0): f_p110}
        return Planes[self.plane_norm]

    def generate_domain(self, display=False):
        f_domain = self.assign_domain()
        ary0=[]
        for z in range(self.Lz):
            ary1=[]
            for y in range(self.Ly):
                ary2=[]
                for x in range(self.Lx):
                    ary2.append(f_domain(x,y,z))
                ary1.append(ary2)
            ary0.append(ary1)
        # visualize domains
        if display==True:
            print(np.array(ary0))
        return ary0

    @staticmethod
    def write_file(f_out=None, text='', append=True):
        rw = 'a+' if append==True else 'w'
        with open(f_out, rw) as fw:
            fw.write(text)

    def write_domain(self, f_out=None, mode='coord', seed=None):
        rng = np.random.RandomState(seed)
        # P for each domain
        if self.setp == False:
            if self.plane_norm == (1,0,0):
                self.P_domain = [(0, 0.1, 0), (0, -0.1, 0)]
            elif self.plane_norm == (1,1,0):
                self.P_domain = [(0.1, 0, 0), (0, 0.1, 0)]
        # create domains
        text_SUM = ''
        self.write_file(f_out=f_out, append=False) # initialize
        ary0 = self.generate_domain()
        for z, ary1 in enumerate(ary0):
            for y, ary2 in enumerate(ary1):
                for x, val in enumerate(ary2):
                    if mode=='coord':
                        px, py, pz = self.P_domain[val]
                        dprx, dpry, dprz = [rng.normal(_p, 0.01, 1)[0] for _p in (px,py,pz)]
                        dppx, dppy, dppz = [rng.normal(0, 0.05, 1)[0] for _ in range(3)]
                        ddix, ddiy, ddiz = [rng.normal(0, 0.7, 1)[0] for _ in range(3)]
                        acrx, acry, acrz = [rng.normal(0, 0.01, 1)[0] for _ in range(3)]
                        acpx, acpy, acpz = [0.0 for _ in range(3)]
                        text_coord = '{:4d} {:4d} {:4d} '.format(x,y,z)
                        text_coord += '{: .6f} {: .6f} {: .6f} '.format(dprx,dpry,dprz)
                        text_coord += '{: .6E} {: .6E} {: .6E} '.format(dppx,dppy,dppz)
                        text_coord += '{: .6E} {: .6E} {: .6E} '.format(ddix,ddiy,ddiz)
                        text_coord += '{: .6E} {: .6E} {: .6E} '.format(acrx,acry,acrz)
                        text_coord += '{: .6f} {: .6f} {: .6f}\n'.format(acpx,acpy,acpz)
                        text_SUM += text_coord
                    elif mode=='localfield':
                        efx, efy, efz = self.P_domain[val]
                        text_field = '{:4d} {:4d} {:4d} '.format(x, y, z)
                        text_field += '{: .6f} {: .6f} {: .6f}\n'.format(efx, efy, efz)
                        text_SUM += text_field
        self.write_file(f_out=f_out, text=text_SUM, append=True)

if __name__ == '__main__':
    DW = DomainWall(Lbox=(48,48,48), w_domain=12, n_domain=2, plane_norm=(1,0,0))
    DW.set_P_domain((0,0.1,0),(0,-0.1,0))
    DW.write_domain(f_out='domain.restart', mode='coord', seed=None)
    DW.write_domain(f_out='domain.localfield', mode='localfield', seed=None)
