# FerroElectric Domain Analysis & Simulation

This repository contains analysis tools & simulation scripts for ferroelectric domains and their interaction with composites, interfaces and defects.

## Requirements
- Feram
- Python3.8
- See `requirements.txt`

## Usage
- Get the latest release from RUB-GitLab

```
git clone https://gitlab.ruhr-uni-bochum.de/tengssh/fedas
```

## Documentation

Please visit the [website](https://shenghanteng.gitlab.io/fedas).

## File structure
```
|- makefile_workchains: makefile for generating input
|- data_analysis/: scripts for data analysis
|- src/: .py for all Python scripts
|- tests/: .py for testing
|- workchains/: collection of input workchains
```

## Change log
- v0.0.1: 2024.7.11
- v0.1.0: 2025.1.16; updated `README.md`, built CI/CD pipelines
- v0.2.0: 2025.1.20; uploaded data_analysis/Jupyter notebooks

## Authors
- <PERSON><PERSON>-<PERSON>, SFC, ICAMS, RUB ([link](https://www.icams.de/institute/icams/members/members-detail/?detail=1935))

## Acknowledgements

The authors thank ICAMS and ZGH for the facilities and computational support.

## Copyright and license

This repository is licensed under the terms of the GPLv3. You can find a copy of the license in the [LICENSE](#LICENSE).
