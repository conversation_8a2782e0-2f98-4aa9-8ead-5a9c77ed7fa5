import os
import pytest
import numpy as np
# user-defined modules
from analyze_feram import Dipo_Analysis_Evo

test_dir = 'tests/'
f_td = lambda x: os.path.join(test_dir, x)

# constants
kb = 8.617333262e-5 # eV/K

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('example_sd.coord'), [f_td('example_sd.coord')]),
                          (f_td('example_sd.dipoRavg'), [f_td('example_sd.dipoRavg')]),
                          ([f_td('example_sd.coord'),f_td('example_sd.dipoRavg')], [f_td('example_sd.coord'),f_td('example_sd.dipoRavg')]),
                          (None, [None]),
                         ])
def test_inherent_init(inputs, outputs):
    if isinstance(inputs, list):
        DAE = Dipo_Analysis_Evo(*inputs)
    else:
        DAE = Dipo_Analysis_Evo(inputs)
    assert DAE.files==outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('example_sd.coord'), 1),
                          (f_td('example_sd.dipoRavg'), 1),
                          ([f_td('example_sd.coord'),f_td('example_sd.dipoRavg')], 2),
                         ])
def test_load_data_len(inputs, outputs):
    if isinstance(inputs, list):
        DAE = Dipo_Analysis_Evo(*inputs)
    else:
        DAE = Dipo_Analysis_Evo(inputs)
    DAE.load_data()
    assert len(DAE.Dataset)==outputs

@pytest.mark.parametrize('inputs, times, outputs',
                         [(f_td('example_sd.coord'), [0], [0]),
                          (f_td('example_sd.dipoRavg'), [0], [0]),
                          (f_td('example_sd.dipoRavg'), None, [0]),
                          ([f_td('example_sd.coord'),f_td('example_sd.dipoRavg')], [0,1], [0,1]),
                          ([f_td('example_sd.coord'),f_td('example_sd.dipoRavg')], None, [0,1]),
                         ])
def test_set_times(inputs, times, outputs):
    f_isnumbers = lambda x: np.all()
    if isinstance(inputs, list):
        DAE = Dipo_Analysis_Evo(*inputs)
    else:
        DAE = Dipo_Analysis_Evo(inputs)
    DAE.load_data()
    DAE.set_times(times)
    assert DAE.Times == outputs

@pytest.mark.parametrize('inputs, sites, outputs',
                         [(f_td('example_sd.coord'), [(0,0,0)], 1),
                          (f_td('example_sd.coord'), [(0,0,0),(0,0,1)], 2),
                          (f_td('example_sd.coord'), None, 1000),
                         ])
def test_select_sites_len(inputs, sites, outputs):
    if isinstance(inputs, list):
        DAE = Dipo_Analysis_Evo(*inputs)
    else:
        DAE = Dipo_Analysis_Evo(inputs)
    DAE.load_data()
    DAE.select_sites(sites=sites)
    assert len(DAE.SiteDataset['ux'])==outputs

@pytest.mark.parametrize('inputs, sites, outputs',
                         [(f_td('example_sd.coord'), [(0,0,0)], [(0,0,0)]),
                          (f_td('example_sd.coord'), [(0,0,0),(0,0,1)], [(0,0,0),(0,0,1)]),
                          (f_td('example_sd.coord'), None, np.vstack( tuple([_.ravel() for _ in np.mgrid[0:10,0:10,0:10]]) ).T),
                         ])
def test_select_sites_coordinates(inputs, sites, outputs):
    if isinstance(inputs, list):
        DAE = Dipo_Analysis_Evo(*inputs)
    else:
        DAE = Dipo_Analysis_Evo(inputs)
    DAE.load_data()
    DAE.select_sites(sites=sites)
    assert np.all(DAE.Sites==outputs)

@pytest.mark.parametrize('inputs, index, outputs',
                         [(None, None, [f_td('example_sd.dipoRavg')]),
                          ([f_td('example_sd.coord')], [0], [f_td('example_sd.coord'), f_td('example_sd.dipoRavg')]),
                          ([f_td('example_sd.coord')], [1], [f_td('example_sd.dipoRavg'), f_td('example_sd.coord')]),
                          ([f_td('example_sd.coord')], None, [f_td('example_sd.dipoRavg'), f_td('example_sd.coord')]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], [0,2], [f_td('example_t180.dipoRavg'), f_td('example_sd.dipoRavg'), f_td('example_sd.coord')]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, [f_td('example_sd.dipoRavg'), f_td('example_t180.dipoRavg'), f_td('example_sd.coord')]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], [1,1], [f_td('example_sd.dipoRavg'), f_td('example_sd.coord'),f_td('example_t180.dipoRavg')]),
                         ])
def test_add_snapshots_files(inputs, index, outputs):
    DAE = Dipo_Analysis_Evo(f_td('example_sd.dipoRavg'))
    DAE.load_data()
    DAE.set_times()
    DAE.add_snapshots(snapshots=inputs, index=index, times=None)
    assert DAE.files==outputs

@pytest.mark.parametrize('inputs, index, times, outputs',
                         [(None, None, None, [0]),
                          ([f_td('example_sd.coord')], [0], [0], [0]),
                          ([f_td('example_sd.coord')], [1], [1], [0,1]),
                          ([f_td('example_sd.coord')], None, None, [0,1.05]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], [0,2], [-1,1], [-1,0,1]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, [2,4], [0,2,4]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, None, [0,1.05,2.1]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, [1,1], [0]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, [0,1], [0]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], [-1,2], [-1,1], [-1,0,1]),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], [-1,2], [-1,0], [0]),
                         ])
def test_add_snapshots_times(inputs, index, times, outputs):
    DAE = Dipo_Analysis_Evo(f_td('example_sd.dipoRavg'))
    DAE.load_data()
    DAE.set_times()
    DAE.add_snapshots(snapshots=inputs, index=index, times=times)
    assert DAE.Times==outputs

@pytest.mark.parametrize('inputs, index, times, outputs',
                         [(None, None, None, 1),
                          ([f_td('example_sd.coord')], [0], [0], 1),
                          ([f_td('example_sd.coord')], [1], [1], 2),
                          ([f_td('example_sd.coord')], None, None, 2),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], [0,2], [-1,1], 3),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, [2,4], 3),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, None, 3),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], None, [1,1], 1),
                         ])
def test_add_snapshots_dataset_len(inputs, index, times, outputs):
    DAE = Dipo_Analysis_Evo(f_td('example_sd.dipoRavg'))
    DAE.load_data()
    DAE.set_times()
    DAE.add_snapshots(snapshots=inputs, index=index, times=times)
    assert len(DAE.Dataset)==outputs

@pytest.mark.parametrize('inputs, site, ui, umin, umax, bin_size, outputs',
                         [([f_td('example_sd.coord')], (0,0,0), 0, -0.2, 0.2, 1, {(0,0,0): {'ux': [1]}}),
                          ([f_td('example_sd.coord')], (0,0,0), 0, -0.2, 0.2, 0.1, {(0,0,0): {'ux': [0,0,1,0,0]}}),
                          ([f_td('example_sd.coord')], (0,0,0), 2, -0.2, 0.2, 0.1, {(0,0,0): {'uz': [0,0,0,1,0]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (0,0,0), 0, -0.2, 0.2, 1, {(0,0,0): {'ux': [2]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (0,0,0), 0, -0.2, 0.2, 0.1, {(0,0,0): {'ux': [0,0,2,0,0]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (0,0,0), 2, -0.2, 0.2, 0.1, {(0,0,0): {'uz': [0,0,0,2,0]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (9,0,0), 2, -0.2, 0.2, 0.1, {(9,0,0): {'uz': [0,1,0,1,0]}}),
                         ])
def test_cal_p_prob(inputs, site, ui, umin, umax, bin_size, outputs):
    DAE = Dipo_Analysis_Evo(*inputs)
    DAE.load_data()
    DAE.set_times()
    sites = [site]
    DAE.select_sites(sites=sites)
    prob,_ = DAE.cal_p_prob(ui=ui, umin=umin, umax=umax, bin_size=bin_size, display=False)
    assert prob==outputs

@pytest.mark.parametrize('inputs, site, temp, ui, umin, umax, bin_size, outputs',
                         [([f_td('example_sd.coord')], (0,0,0), 1, 0, -0.2, 0.2, 1, {(0,0,0): {'ux': [0]}}),
                          ([f_td('example_sd.coord')], (0,0,0), 1, 0, -0.2, 0.2, 0.1, {(0,0,0): {'ux': [0,0,0,0,0]}}),
                          ([f_td('example_sd.coord')], (0,0,0), 1, 2, -0.2, 0.2, 0.1, {(0,0,0): {'uz': [0,0,0,0,0]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (0,0,0), 1, 0, -0.2, 0.2, 1, {(0,0,0): {'ux': [-np.log(2)*kb]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (0,0,0), 1, 0, -0.2, 0.2, 0.1, {(0,0,0): {'ux': [0,0,-np.log(2)*kb,0,0]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (0,0,0), 1, 2, -0.2, 0.2, 0.1, {(0,0,0): {'uz': [0,0,0,-np.log(2)*kb,0]}}),
                          ([f_td('example_t180.dipoRavg'),f_td('example_sd.coord')], (9,0,0), 1, 2, -0.2, 0.2, 0.1, {(9,0,0): {'uz': [0,0,0,0,0]}}),
                         ])
def test_cal_free_energy(inputs, site, temp, ui, umin, umax, bin_size, outputs):
    DAE = Dipo_Analysis_Evo(*inputs)
    DAE.load_data()
    DAE.set_times()
    sites = [site]
    DAE.select_sites(sites=sites)
    feng,_ = DAE.cal_free_energy(temperature=temp, ui=ui, umin=umin, umax=umax, bin_size=bin_size, f_eps=0)
    assert feng==outputs
