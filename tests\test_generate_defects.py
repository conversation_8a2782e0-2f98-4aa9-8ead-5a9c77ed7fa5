import os
import pytest
# user-defined modules
from generate_defects import Defects

test_dir = 'tests/'
f_td = lambda x: os.path.join(test_dir, x)

@pytest.mark.parametrize('inputs, outputs',
                         [(None, 0),
                          ((1,1,1), 1),
                          ((2,2,2), 8),
                          ((3,3,3), 27),
                         ])
def test_initial_sites(inputs, outputs):
    D = Defects(size=inputs)
    assert D.sites == outputs

@pytest.mark.parametrize('inputs, i, outputs',
                         [(None, 0, list(range(10))),
                          (range(3), 0, list(range(3))),
                          (range(4), 1, list(range(4))),
                          (range(5), 2, list(range(5))),
                          (range(6,12), 0, [6,7,8,9]),
                          ([-1,2,3,4], 0, [2,3,4]),
                         ])
def test_initial_sites(inputs, i, outputs):
    D = Defects(size=(10,10,10))
    _list = [range(10) for _ in range(3)]
    _list[i] = inputs
    x, y, z = _list
    D.select_data(x_list=x, y_list=y, z_list=z)
    assert list(D.Slice[i]) == list(outputs)

@pytest.mark.parametrize('axis, t, outputs',
                         [(0, None, 10),
                          (1, None, 10),
                          (2, None, 10),
                          (0, 5, 5),
                          (0, range(1,6), 5),
                          (0, (1,3,5,7), 4),
                         ])
def test_line_df_len(axis, t, outputs):
    D = Defects(size=(10,10,10))
    df = D.line_df(axis=axis, t=t)
    assert len(df) == outputs

@pytest.mark.parametrize('axis, t, offset, outputs',
                         [(0, None, None, (9,0,0)),
                          (0, None, 'middle', (9,5,5)),
                          (1, None, None, (0,9,0)),
                          (1, None, 'middle', (5,9,5)),
                          (1, None, (1,0,1), (1,9,1)),
                          (1, None, (1,1,1), (1,9,1)),
                          (2, None, None, (0,0,9)),
                          (2, None, 'middle', (5,5,9)),
                          (0, 5, 'middle', (4,5,5)),
                          (0, range(1,6), 'middle', (5,5,5)),
                          (0, (1,3,5,7), 'middle', (7,5,5)),
                         ])
def test_line_df_last_line(axis, t, offset, outputs):
    D = Defects(size=(10,10,10))
    df = D.line_df(axis=axis, t=t, offset=offset)
    assert df[-1] == '{} {} {} 0.00 0.00 0.00\n'.format(*outputs)

@pytest.mark.parametrize('axis, t, w, outputs',
                         [(0, None, 1, 10),
                          (1, None, 1, 10),
                          (2, None, 1, 10),
                          (0, 5, 1, 5),
                          (0, range(1,6), 1, 5),
                          (0, (1,3,5,7), 1, 4),
                          (0, None, 2, 20),
                          (1, None, 2, 20),
                          (2, None, 2, 20),
                          (0, 5, 2, 10),
                          (0, range(1,6), 2, 10),
                          (0, (1,3,5,7), 2, 8),
                         ])
def test_linew_df_len(axis, t, w, outputs):
    D = Defects(size=(10,10,10))
    df = D.linew_df(axis=axis, t=t, norm=2, width=w)
    assert len(df) == outputs

@pytest.mark.parametrize('axis, t, n, w, offset, outputs',
                         [(0, None, 2, 2, 'middle', (9,6,5)),
                          (0, None, 1, 2, 'middle', (9,5,6)),
                          (1, None, 0, 2, 'middle', (5,9,6)),
                          (1, None, 2, 2, 'middle', (6,9,5)),
                          (2, None, 0, 2, 'middle', (5,6,9)),
                          (2, None, 1, 2, 'middle', (6,5,9)),
                          (0, 5, 2, 2, 'middle', (4,6,5)),
                          (0, 5, 2, 3, 'middle', (4,7,5)),
                          (0, range(1,6), 2, 2, 'middle', (5,6,5)),
                          (0, range(1,6), 2, 4, 'middle', (5,8,5)),
                          (0, (1,3,5,7), 2, 2, 'middle', (7,6,5)),
                          (0, (1,3,5,7), 2, 5, 'middle', (7,9,5)),
                          (0, None, 2, 2, None, (9,1,0)),
                          (0, None, 1, 2, None, (9,0,1)),
                          (1, None, 0, 2, None, (0,9,1)),
                          (1, None, 2, 2, None, (1,9,0)),
                          (2, None, 0, 2, None, (0,1,9)),
                          (2, None, 1, 2, None, (1,0,9)),
                          (0, 5, 2, 2, None, (4,1,0)),
                          (0, 5, 2, 3, None, (4,2,0)),
                          (0, range(1,6), 2, 2, None, (5,1,0)),
                          (0, range(1,6), 2, 4, None, (5,3,0)),
                          (0, (1,3,5,7), 2, 2, None, (7,1,0)),
                          (0, (1,3,5,7), 2, 5, None, (7,4,0)),
                         ])
def test_linew_df_last_line(axis, t, n, w, offset, outputs):
    D = Defects(size=(10,10,10))
    df = D.linew_df(axis=axis, t=t, norm=n, width=w, offset=offset)
    assert df[-1] == '{} {} {} 0.00 0.00 0.00\n'.format(*outputs)

@pytest.mark.parametrize('n, r, outputs',
                         [(2, 1, 100),
                          (1, 1, 100),
                          (0, 1, 100),
                          (2, 0.5, 50),
                          (2, 0.1, 10),
                         ])
def test_plane_df_len(n, r, outputs):
    D = Defects(size=(10,10,10))
    df = D.plane_df(normal=n, ratio=r)
    assert len(df) == outputs

@pytest.mark.parametrize('n, r, xrng, yrng, zrng, outputs',
                         [(2, 1, range(3), None, None, 30),
                          (1, 1, None, None, range(4), 40),
                          (0, 1, None, range(5), None, 50),
                          (2, 0.5, range(5), None, None, 25),
                          (2, 0.1, range(5,10), None, None, 5),
                         ])
def test_plane_df_select_data_len(n, r, xrng, yrng, zrng, outputs):
    D = Defects(size=(10,10,10))
    D.select_data(x_list=xrng, y_list=yrng, z_list=zrng)
    df = D.plane_df(normal=n, ratio=r)
    print(D.Slice)
    assert len(df) == outputs

@pytest.mark.parametrize('n, o, r, outputs',
                         [(2, None, 1, (9,9,0)),
                          (1, None, 1, (9,0,9)),
                          (0, None, 1, (0,9,9)),
                          (2, None, 0.5, (9,9,0)),
                          (2, None, 0.1, (4,9,0)),
                         ])
def test_plane_df_last_line(n, o, r, outputs):
    D = Defects(size=(10,10,10))
    df = D.plane_df(normal=n, o=o, ratio=r)
    assert df[-1] == '{} {} {} 0.00 0.00 0.00\n'.format(*outputs)

@pytest.mark.parametrize('n, o, r, xrng, yrng, zrng, outputs',
                         [(2, None, 1, range(3), None, None, (2,9,0)),
                          (1, None, 1, None, None, range(4), (9,0,3)),
                          (0, None, 1, None, range(5), None, (0,4,9)),
                          (2, None, 0.5, [0], None, None, (0,8,0)),
                          (2, None, 0.1, [1], None, None, (1,8,0)),
                          (2, None, 1, [2], range(3), None, (2,2,0)),
                         ])
def test_plane_df_select_data_last_line(n, o, r, xrng, yrng, zrng, outputs):
    D = Defects(size=(10,10,10))
    D.select_data(x_list=xrng, y_list=yrng, z_list=zrng)
    df = D.plane_df(normal=n, o=o, ratio=r)
    assert df[-1] == '{} {} {} 0.00 0.00 0.00\n'.format(*outputs)
