image: continuumio/miniconda3:latest

stages:
  - test
  - build
  - pages

variables:
  CONDA_ENV: "py38"

before_script:
  - echo $PATH
  - |
    if [[ ! -d ${CONDA_PREFIX}/envs/${CONDA_ENV} ]]; then
      conda env create --file environment.yml
    fi
  - source ~/.bashrc
  - conda activate py38
  - pip install -r requirements.txt
  - echo $PATH

test:
  stage: test
  script:
    - python -m pytest

build:
  stage: build
  script:
    - mkdir build/
    - sphinx-build -M html source/ build/
  artifacts:
    paths:
      - build/

pages:
  stage: pages
  script:
    - mkdir public/
    - mv build/html/* public/
  artifacts:
    paths:
      - public/
  only:
    - main
