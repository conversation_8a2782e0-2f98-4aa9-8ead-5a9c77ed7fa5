import os
import sys
import helper
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src/')))

test_dir = 'tests/'
f_td = lambda x: os.path.join(test_dir, x)

H = helper.Helper()

H.create_mod(L=10, r_Ba=0.7, seed=0, f_out=f_td('example.modulation'), overwrite=False)

H.create_domains(L=2, mode='sd', fmt='coord', f_out=f_td('example_sd_L2.coord'), overwrite=False)
H.create_domains(L=10, mode='sd', fmt='coord', f_out=f_td('example_sd.coord'), overwrite=False)
H.create_domains(L=10, mode='sd', fmt='dipoRavg', f_out=f_td('example_sd.dipoRavg'), overwrite=False)
H.create_domains(L=10, mode='t180', rand=True, fmt='dipoRavg', f_out=f_td('example_t180.dipoRavg'), overwrite=True)
H.generate_defects(L=10, n=50, mode='stripe', f_out=f_td('example.defects'), overwrite=True, display=False)

H.create_hl(mode=0, f_out=f_td('example.hl'))
H.create_hl(mode=1, f_out=f_td('example_1.hl'))
H.create_hl(mode=2, f_out=f_td('example_2.hl'))
H.create_hl(mode=3, f_out=f_td('example_3.hl'))
H.create_hl(mode=4, f_out=f_td('example_4.hl'))
H.create_hl(mode=5, f_out=f_td('example_5.hl'))

H.create_dump(mode=0, f_out=f_td('dump_example_0'))
H.create_dump(mode=1, f_out=f_td('dump_example_1'))
H.create_dump(mode=2, f_out=f_td('dump_example_2'))
