.PHONY: man, set, csv, plot, all, clean, clean-csv, clean-all
export PATH := $(HOME)/Documents/anaconda3/envs/py38/bin/:$(PATH)
export PYTHONPATH := $(HOME)/storage/gitlab_rub/fedas/src/:$(PYTHONPATH)

FIG = figures/check_csv figures/check_evo $(wildcard figures/*.png) $(wildcard figures/*.pdf)

man:
	@echo "# DW Extraction for Feram simulations"
	@echo "  [Usage]: 'make [target] -f [Makefile]'"
	@echo "  - make set  : Set-up parameters for the scripts"
	@echo "  - make csv  : Create summary csv"
	@echo "  - make plot : Plot csv, evo"
	@echo "  - make all  : All"
	@echo "  - make clean: Clean main artifacts"

set: params
	@echo ">> set parameters"
	@grep "^kernel = " generate_DWL_dump.sh write_sum_csv.py
	@echo "---"
	sed -i "s/^kernel = .*/$$(grep 'kernel =' $<)/g" generate_DWL_dump.sh
	sed -i "s/^kernel = .*/$$(grep 'kernel =' $<)/g" write_sum_csv.py
	sed -i "s/^kernel = .*/$$(grep 'kernel =' $<)/g" plot_csv.py
	@echo "+++"
	@grep "^kernel = " generate_DWL_dump.sh write_sum_csv.py

DW_summary.csv: write_sum_csv.py
	@echo ">> create summary csv"
	python3 write_sum_csv.py

csv: DW_summary.csv

figures/check_csv: DW_summary.csv plot_csv.py
	@echo ">> Plotting $@"
	python3 plot_csv.py $<
	touch figures/check_csv

figures/check_evo: plot_evo.py
	@echo ">> Plotting $@"
	python3 plot_evo.py
	wait
	touch figures/check_evo

plot: $(FIG)

all: set csv plot
	@echo ">> set params + create csv + plot"

clean:
	rm -f figures/*

clean-csv:
	rm -f DW_summary.csv

clean-all:
	rm -f DW_summary.csv figures/*
