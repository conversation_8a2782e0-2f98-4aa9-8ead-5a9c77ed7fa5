import os, glob
import numpy as np

class Defects():
    """ Creating .defects file for Feram """
    def __init__(self, fname='new.defects', size=None):
        self.fname = fname
        if size is not None:
            self.xs=size[0]
            self.ys=size[1]
            self.zs=size[2]
        else:
            self.xs = self.ys = self.zs = 0
        self.sites = self.xs*self.ys*self.zs
        self.Slice = None

    def select_data(self, x_list=None, y_list=None, z_list=None):
        """ Selecting data for a given data range

        Parameters
        ----------
        x_list/y_list/z_list: list/tuple/range/np.ndarray
            the range/list of x/y/z coordinates

        Returns
        -------
        None
        """
        Size = [self.xs, self.ys, self.zs]
        _List = [None for _ in range(3)]
        for i,_list in enumerate([x_list, y_list, z_list]):
            if isinstance(_list, (list, tuple, range, np.ndarray)):
                _list_chk = []
                for val in _list:
                    if val>=0 and val<Size[i]: # check data range
                        _list_chk.append(val)
                _List[i] = _list_chk
            elif _list is None:
                _List[i] = range(Size[i])
            else:
                print('[Error]: Unkown type for {}_list!'.format('xyz'[i]))
        self.Slice = _List

    def unselect_data(self):
        """ Unselecting data """
        self.Slice = None

    def line_df(self, axis=1, t=None, fx=None, fy=None, fz=None, offset='middle',
                defects=(0.0, 0.0, 0.0)):
        """ A parametric line defect

        Parameters
        ----------
        axis: int
            direction of the line (0:x, [1]:y, 2:z)
        t: int/list/range/np.ndarray
           parameters for the x,y,z components of the line
        fx/fy/fz: function
            parametric equation for x/y/z component
        offset: str or int or tuple/list(3)
            the offset (dx,dy,dz) to apply along x,y,z; ['middle']: to the middle of the box
        defects: tuple/list
            vector of the defect dipole

        Returns
        -------
        text: list
            list of strings containing coordinates and defects for *.defects file
        """
        text = []
        # parametric equation as a function of t
        if t is None:
            ts = range(self.ys)
        elif isinstance(t, int):
            ts = range(t)
        elif isinstance(t, (list, tuple, range, np.ndarray)):
            ts = t
        else:
            print('[Error]: Unkown type for t!')
            return text
        # offset
        if offset=='middle':
            shift = [int(_/2) for _ in (self.xs, self.ys, self.zs)]
            shift[axis] = 0
        elif isinstance(offset, int):
            shift = [offset for _ in range(3)]
            shift[axis] = 0
        elif isinstance(offset, (list, tuple)):
            if len(offset)==3:
                shift = list(offset)
            else:
                print('[Error]: Incorrect length for offset ({}!=3)!'.format(len(offset)))
                return text
            if shift[axis]!=0:
                print('[Warning]: offset should be 0 along `axis`!')
            shift[axis] = 0
        else:
            shift = [0, 0, 0]
        # direction
        if fx is None and fy is None and fz is None:
            faxis = [lambda t: 0 for _ in range(3)]
            faxis[axis] = lambda t: t
        else:
            faxis = [fx, fy, fz]
        _fx, _fy, _fz = faxis
        for _t in ts:
            x, y, z = _fx(_t)+shift[0], _fy(_t)+shift[1], _fz(_t)+shift[2]
            text.append("%d %d %d"%(x,y,z)+" %.2f %.2f %.2f\n"%(defects))
        return text

    def linew_df(self, axis=1, norm=2, t=None, width=1, offset='middle', 
                 defects=(0.0, 0.0, 0.0)):
        """ A Straight line with a width

        Parameters
        ----------
        axis: int
            direction of the line (0:x, [1]:y, 2:z)
        norm: int
            normal of (axis x width)
        t: int/list/range/np.ndarray
           index for the line along `axis`
        width: int
            width of line
        offset: str or int or tuple/list(3)
            the offset (dx,dy,dz) to apply along x,y,z; ['middle']: to the middle of the box
        defects: tuple/list
            vector of the defect dipole

        Returns
        -------
        text: list
            list of strings containing coordinates and defects for *.defects file
        """
        text = []
        # width
        if isinstance(width, int):
            if width<1:
                print('[ERROR]: width must >=1')
                return text
        else:
            print('[Error]: Incorrect type for width!')
            return text
        # offset
        if offset=='middle':
            shift = [int(_/2) for _ in (self.xs, self.ys, self.zs)]
            shift[axis] = 0
        elif isinstance(offset, int):
            shift = [offset for _ in range(3)]
            shift[axis] = 0
        elif isinstance(offset, (list, tuple)):
            if len(offset)==3:
                shift = list(offset)
            else:
                print('[Error]: Incorrect length for offset ({}!=3)!'.format(len(offset)))
                return text
            if shift[axis]!=0:
                print('[Warning]: offset should be 0 along `axis`!')
            shift[axis] = 0
        else:
            shift = [0, 0, 0]
        for s in range(width):
            _shift = [_+s if i not in (axis,norm) else _ for i,_ in enumerate(shift)]
            text += self.line_df(axis=axis, t=t, offset=_shift, defects=defects)
        return text

    def plane_df(self, normal=2, o=None, ratio=1, defects=(0.0, 0.0, 0.0), seed=0):
        if self.Slice is not None: # if select_data
            Size = [len(_) for _ in self.Slice]
            sites_tot = Size[0]*Size[1]*Size[2]
            Index = self.Slice
        else:
            Size = [self.xs, self.ys, self.zs]
            sites_tot = self.sites
            Index = [range(_) for _ in Size]
        # plane normal
        Plane = {0:[], 1:[], 2:[]}
        if o is None:
            Plane[normal]=[0]
        else:
            Plane[normal]=[o]
        # sites
        sites = sites_tot//Size[normal]
        atom=[1 for _ in range(int(sites*ratio))]+[0 for _ in range(sites-int(sites*ratio))]
        np.random.seed(seed)
        np.random.shuffle(atom)
        _i=0
        text=[]
        for _z in Index[2]:
            for _y in Index[1]:
                for _x in Index[0]:
                    if _x in Plane[0] or _y in Plane[1] or _z in Plane[2]:
                        if atom[_i]==1:
                            text.append("%d %d %d"%(_x,_y,_z)+" %.2f %.2f %.2f\n"%defects)
                        _i+=1
        return text

    def multi_plane_df(self, plane_norm=(1,0,0), hspacing=1, offset=(0,0,0), defects=(0,0,0)):
        xoff, yoff, zoff = offset
        f_p100 = lambda i,j,k: 1*sum([(i==xoff+hspacing*n) for n in range(self.xs//hspacing)])
        f_p110 = lambda i,j,k: 1*sum([(i-xoff+j-yoff==hspacing*n) for n in range(2*self.xs//hspacing)])
        Plane = {(1,0,0): f_p100, (1,1,0): f_p110}
        f_plane = Plane[plane_norm]
        text=[]
        for _z in range(self.zs):
            for _y in range(self.ys):
                for _x in range(self.xs):
                    if f_plane(_x,_y,_z):
                        text.append("%d %d %d"%(_x,_y,_z)+" %.2f %.2f %.2f\n"%defects)
        return text

    def donut_df(self, z=None, c=(0,0), radius=0, thickness=1, n_sites=None, defects=(0,0,0), seed=None):
        np.random.seed(seed)
        r_circle = lambda _x,_y: 1 if np.sqrt(np.sum(np.square([_x-c[0], _y-c[1]])))<radius else 0
        xm, ym = np.meshgrid(np.arange(self.xs), np.arange(self.ys))
        index = np.arange(self.xs*self.ys)
        text=[]
        for _z in range(z, z+thickness):
            np.random.shuffle(index)
            for i,ind in enumerate(index):
                if len(text)==n_sites:
                   break
                _x, _y = xm.ravel()[ind], ym.ravel()[ind]
                if not r_circle(_x,_y):
                    text.append("%d %d %d"%(_x,_y,_z)+" %.2f %.2f %.2f\n"%defects)
                    if i+1 > n_sites/thickness:
                        break
        return text

    def cluster_df(self, c=None, r=1, defects=(0.0, 0.0, 0.0)):
        if c==None:
            c=(self.xs-r)/2, (self.ys-r)/2, (self.zs-r)/2
        text=[]
        for _z in range(self.zs):
            for _y in range(self.ys):
                for _x in range(self.xs):
                    _v=np.array([_x, _y, _z])-np.array(c)
                    radius=np.sqrt(np.sum(np.square(_v)))
                    if radius<=r:
                        text.append("%d %d %d"%(_x,_y,_z)+" %.2f %.2f %.2f\n"%defects)
        return text

    def cubic_df(self, o=(0,0,0), a=1, defects=(0.0, 0.0, 0.0)):
        if o==None:
            o=(self.xs-a)/2, (self.ys-a)/2, (self.zs-a)/2
        f_in=lambda x,y,z: (x>=o[0])*(y>=o[1])*(z>=o[2])*(x<(o[0]+a))*(y<(o[1]+a))*(z<(o[2]+a))
        text=[]
        for _z in range(self.zs):
            for _y in range(self.ys):
                for _x in range(self.xs):
                    if f_in(_x,_y,_z):
                        text.append("%d %d %d"%(_x,_y,_z)+" %.2f %.2f %.2f\n"%defects)
        return text

    def dec_rand_defects(f):
        def wrapper(self, mode=None, *args, **kwargs):
            seed = kwargs['seed']
            percent = kwargs['percent']
            xlim, ylim, zlim = (kwargs[key] for key in ['xlim', 'ylim', 'zlim'])
            xlo, xhi = (0, self.xs) if xlim==None else (xlim[0], xlim[1])
            ylo, yhi = (0, self.ys) if ylim==None else (ylim[0], ylim[1])
            zlo, zhi = (0, self.zs) if zlim==None else (zlim[0], zlim[1])
            ntot=(xhi-xlo)*(yhi-ylo)*(zhi-zlo)
            nsites=int(ntot*percent*0.01)
            np.random.seed(seed)
            if mode==None:
                return f(self, *args, **kwargs)
            elif mode=='uniform':
                def uniform(self, ux_min=-0.1, ux_max=0.1, uy_min=-0.1, uy_max=0.1, uz_min=0.0, uz_max=0.1, 
                            *args, **kwargs):
                    ux_ary = np.random.uniform(ux_min, ux_max, nsites)
                    uy_ary = np.random.uniform(uy_min, uy_max, nsites)
                    uz_ary = np.random.uniform(uz_min, uz_max, nsites)
                    defects = np.stack((ux_ary, uy_ary, uz_ary), axis=-1).tolist()
                    return f(self, defects=defects, *args, **kwargs)
                return uniform(self, *args, **kwargs)
            elif mode=='normal':
                def normal(self, mux=0.0, sigmax=0.05, muy=0.0, sigmay=0.05, muz=0.0, sigmaz=0.05, 
                           *args, **kwargs):
                    ux_ary = np.random.normal(mux, sigmax, nsites)
                    uy_ary = np.random.normal(muy, sigmay, nsites)
                    uz_ary = np.random.normal(muz, sigmaz, nsites)
                    defects = np.stack((ux_ary, uy_ary, uz_ary), axis=-1).tolist()
                    return f(self, defects=defects, *args, **kwargs)
                return normal(self, *args, **kwargs)
            elif mode=='multiple': # 2 types in 1 doomain
                def multiple(self, subdiv=(0.5, 0.5), defect1=(0.1,0.0,0.0), defect2=(-0.1,0.0,0.0),
                             *args, **kwargs):
                    index=np.arange(ntot)
                    np.random.shuffle(index)
                    n0=int(nsites*subdiv[0])
                    n1=nsites-n0
                    irand=[]
                    for _ in range(ntot):
                        if _ in index[::2][:n0]:
                            irand.append(1)
                        elif _ in index[1::2][:n1]:
                            irand.append(2)
                        else:
                            irand.append(0)
                    defects=[]
                    for t in irand:
                        if t==1:
                            defects.append(defect1)
                        elif t==2:
                            defects.append(defect2)
                    return f(self, defects=defects, *args, **kwargs)
                return multiple(self, *args, **kwargs)
        return wrapper

    @dec_rand_defects
    def random_df(self, percent=0, defects=(0.0,0.0,0.0), seed=0, xlim=None, ylim=None, zlim=None):
        xlo, xhi = (0, self.xs) if xlim==None else (xlim[0], xlim[1])
        ylo, yhi = (0, self.ys) if ylim==None else (ylim[0], ylim[1])
        zlo, zhi = (0, self.zs) if zlim==None else (zlim[0], zlim[1])
        ntot=(xhi-xlo)*(yhi-ylo)*(zhi-zlo)
        nsites=int(ntot*percent*0.01)
        index=np.arange(ntot)
        print(ntot, nsites)
        np.random.seed(seed)
        np.random.shuffle(index)
        irand=[1 if _ in index[:nsites] else 0 for _ in range(ntot)]
        text=[]
        if isinstance(defects, tuple):
            defects = [defects for _ in range(nsites)]
        elif isinstance(defects, list) and len(defects)!=nsites:
            print('Error: inconsistent number of defect sites')
            return text
        for _z in range(zlo, zhi):
            for _y in range(ylo, yhi):
                for _x in range(xlo, xhi):
                    if irand.pop():
                        text.append("%d %d %d"%(_x,_y,_z)+" %.2g %.2g %.2g\n"%tuple(defects.pop()))
        return text

    def write(self, defects=None):
        if defects==None:
            return
        with open(self.fname, 'w') as fw:
            for _df in defects:
                fw.writelines(_df)

if __name__ == '__main__':
    fname = 'new.defects'
    size=(48,48,48)
    D=Defects(size=size)
    df_dist = dict(mux=0.01, sigmax=0.00)
    rnd = D.random_df(mode='normal', percent=1, seed=0, xlim=None, ylim=None, zlim=None, **df_dist)
    defects_all = [rnd]
    with open(fname, 'w') as fw:
        for _df in defects_all:
            fw.writelines(_df)
    os.system('more %s'%fname)    
