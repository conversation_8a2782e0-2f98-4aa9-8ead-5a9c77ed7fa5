import os
import numpy as np
import shutil
import subprocess as spb
import glob
from collections import OrderedDict
# user-defined modules
from utilities_feram import Modulation, write_feram, run_feram
from generate_defects import Defects
from create_domains import DomainWall
from analyze_feram import Dipo_Analysis
from feram_converter import feram2dump
from domain_rectangle import DomainWalls

# feram input
input_feram = lambda verbose = 2, method = 'md', Q_Nose = 0.1, GPa = 0, kelvin = 300, bulk_or_film = 'bulk', \
                     L0 = 12, L1 = 12, L2 = 12, \
                     dt = 0.002, n_thermalize = 40000, n_average = 20000, \
                     n_hl_freq = 5000, n_coord_freq = 60000, \
                     coord_directory = 'never', slice_directory = 'never', distribution_directory = 'never', \
                     external_E_field0 = 0.0, external_E_field1 = 0.0, external_E_field2 = 0.0, \
                     init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0, \
                     init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03: \
"""# Parameters for BaTiO3 effective Hamiltonian
# from https://journals.aps.org/prb/abstract/10.1103/PhysRevB.82.134106
#--- Method, Temperature, and mass ---------------
verbose = {verbose}
method = '{method}'
Q_Nose = {Q_Nose}
GPa = {GPa}
kelvin = {kelvin}
mass_amu = 38.24
# acoustic_mass_amu = 41.67

#--- System geometry -----------------------------
bulk_or_film = '{bulk_or_film}'
L = {L0} {L1} {L2}

#--- Elastic Constants ---------------------------
B11 = 126.731671475652
B12 = 41.7582963902598
B44 = 49.2408864348646

#--- From perovskite-optcell2-p.gp ---------------
B1xx = -185.347187551195 [eV/Angstrom^2]
B1yy = -3.28092949275457 [eV/Angstrom^2]
B4yz = -14.5501738943852 [eV/Angstrom^2]
P_k1 = -267.98013991724 [eV/Angstrom^6]
P_k2 = 197.500718362573 [eV/Angstrom^6]
P_k3 = 830.199979293529 [eV/Angstrom^6]
P_k4 = 641.968099408642 [eV/Angstrom^8]
P_alpha = 78.9866142426818 [eV/Angstrom^4]
P_gamma = -115.484148812672 [eV/Angstrom^4]

#--- Time step -----------------------------------
dt = {dt} [pico second]
n_thermalize = {n_thermalize}
n_average = {n_average}
n_hl_freq = {n_hl_freq}
n_coord_freq = {n_coord_freq}
coord_directory = '{coord_directory}'
slice_directory = '{slice_directory}'
distribution_directory = '{distribution_directory}'

#--- External electric field ---------------------
external_E_field = {external_E_field0} {external_E_field1} {external_E_field2}

#--- From eigenvalues2j --------------------------
P_kappa2 = 8.53400622096412 [eV/Angstrom^2]
j = -2.08403 -1.12904  0.68946 -0.61134  0.00000  0.27690  0.00000  [eV/Angstrom^2]
a0 = 3.98597 [Angstrom]
Z_star = 10.33000
epsilon_inf = 6.86915

#--- Initial dipole configrations ----------------
init_dipo_avg = {init_dipo_avg0} {init_dipo_avg1} {init_dipo_avg2} [Angstrom]
init_dipo_dev = {init_dipo_dev0} {init_dipo_dev1} {init_dipo_dev2} [Angstrom]
""".format(verbose=verbose, method=method, Q_Nose=Q_Nose, GPa=GPa, kelvin=kelvin, bulk_or_film=bulk_or_film, 
           L0=L0, L1=L1, L2=L2,
           dt=dt, n_thermalize=n_thermalize, n_average=n_average, n_hl_freq=n_hl_freq, n_coord_freq=n_coord_freq, 
           coord_directory=coord_directory, slice_directory=slice_directory,
           distribution_directory=distribution_directory, external_E_field0=external_E_field0,
           external_E_field1=external_E_field1, external_E_field2=external_E_field2,
           init_dipo_avg0=init_dipo_avg0, init_dipo_avg1=init_dipo_avg1, init_dipo_avg2=init_dipo_avg2,
           init_dipo_dev0=init_dipo_dev0, init_dipo_dev1=init_dipo_dev1, init_dipo_dev2=init_dipo_dev2)

# workflow
class WorkChain_DWs_bulk():
    def __init__(self, PATH='./', FILE='bto', xsize=164, ysize=48, zsize=48, bulk_or_film='bulk',
                 seed=1000, seed1=123456789, ncore=20, tag=0, worklog='worklog'):
        self.PATH = PATH
        self.FILE = FILE
        self.xsize = xsize
        self.ysize = ysize
        self.zsize = zsize
        self.bulk_or_film = bulk_or_film
        self.seed = seed
        self.seed1 = seed1
        self.ncore = ncore
        self.tag = tag
        self.worklog = worklog
        self.epi_strain = [0, 0]
        self.external_E_field = [0, 0, 0]
        self.exe = 'feram'

    # calculation functions
    def cf_set_exe(self, exe='feram'):
        self.exe = exe

    def cf_set_seed1(self, seed1=123456789):
        self.seed1 = seed1

    def cf_set_tag(self, tag=0):
        self.tag = tag

    def cf_set_bulk_or_film(self, tag='bulk'):
        self.bulk_or_film = tag

    def cf_set_epi_strain(self, exx=0, eyy=0):
        self.epi_strain = [exx, eyy]

    def cf_set_external_E_field(self, efx=0, efy=0, efz=0):
        self.external_E_field = [efx, efy, efz]

    def cf_create_domains(self, wall='T90', temp=260, field_stages=[100,]):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        bulk_or_film = self.bulk_or_film
        exx, eyy = self.epi_strain
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        exe = self.exe
        for ef in field_stages:
            # create domains
            DW = DomainWalls(wall=wall, size=(xsize, ysize, zsize))
            loc_ef = 0.00001*(ef) # (kV/cm)
            DW.create_domains(f_name=FILE, f_ext='localfield', scale=loc_ef)
            
            # initialization
            text = input_feram(verbose=2, method='md', Q_Nose=15, dt=0.001,
                               GPa=0, bulk_or_film=bulk_or_film,
                               kelvin=temp,
                               L0=xsize, L1=ysize, L2=zsize,
                               n_thermalize=20000,
                               n_average=10000,
                               n_hl_freq=30000,
                               n_coord_freq=30000,
                               coord_directory = './',
                               external_E_field0=0.0, external_E_field1=0.0, external_E_field2=0.0,
                               init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                               init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
            options = [('seed', '{} 987654321'.format(seed1), 10)]
            if bulk_or_film!='bulk':
                options+= [('epi_strain', '{} {}'.format(exx, eyy), 15)]
            write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
            run_feram(PATH=PATH, FILE=FILE, tag='init', header='', f_out='data.avg', cores=ncore, exe=exe)
    
        # move localfield to folder & backup
        os.system('mv {}.localfield data.avg logfile'.format(FILE))
        print('>>> stage: init finished!')
    
    def cf_run_thermalization(self, temp=260, dt=0.001, n_thermalize=20000, n_average=10000):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        bulk_or_film = self.bulk_or_film
        exx, eyy = self.epi_strain
        efx, efy, efz = self.external_E_field
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        exe = self.exe
        # thermalization w/o localfield
        n_total = n_thermalize + n_average
        text = input_feram(verbose=2, method='md', Q_Nose=15, dt=dt,
                           GPa=0, bulk_or_film=bulk_or_film,
                           kelvin=temp,
                           L0=xsize, L1=ysize, L2=zsize,
                           n_thermalize=n_thermalize,
                           n_average=n_average,
                           n_hl_freq=n_total,
                           n_coord_freq=n_total,
                           coord_directory = './',
                           external_E_field0=efx, external_E_field1=efy, external_E_field2=efz,
                           init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                           init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
        options = [('seed', '{} 987654321'.format(seed1), 10)]
        if bulk_or_film!='bulk':
            options+= [('epi_strain', '{} {}'.format(exx, eyy), 15)]
        write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
        run_feram(PATH=PATH, FILE=FILE, tag='therm', header='', f_out='data.avg', cores=ncore, exe=exe)
    
        # backup
        os.system('mv {}.feram data.avg logfile'.format(FILE))
        print('>>> stage: therm finished!')

    def cf_run_temp_ramping(self, T0=400, T1=50, dT=5, n_T_freq=50, n_thermalize=40000, n_average=20000):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        bulk_or_film = self.bulk_or_film
        exx, eyy = self.epi_strain
        efx, efy, efz = self.external_E_field
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        exe = self.exe
        # thermal ramping
        nT = np.abs(T1-T0)//dT+1
        label = 'heating' if T1>T0 else 'cooling'
        c = 1 if T1>T0 else -1
        p0 = 0.08 if T1>T0 else 0.0
        dp = 0.01 if T1>T0 else 0.03
        n_total = n_thermalize + n_average
        for i in range(nT):
            temp = T0 + i*c*dT
            Q_Nose = 50 if temp>=400 else 15
            text = input_feram(verbose=2, method='md', Q_Nose=Q_Nose, dt=0.001,
                               GPa=0, bulk_or_film=bulk_or_film,
                               kelvin=temp,
                               L0=xsize, L1=ysize, L2=zsize,
                               n_thermalize=n_thermalize,
                               n_average=n_average,
                               n_hl_freq=n_total,
                               n_coord_freq=n_total,
                               coord_directory='./',
                               external_E_field0=efx, external_E_field1=efy, external_E_field2=efz,
                               init_dipo_avg0 = p0, init_dipo_avg1 = p0, init_dipo_avg2 = p0,
                               init_dipo_dev0 = dp, init_dipo_dev1 = dp, init_dipo_dev2 = dp)
            options = [('seed', '{} 987654321'.format(seed1), 10)]
            if bulk_or_film!='bulk':
                options+= [('epi_strain', '{} {}'.format(exx, eyy), 15)]
            write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
            if temp%n_T_freq==0:
                run_feram(PATH=PATH, FILE=FILE, tag=str(temp), header='', f_out='data.avg', cores=ncore, exe=exe)
            else:
                run_feram(PATH=PATH, FILE=FILE, header='', f_out='data.avg', cores=ncore, exe=exe)
        # save files
        os.system('mv {}* data.avg logfile'.format(FILE))
        os.system('mv logfile logfile_{}'.format(label))
    
    def cf_generate_defects_T180DW(self, percent=0, dd_strength=0.1):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        # generate defects
        pdd = dd_strength
        ndd = -1*dd_strength
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        rnd1 = D.random_df(percent=percent, defects=(0.0,pdd,0.0), seed=seed, xlim=[0, xsize//4], ylim=None, zlim=None)
        rnd2 = D.random_df(percent=percent, defects=(0.0,ndd,0.0), seed=seed, xlim=[xsize//4, xsize*3//4], ylim=None, zlim=None)
        rnd3 = D.random_df(percent=percent, defects=(0.0,pdd,0.0), seed=seed, xlim=[xsize*3//4, xsize], ylim=None, zlim=None)
        D.write(defects=[rnd1, rnd2, rnd3]) 

    def cf_generate_n_defects(self, n=1, a=1, d=1, axis=2, dd_strength=0.1):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        # generate defects
        ndd = -1*dd_strength
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        defects = []
        if axis==1:
            y = ysize//2 - (n>1)*d//n - a//(n%2+1)
            for i in range(n):
                cb = D.cubic_df(o=(xsize//4+12,y,zsize//2), a=a, defects=(0.0, ndd, 0.0))
                defects.append(cb)
                y+=a+d
        elif axis==2:
            z = zsize//2 - (n>1)*d//n - a//(n%2+1)
            for i in range(n):
                cb = D.cubic_df(o=(xsize//4+12,ysize//2,z), a=a, defects=(0.0, ndd, 0.0))
                defects.append(cb)
                z+=a+d
        D.write(defects=defects)

    def cf_generate_layer_defects(self, normal=0, x_list=[0], y_list=None, z_list=None, ratio=0, dd_strength=0.1, seed=0):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        # generate defects
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        defects = []
        for i in range(ilo,ihi+1):
            D.select_data(x_list=x_list, y_list=y_list, z_list=z_list)
            d = D.plane_df(normal=normal, o=i, ratio=ratio, defects=(0.0, dd_strength, 0.0), seed=seed)
            D.unselect_data()
            defects.append(d)
        D.write(defects=defects)

    def cf_run_field_rampingz(self, ef_kVocm=100, temp=260, n_thermalize=150000, 
                             n_hl_freq=1000, n_coord_freq=15000):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        bulk_or_film = self.bulk_or_film
        exx, eyy = self.epi_strain
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        exe = self.exe
        if n_thermalize==0:
            os.system('mv {}.* data.avg logfile'.format(FILE))
            print('>>> stage: no ramping!')
            return
        # field ramping
        efield = 0.00001*(ef_kVocm) # kV/cm
        text = input_feram(verbose=1, method='md', Q_Nose=15, dt=0.001,
                           GPa=0, bulk_or_film=bulk_or_film,
                           kelvin=temp,
                           L0=xsize, L1=ysize, L2=zsize,
                           n_thermalize=n_thermalize,
                           n_average=0,
                           n_hl_freq=n_hl_freq,
                           n_coord_freq=n_coord_freq,
                           coord_directory = './',
                           external_E_field0=0.0, external_E_field1=0.0, external_E_field2=efield,
                           init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                           init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
        options = [('seed', '{} 987654321'.format(seed1), 10),
                   ('n_E_wave_period', 0, 37), ('E_wave_type', "'ramping_on'", 38)]
        if bulk_or_film!='bulk':
            options+= [('epi_strain', '{} {}'.format(exx, eyy), 15)]
        write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
        run_feram(PATH=PATH, FILE=FILE, tag='ramp', header='', f_out='data.avg', cores=ncore, exe=exe)
    
        os.system('rm -f {}.restart'.format(FILE))
        os.system('mv fort.12 {}.hl'.format(FILE))
        os.system('mv {}.* data.avg logfile'.format(FILE))
        print('>>> stage: ramp finished!')

    def cf_dw_analysis(self, norm=0, ui=1, xlim=(0,82), ylim=(0,48), zlim=(0,48), rmse_crit=0.01, 
                       log='dw_analysis', overwrite=False, keep_all=True):
        FILE = self.FILE
        tag = self.tag
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        bulk_or_film = self.bulk_or_film
        exx, eyy = self.epi_strain
        ymin, ymax = ylim
        zmin, zmax = zlim
        f_coords = ['logfile/therm.coord']+sorted(glob.glob('logfile/{}.0*.coord'.format(FILE)))
        RES=[]
        backup=0
        for i,f in enumerate(f_coords):
            print(f)
            DA = Dipo_Analysis(f)
            Dipos = DA.load_data()
            # quick check DW pos
            res_qk = DA.find_DW_pos(norm=norm, ui=ui, rmse_crit=0.01, mode='quick', display=False)
            xdws = res_qk[0]['x']['x0']
            wdws = res_qk[0]['x']['w0']
            if xlim==None:
                if isinstance(xdws, int) and xdws==-1:
                    xmin, xmax = 0, 162
                else:
                    xmed = (xdws[0]+xdws[-1])/2
                    xmin = xdws[0]-(xmed-xdws[0]) if len(xdws)>=2 else 0
                    xmax = xmed if len(xdws)>=2 else xdws[0]+wdws[0]
            else:
                xmin, xmax = xlim
            DA.select_data(i_list=range(int(xmin),int(xmax)), j_list=range(int(ymin),int(ymax)), k_list=range(int(zmin),int(zmax)))
            # fitting
            res = DA.find_DW_pos(norm=norm, ui=ui, rmse_crit=rmse_crit, display=False)
            if not res[0]['status']:
                backup+=1
            RES.append(res)
        rw = 'w' if overwrite else 'a+'
        with open(log, rw) as fw:
            fw.write(str({tag: RES})+'\n')
        # dump coord/defects
        feram2dump(path='./logfile/', ext='.coord', step=1, reverse=False, size=(xsize,ysize,zsize), f_mod=None)
        if len(glob.glob('*.defects'))!=0:
            feram2dump(path='./logfile/', ext='.defects', step=1, reverse=False, size=(xsize,ysize,zsize), f_mod=None)
        # check fitting status
        backup = 1 if keep_all else backup
        if backup==0:
            print('# fitting successful')
            os.system('rm -f logfile/*.coord logfile/*.dipoRavg')
        else:
            print('# tar files')
            os.system('tar -jcvf logfile/`date +"%Y%m%d"`_coord.tar.bz2 logfile/*.coord && rm -f logfile/*.coord')
        os.system('mv dump* logfile')
        os.system('mv logfile logfile_ramp_{}'.format(tag))

    def cf_dw_record(self, log_in='dw_analysis', log_out='dw_record', overwrite=True):
        with open(log_in, 'r') as fr:
            raw = fr.readlines()
        SUM=OrderedDict()
        for line in raw:
            data = eval(line)
            for i in data:
                X0 = [_[0]['x']['x0'] for _ in data[i]]
                W0 = [_[0]['x']['w0'] for _ in data[i]]
                dX0 = [x-X0[0] for x in X0]
                V0 = np.gradient(dX0).tolist() if len(dX0)>2 else 0
                SUM[i] = {'x0': X0, 'w0': W0, 'v0': V0}
        rw = 'w' if overwrite else 'a+'
        with open(log_out, rw) as fw:
            fw.write(f'{log_in}: '+str(SUM)+'\n')
        # tar files
        os.system(f'mkdir dir_{log_in}')
        os.system(f'mv logfile_ramp_* dir_{log_in}')

# helper functions
def multi_run(cls, outline, epi_strain=None, n=10):
    for i in range(n):
        cls.cf_set_exe(exe='feram')
        cls.cf_set_seed1(seed1=123456789+i)
        cls.cf_set_tag(tag=i)
        cls.cf_set_bulk_or_film(tag='bulk')
        if epi_strain!=None:
            cls.cf_set_epi_strain(exx=epi_strain[0], eyy=epi_strain[1])
        for func, param in outline:
            print('# function:', func.__name__)
            print('# parameters:', param)
            func(**param)

################
# main program
################
xsize = 36 
ysize = 36 
zsize = 36 
WC = WorkChain_DWs_bulk(PATH='./', FILE='bto',
                        xsize=xsize, ysize=ysize, zsize=zsize,
                        seed=1000, ncore=18)

for wall in ['T180_001', 'T180_011', 'T90',
             'O180_1-10', 'O180_001', 'O90', 'O120',
             'R180_1-10', 'R109', 'R71']: # wall type
    temp = (wall[0]=='T')*260+ (wall[0]=='O')*120 + (wall[0]=='R')*50 # temperature, K
    record = f'rec_{temp}K'
    os.system(f'mkdir {temp}K')
    for ef in [0.1, 0.5]: # E-fields, MV/cm
        efield = ef*1000 # kV/cm
        analysis = f'ana_dw{wall}_{efield}kVocm'
        outline = [(WC.cf_create_domains, dict(wall=wall, temp=temp, field_stages=[efield,]) ),
                   (WC.cf_run_thermalization, dict(temp=temp, dt=0.001, n_thermalize=60000, n_average=20000)),
                   (WC.cf_run_field_rampingz, dict(ef_kVocm=efield, temp=temp, n_thermalize=0,
                                                  n_hl_freq=1000, n_coord_freq=1000) ),
                   (WC.cf_dw_analysis, dict(norm=2, ui=0, xlim=(0,xsize), ylim=(0,ysize), zlim=(0,zsize), rmse_crit=0.01,
                                            log=analysis, overwrite=False))]
        multi_run(WC, outline, n=1)
        WC.cf_dw_record(log_in=analysis, log_out=record, overwrite=False)
    os.system(f'mv ana_dw* dir_ana_dw* {temp}K')
