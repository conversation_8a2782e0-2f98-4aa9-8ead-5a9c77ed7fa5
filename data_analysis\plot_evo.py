import os
import numpy as np
from utilities_feram import parse_data
from analyze_feram import Dipo_Analysis, DWPass_Collector
from Data import fdata
import matplotlib as mpl
import matplotlib.pyplot as plt

# main
ext = '.png'

# functions
def plot_layer_density(f_defects=None, iax=0, bins=1, yref=None, if_cmax=False, figsize=(6,4), fig_out=None):
    # parse data
    data = parse_data(f_defects)['xyz'[iax]]
    h = np.histogram(data, bins=bins)
    conc = (h[0]/48/48)*100
    # plot histogram
    fig, ax = plt.subplots(figsize=figsize, dpi=100)
    ax.bar(np.arange(len(h[0])), conc, width=1, color='b')
    if yref is not None:
        ax.axhline(y=yref,color='k', ls='--', lw=1)
    if if_cmax:
        # local maximal density
        i_cmax = np.argmax(h[0])
        ax.axvline(x=i_cmax, color='k', ls='--', lw=1)
        ax.text(i_cmax*1.1, np.max(conc)*0.98, f'x={i_cmax}', fontsize=16)
    ax.tick_params(which='major', labelsize=18)
    ax.set_xlim([-0.5, np.max(bins)])
    ax.set_xlabel('XYZ'[iax], fontsize=20)
    ax.set_ylabel(f'Density per {"xyz"[iax]}-layer (%)', fontsize=20)
    if fig_out!=None:
        fig.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
    else:
        plt.show()
    plt.close('all')

def plot_1L_hist(f_defects=None, iax=0, bins=1, if_rotate=False, figsize=None, fig_out=None):
    # parse data
    data = parse_data(f_defects)['xyz'[iax]]
    h = np.histogram(data, bins=bins)
    # plot histogram
    fig, ax = plt.subplots(figsize=figsize, dpi=100)
    if not if_rotate:
        ax.bar(np.arange(len(h[0])), h[0], width=1, color='b')
        ax.tick_params(which='major', labelsize=18)
        ax.set_xlim([-0.5, np.max(bins)])
        ax.set_xlabel('XYZ'[iax], fontsize=20)
        ax.set_ylabel(f'# of defects', fontsize=20)
    else:
        ax.barh(np.arange(len(h[0])), h[0], height=1, color='b')
        ax.tick_params(which='major', labelsize=18)
        ax.set_ylim([-0.5, np.max(bins)])
        ax.set_xlabel(f'# of defects', fontsize=20)
        ax.set_ylabel('XYZ'[iax], fontsize=20)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    if fig_out!=None:
        fig.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
    else:
        plt.show()
    plt.close('all')

# defects
## rand.3D
temp = 260
system = '3D_0.5%_s0_0'
f_defects = fdata[temp][system]+'bto.defects'
plot_layer_density(f_defects=f_defects, iax=0, bins=np.arange(165), yref=1, if_cmax=True,
                   figsize=(6,4), fig_out='defects_r3d.5p_x'+ext)
temp = 260
system = '3D_1%_s0_0'
f_defects = fdata[temp][system]+'bto.defects'
plot_layer_density(f_defects=f_defects, iax=0, bins=np.arange(165), yref=1, if_cmax=False,
                   figsize=(6,4), fig_out='defects_r3d1p_x'+ext)
temp = 260
system = '3D_2%_s0_0'
f_defects = fdata[temp][system]+'bto.defects'
plot_layer_density(f_defects=f_defects, iax=0, bins=np.arange(165), yref=2, if_cmax=False,
                   figsize=(6,4), fig_out='defects_r3d2p_x'+ext)
temp = 260
system = '1L_3.5%_s0_0'
f_defects = fdata[temp][system]+'bto.defects'
plot_1L_hist(f_defects=f_defects, iax=2, bins=np.arange(49), if_rotate=True,
             figsize=(4,5), fig_out='defects_1L3.5p_z'+ext)

# Snapshots
## pristine
temp = 240
for s in range(1):
    system = f'pt_f100_{s}'
    f_dump = fdata[temp][system]+'dump_feram_0.coord'
    f_defects = None
    for t in [0,1,2]:
        DA = Dipo_Analysis(f_dump)
        DA.load_data(is_dump=True, iframe=f'{t}:{t+1}')
        DA.plot_2d(norm=0, ind=41, c_axis='uy', spacing=10, if_sfc=True,
                   figsize=(5,5), fig_out=f'snap_pt_f100_{temp}K_{s}_yz_t{t}'+ext)
## 1L
temp = 260
system = '1L_3.5%_s0_0'
f_dump = fdata[temp][system]+'dump_feram_0.coord'
f_defects = fdata[temp][system]+'bto.defects'
kernel = (1,1,11) # (nz,ny,nx)
knl = np.ones(kernel)/11
key = {'uy': lambda u: 0.1 if u>=0 else -0.1, 'ux': lambda u: 0, 'uz': lambda u: 0}
div = 12
DA = Dipo_Analysis(f_dump)
DA.load_data(is_dump=True, iframe=':')
DA.plot_2d(norm=1, ind=0, c_axis='uy', spacing=24, if_sfc=True, fig_out='snap_1L3.5p_s0_0'+ext)
DA.select_data(i_list=list(range(82)))
DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
RGH = DA.cal_DW_roughness(mode='defects', f_app=f_defects, norm=0, div=div, 
                          show_sub='d', vmax=None, wlim=(-8,8), if_sfc=True,
                          display=True, figsize=None, fig_out='rgh_1L3.5p_s0_0'+ext)
# Wall lines
kernel = (1,1,11) # (nz,ny,nx)
knl = np.ones(kernel)/11
key = {'uy': lambda u: 0.1 if u>=0 else -0.1, 'ux': lambda u: 0, 'uz': lambda u: 0}
## pristine
field = 100 # kV/cm
for temp in [240, 260, 280]:
    for i in range(1):
        system = f'pt_f{field}_{i}'
        f_dump = fdata[temp][system]+'dump_feram_0.coord'
        DA = Dipo_Analysis(f_dump)
        DA.load_data(is_dump=True, iframe=':')
        DA.select_data(i_list=range(82))
        DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
        xlim = None if temp!=240 else (36, 54)
        WL = DA.plot_wall_line(norm=0, ui=1, xref=None, xlim=xlim, dt=10, if_sfc=True, i_arr=None,
                               figsize=(5,5), fig_out=f'wl_pt_f{field}_{temp}K_{i}'+ext)
## rand.3D
temp = 260
system = '3D_1%_s0_0'
f_dump = fdata[temp][system]+'dump_feram_0.coord'
f_defects = fdata[temp][system]+'bto.defects'
DA = Dipo_Analysis(f_dump)
DA.load_data(is_dump=True, iframe=':')
DA.select_data(i_list=list(range(82)))
DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
WL = DA.plot_wall_line(norm=0, ui=1, xref=None, xlim=[35,55], if_sfc=True, 
                       figsize=(5,5), fig_out='wl_r3d1p_s0_0'+ext)
## rand.3D 1% after field removal
temp = 260
system = '3D_1%_s0_0_f0_100ps'
f_dump = fdata[temp][system]+'dump_feram_0.coord'
f_defects = fdata[temp][system]+'bto.defects'
DA = Dipo_Analysis(f_dump)
DA.load_data(is_dump=True, iframe=':')
DA.select_data(i_list=list(range(82)))
DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
WL = DA.plot_wall_line(norm=0, ui=1, xref=None, xlim=[35,55], if_sfc=True, 
                       figsize=(5,5), fig_out='wl_r3d1p_s0_0_f0_100ps'+ext)
## 1L
temp = 260
for s in [0]: # s=0,1
    for i in range(1): # i=0,1,2,3,4,5
        system = f'1L_3.5%_s{s}_{i}'
        f_dump = fdata[temp][system]+'dump_feram_0.coord'
        DA = Dipo_Analysis(f_dump)
        DA.load_data(is_dump=True, iframe=':')
        DA.select_data(i_list=list(range(82)))
        DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
        WL = DA.plot_wall_line(norm=0, ui=1, xref=50, xlim=None, dt=10, if_sfc=True, i_arr=8,
                               figsize=(5,5), fig_out=f'wl_1L3.5p_s{s}_{i}'+ext)
### 1L 3.5% after field removal
temp = 260
system = '1L_3.5%_s0_0_f0_90ps'
f_dump = fdata[temp][system]+'dump_feram_0.coord'
DA = Dipo_Analysis(f_dump)
DA.load_data(is_dump=True, iframe=':')
DA.select_data(i_list=list(range(82)))
DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
WL = DA.plot_wall_line(norm=0, ui=1, xref=50, xlim=[46,64], if_sfc=True,
                       figsize=(5,5), fig_out=f'wl_1L3.5p_s0_0_f0_90ps'+ext)
system = '1L_3.5%_s0_0_f0_100ps'
f_dump = fdata[temp][system]+'dump_feram_0.coord'
DA = Dipo_Analysis(f_dump)
DA.load_data(is_dump=True, iframe=':')
DA.select_data(i_list=list(range(82)))
DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
WL = DA.plot_wall_line(norm=0, ui=1, xref=50, xlim=[46,68], if_sfc=True,
                       figsize=(5,5), fig_out=f'wl_1L3.5p_s0_0_f0_100ps'+ext)

## 1L-slit
temp = 260
for dz in [4,]:
    for i in range(10):
        system = f'1L_sl_dz{dz}_{i}'
        f_dump = fdata[temp][system]+'dump_feram_0.coord'
        DA = Dipo_Analysis(f_dump)
        DA.load_data(is_dump=True, iframe=':')
        DA.select_data(i_list=range(82))
        DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
        WL = DA.plot_wall_line(norm=0, ui=1, xref=50, xlim=None, dt=10, if_sfc=True,
                               figsize=(5,5), fig_out=f'wl_sl_dz{dz}_{i}'+ext)
for dy in [40,]:
    for i in range(10):
        system = f'1L_sl_dy{dy}_{i}'
        f_dump = fdata[temp][system]+'dump_feram_0.coord'
        DA = Dipo_Analysis(f_dump)
        DA.load_data(is_dump=True, iframe=':')
        DA.select_data(i_list=range(82))
        DW = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
        WL = DA.plot_wall_line(norm=0, ui=1, xref=50, xlim=None, dt=10, if_sfc=True, 
                               figsize=(5,5), fig_out=f'wl_sl_dy{dy}_{i}'+ext)

# Passed ratio
if not os.path.exists('figures/check_evo'):
    temp = 260
    pts = [7, 6, 5, 4, 3.5, 2, 0]
    f_all = []
    for pt in pts:
        if pt==0:
            f_dumps = [fdata[temp][f'pt_f100_{i}']+'dump_feram_0.coord' for i in range(10)]
            f_all.append(f_dumps)
        else:
            for c in [0, 1]:
                f_dumps = [fdata[temp][f'1L_{pt}%_s{c}_{i}']+'dump_feram_0.coord' for i in range(5)]
                f_all.append(f_dumps)
    ##
    kernel = np.ones((1,1,11))/11 # (z,y,x)
    key = {'uy': lambda u: 0.1 if u>=0 else -0.1, 'ux': lambda u: 0, 'uz': lambda u: 0}
    labels = [f'{pt}%' for pt in [pts[int(_/2)] for _ in range(2*len(pts))][:-1]]
    DC = DWPass_Collector()
    for f_dumps in f_all:
        DC.load_class(*f_dumps)
    DC.load_data(is_dump=True, iframe=':')
    DC.select_data(i_list=list(range(82)))
    DC.find_DW_surf(norm=0, ui=1, kernel=kernel, key=key)
    DC.g_cal_DW_pass(ind=50, dt=10, labels=labels, fig_out=False)
    DC.assign_group(nframe=11)
    SUM = DC.collect_dw()
    ##
    pts = [7, 6, 5, 4, 3.5, 2, 0]
    labels = [f'{pt}%' for pt in [pts[int(_/2)] for _ in range(2*len(pts))][:-1]]
    line_styles = ['-', '--']*len(pts[:-1])+['-']
    colors = ''.join(['ycmbgr'[int(_/2)%6] for _ in range(2*len(pts[:-1]))])+'k'
    for ind in [50, 51, 52]:
        DC.g_cal_DW_pass(ind=ind, dt=10, labels=labels, fig_out=False)
        SUM = DC.collect_dw()
        DC.sg_plot_DW_pass(dt=10, labels=labels, line_styles=line_styles, colors=colors, 
                           legend_title='', show_all=False, if_sfc=True, fig_out=f'dwpass_1L_ind{ind}'+ext)

# move to figures/
os.system('mkdir figures/')
os.system('mv *.png *.pdf figures/')
