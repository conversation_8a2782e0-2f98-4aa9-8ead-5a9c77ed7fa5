import os, glob
import pytest
import numpy as np
# user-defined modules
import helper
from analyze_feram import Dipo_Analysis

test_dir = 'tests/'
f_td = lambda x: os.path.join(test_dir, x)

@pytest.mark.parametrize('inputs, outputs',
                         [([f_td('example_sd.coord'), f_td('example_sd_L2.coord')], [f_td('example_sd.coord'), f_td('example_sd_L2.coord')]),
                          ([f_td('example_t180.dipoRavg'), f_td('example_sd.dipoRavg')], [f_td('example_t180.dipoRavg'), f_td('example_sd.dipoRavg')])
                         ])
def test_DA_init(inputs, outputs):
    DA = Dipo_Analysis(*inputs)
    assert DA.files==outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(glob.glob(f_td('example*.coord')), 2),
                          (glob.glob(f_td('example*.dipoRavg')), 2)
                         ])
def test_DA_load_data_nfiles(inputs, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    assert len(DA.Dataset.keys())==outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(glob.glob(f_td('example_sd.coord')), (10,10,10)),
                          (glob.glob(f_td('example_sd.dipoRavg')), (10,10,10))
                         ])
def test_DA_load_data_x_shape(inputs, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    assert DA.Dataset[0]['x'].shape==outputs

###

@pytest.mark.parametrize('inputs, shift, outputs',
                         [(glob.glob(f_td('example_sd.dipoRavg')), 0, 1),
                          (glob.glob(f_td('example_sd.dipoRavg')), 5, 1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 0, 1),
                          (glob.glob(f_td('example_*.dipoRavg')), 0, 1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 5, 0),
                         ])
def test_DA_cal_corr_uxx_x(inputs, shift, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    vshift = [shift, 0, 0]
    val = DA.cal_corr(ui0=0, ui1=0, vshift=vshift, display=0)
    assert np.all(np.abs(np.array(val, dtype=object)-outputs) < 0.5)

@pytest.mark.parametrize('inputs, shift, outputs',
                         [(glob.glob(f_td('example_sd.dipoRavg')), 0, 1),
                          (glob.glob(f_td('example_sd.dipoRavg')), 5, 1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 0, 1),
                          (glob.glob(f_td('example_*.dipoRavg')), 0, 1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 5, -1),
                         ])
def test_DA_cal_corr_uzz_x(inputs, shift, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    vshift = [shift, 0, 0]
    val = DA.cal_corr(ui0=2, ui1=2, vshift=vshift, display=0)
    assert np.all(np.abs(np.array(val, dtype=object)-outputs) < 0.5)

@pytest.mark.parametrize('inputs, ui, outputs',
                         [(glob.glob(f_td('example_sd.dipoRavg')), 0, ([0], [0])),
                          (glob.glob(f_td('example_sd.dipoRavg')), 1, ([0], [0])),
                          (glob.glob(f_td('example_sd.dipoRavg')), 2, ([0.1], [0])),
                          (glob.glob(f_td('example_t180.dipoRavg')), 2, ([0], [0.1])),
                         ])
def test_DA_cal_stat(inputs, ui, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    mean, stdev = DA.cal_stat(ui=ui)
    assert np.allclose(mean, outputs[0], atol=0.01) and np.allclose(stdev, outputs[1], atol=0.01)

@pytest.mark.parametrize('inputs, ui, outputs',
                         [(glob.glob(f_td('example_sd.dipoRavg')), 0, [[0,1000,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 1, [[0,1000,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 2, [[0,0,1000]]),
                         ])
def test_DA_cal_hist(inputs, ui, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    hist, bins = DA.cal_hist(ui=ui, umin=-0.1, umax=0.1, bin_size=0.1)
    assert np.allclose(hist, outputs)

###

@pytest.mark.parametrize('inputs, ii, jj, kk, exclude, outputs',
                         [([f_td('example_sd.dipoRavg')], range(10), range(10), range(10), None, (10,10,10)),
                          ([f_td('example_sd.dipoRavg')], range(5), range(10), range(10), None, (10,10,5)),
                          ([f_td('example_sd.dipoRavg')], range(5), range(10), range(1), None, (1,10,5)),
                          ([f_td('example_sd.dipoRavg')], None, None, None, None, (10,10,10)),
                          ([f_td('example_sd.dipoRavg')], None, range(10), range(10), None, (10,10,10)),
                          ([f_td('example_sd.dipoRavg')], range(10), range(10), range(10), [f_td('example.defects')], (10,10,10)),
                          ([f_td('example_sd.dipoRavg')], range(5), range(10), range(10), [f_td('example.defects')], (10,10,5)),
                          ([f_td('example_sd.dipoRavg')], None, range(10), range(5), [f_td('example.defects')], (5,10,10)),
                         ])
def test_DA_select_data_shape(inputs, ii, jj, kk, exclude, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    Sliceset = DA.select_data(ii, jj, kk, exclude)
    assert Sliceset[0]['x'].shape==outputs

@pytest.mark.parametrize('inputs, s0, s1, s2, outputs',
                         [([f_td('example_sd.dipoRavg')], None, {'uz': lambda x: x>0}, None, (10,10,10)),
                          ([f_td('example_sd.dipoRavg')], range(8), {'uz': lambda x: x>0}, None, (10,10,8)),
                          ([f_td('example_sd.dipoRavg')], None, {'uz': lambda x: x>0}, range(8), (10,10,8)),
                          ([f_td('example_sd.dipoRavg')], range(8), {'uz': lambda x: x>0}, range(2), (10,10,8)),
                         ])
def test_DA_multi_select_data_shape(inputs, s0, s1, s2, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    DA.select_data(i_list=s0)
    DA.select_data(key=s1)
    Sliceset = DA.select_data(i_list=s2)
    assert Sliceset[0]['x'].shape==outputs

@pytest.mark.parametrize('inputs, ii, jj, kk, exclude, outputs',
                         [([f_td('example_sd.dipoRavg')], range(10), range(10), range(10), [f_td('example.defects')], 950),
                          ([f_td('example_sd.dipoRavg')], range(5), range(10), range(10), [f_td('example.defects')], 450),
                          ([f_td('example_sd.dipoRavg')], None, range(10), range(5), [f_td('example.defects')], 500),
                          ([f_td('example_sd.dipoRavg')], range(10), range(10), range(10), ['~'+f_td('example.defects')], 50),
                          ([f_td('example_sd.dipoRavg')], range(5), range(10), range(10), ['~'+f_td('example.defects')], 50),
                          ([f_td('example_sd.dipoRavg')], None, range(10), range(5), ['~'+f_td('example.defects')], 0),
                         ])
def test_DA_select_data_invert_exclude(inputs, ii, jj, kk, exclude, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    Sliceset = DA.select_data(ii, jj, kk, exclude)
    assert np.ma.count(Sliceset[0]['x'])==outputs

@pytest.mark.parametrize('inputs, ui, condi, outputs',
                         [([f_td('example_sd.dipoRavg')], 'ux', lambda x: x>0, 0),
                          ([f_td('example_sd.dipoRavg')], 'uz', lambda x: x>0, 1000),
                          ([f_td('example_t180.dipoRavg')], 'ux', lambda x: x==0.1, 0),
                          ([f_td('example_t180.dipoRavg')], 'uz', lambda x: x>0, 500),
                          ([f_td('example_t180.dipoRavg')], 'uz', lambda x: -1<=x<0, 500),
                         ])
def test_DA_select_data_condi(inputs, ui, condi, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    Sliceset = DA.select_data(key={ui: condi})
    assert np.ma.count(Sliceset[0][ui])==outputs

###

@pytest.mark.parametrize('inputs, ui, condi, outputs',
                         [([f_td('example_sd.dipoRavg')], 0, lambda x: x>0, [np.nan]),
                          ([f_td('example_sd.dipoRavg')], 2, lambda x: x>0, [0.1]),
                          ([f_td('example_t180.dipoRavg')], 0, lambda x: x==0.1, [np.nan]),
                          ([f_td('example_t180.dipoRavg')], 2, lambda x: x>0, [0.1]),
                          ([f_td('example_t180.dipoRavg')], 2, lambda x: x<0, [-0.1]),
                         ])
def test_DA_cal_stat_select_data_condi(inputs, ui, condi, outputs):
    ind = {0: 'ux', 1: 'uy', 2: 'uz'}
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    Sliceset = DA.select_data(key={ind[ui]: condi})
    mean, std = DA.cal_stat(ui=ui)
    assert np.allclose(mean, outputs, atol=0.01, equal_nan=True)

@pytest.mark.parametrize('inputs, ui, ii, jj, kk, outputs',
                         [(glob.glob(f_td('example_sd.dipoRavg')), 0, range(10), range(10), range(10), [[0,1000,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 0, range(5), range(5), range(5), [[0,125,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 1, range(3), range(3), range(3), [[0,27,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 2, range(2), range(2), range(2), [[0,0,8]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 2, range(1), range(1), range(1), [[0,0,1]]),
                         ])
def test_DA_select_data_cal_hist(inputs, ui, ii, jj, kk, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    Sliceset = DA.select_data(ii, jj, kk)
    hist, bins = DA.cal_hist(ui=ui, umin=-0.1, umax=0.1, bin_size=0.1)
    assert np.allclose(hist, outputs)

@pytest.mark.parametrize('inputs, ui, ii, jj, kk, outputs',
                         [(glob.glob(f_td('example_sd.dipoRavg')), 0, range(10), range(10), range(10), [[0,1000,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 0, range(5), range(5), range(5), [[0,1000,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 1, range(3), range(3), range(3), [[0,1000,0]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 2, range(2), range(2), range(2), [[0,0,1000]]),
                          (glob.glob(f_td('example_sd.dipoRavg')), 2, range(1), range(1), range(1), [[0,0,1000]]),
                         ])
def test_DA_unselect_data_cal_hist(inputs, ui, ii, jj, kk, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    Sliceset = DA.select_data(ii, jj, kk)
    DA.unselect_data()
    hist, bins = DA.cal_hist(ui=ui, umin=-0.1, umax=0.1, bin_size=0.1)
    assert np.allclose(hist, outputs)

###

@pytest.mark.parametrize('inputs, ax, ui, outputs',
                         [(glob.glob(f_td('example_t180.dipoRavg')), 0, 0, -1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 0, 1, -1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 0, 2, 4.5),
                         ])
def test_DA_find_DW_1d(inputs, ax, ui, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    SUM = DA.find_DW_pos(norm=ax, ui=ui, display=0)
    assert np.allclose(SUM[0]['x']['x0'], outputs, atol=0.5)

@pytest.mark.parametrize('inputs, ax, ui, outputs',
                         [(glob.glob(f_td('example_t180.dipoRavg')), 0, 0, -1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 0, 1, -1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 0, 2, [4.5]),
                         ])
def test_DA_find_DW_pos_quick_mode(inputs, ax, ui, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    SUM = DA.find_DW_pos(norm=ax, ui=ui, mode='quick', display=0)
    assert np.allclose(SUM[0]['x']['x0'], outputs, atol=0.5)

@pytest.mark.parametrize('inputs, ax, ui, outputs',
                         [(glob.glob(f_td('example_t180.dipoRavg')), 0, 2, np.ones((3,3))*4.5),
                         ])
def test_DA_find_DW_2d_3x3(inputs, ax, ui, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    DA.select_data(range(10), range(3,6), range(3,6))
    kernel = None
    key = {'uz': lambda x: -1 if x<0 else 1}
    DW = DA.find_DW_surf(norm=ax, ui=ui, kernel=kernel, key=key, display=False)
    assert np.allclose(DW[0], outputs, atol=0.0)

@pytest.mark.parametrize('inputs, mode, kernel, key, outputs',
                         [(glob.glob(f_td('example_t180.dipoRavg')), 'ct', None, {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                          (glob.glob(f_td('example_t180.dipoRavg')), 'ct', np.ones((1,1,1)), {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                          (glob.glob(f_td('example_t180.dipoRavg')), 'ct', np.ones((1,1,3)), {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                          (glob.glob(f_td('example_t180.dipoRavg')), 'tc', None, {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                          (glob.glob(f_td('example_t180.dipoRavg')), 'tc', np.ones((1,1,1)), {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                          (glob.glob(f_td('example_t180.dipoRavg')), 'tc', np.ones((1,1,3)), {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                          (glob.glob(f_td('example_t180.dipoRavg')), 'tc', np.ones((1,1,4)), {'uz': lambda x: -1 if x<0 else 1}, np.ones((10,10))*4.5),
                         ])
def test_DA_find_DW_2d_mode(inputs, mode, kernel, key, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    DW = DA.find_DW_surf(norm=0, ui=2, kernel=kernel, key=key, mode=mode, display=False)
    assert np.allclose(DW[0], outputs, atol=0.0)

@pytest.mark.parametrize('inputs, key0, key1, outputs',
                         [(glob.glob(f_td('example_sd.coord')), {'uz': lambda x: x>0}, {'uz': lambda x: x>0}, ('T','T',0)),
                          (glob.glob(f_td('example_t180.dipoRavg')), {'uz': lambda x: x>0}, {'uz': lambda x: x<0}, ('T','T',180)),
                         ])
def test_DA_check_DW_type(inputs, key0, key1, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    res = DA.check_dw_type(key0=key0, key1=key1)
    assert res[0][0:2]==outputs[0:2] and np.allclose(res[0][-1], outputs[-1], atol=2)

###

@pytest.mark.parametrize('inputs, ax, ui, div, n_div, n_data',
                         [(f_td('dump_example_0'), 0, 1, 1, 1, 3),
                          (f_td('dump_example_0'), 0, 1, 2, 4, 3),
                          (f_td('dump_example_1'), 0, 1, 1, 1, 6),
                          (f_td('dump_example_1'), 0, 1, 3, 9, 6),
                          (f_td('dump_example_2'), 0, 1, 1, 1, 10),
                          (f_td('dump_example_2'), 0, 1, 3, 9, 10),
                         ])
def test_DA_cal_DW_velocity_shape(inputs, ax, ui, div, n_div, n_data):
    DA = Dipo_Analysis(inputs)
    DA.load_data(is_dump=True, iframe=':')
    kernel = None
    key = {'uz': lambda x: -1 if x<0 else 1}
    DW = DA.find_DW_surf(norm=ax, ui=ui, kernel=kernel, key=key, display=False)
    vDW = DA.cal_DW_velocity(norm=ax, div=div, dt=1, solid=None, x0=None, show_pos=False, Xd=None)
    assert len(vDW)==n_div and len(vDW[(0,0)][0])==len(vDW[(0,0)][1])==n_data

@pytest.mark.parametrize('inputs, ax, ui, div, outputs',
                         [(f_td('dump_example_2'), 0, 1, 1, [0,3,2,1,1,1,1,3,1,2]),
                          (f_td('dump_example_2'), 0, 1, 3, [0,3,2,1,1,1,1,3,1,2]),
                         ])
def test_DA_cal_DW_velocity_output(inputs, ax, ui, div, outputs):
    DA = Dipo_Analysis(inputs)
    DA.load_data(is_dump=True, iframe=':')
    kernel = None
    key = {'uz': lambda x: -1 if x<0 else 1}
    DW = DA.find_DW_surf(norm=ax, ui=ui, kernel=kernel, key=key, display=False)
    vDW = DA.cal_DW_velocity(norm=ax, div=div, dt=1, solid=None, x0=None, v0=0, show_pos=False, Xd=None)
    assert vDW[(0,0)][1]==outputs

###

@pytest.mark.parametrize('inputs, outputs',
                         [(glob.glob(f_td('example_sd.coord')), [1000]),
                          (glob.glob(f_td('example_t180.dipoRavg')), [500,500]),
                         ])
def test_DA_count_phase(inputs, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    SUM = DA.count_phase(u_crit=0.05)
    assert list(SUM[0].values()) == outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(glob.glob(f_td('example_sd.coord')), 1),
                          (glob.glob(f_td('example_t180.dipoRavg')), 2),
                         ])
def test_DA_catalog_phase_number(inputs, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    res = DA.catalog_phase(u_crit=0.05)
    assert len(res[0][0]) == outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(glob.glob(f_td('example_sd.coord')), ['$T_0^0$']),
                          (glob.glob(f_td('example_t180.dipoRavg')), ['$T_0^0$','$T_0^1$']),
                         ])
def test_DA_catalog_phase_labels(inputs, outputs):
    DA = Dipo_Analysis(*inputs)
    DA.load_data()
    res = DA.catalog_phase(u_crit=0.05)
    assert res[0][0] == outputs

###

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('dump_example_0'), 3),
                          (f_td('dump_example_1'), 6),
                         ])
def test_int_read_dump_Dipo_Analysis_load_data(inputs, outputs):
    DA = Dipo_Analysis(inputs)
    data = DA.load_data(is_dump=True)
    assert len(data)==outputs

@pytest.mark.parametrize('inputs, iframe, outputs',
                         [([f_td(_) for _ in ['dump_example_0', 'dump_example_0']], ':', 6),
                          ([f_td(_) for _ in ['dump_example_0', 'dump_example_0']], '0', 2),
                          ([f_td(_) for _ in ['dump_example_0', 'dump_example_0']], '-1', 2),
                          ([f_td(_) for _ in ['dump_example_0', 'dump_example_1']], ':', 9),
                          ([f_td(_) for _ in ['dump_example_0', 'dump_example_1']], '2:', 5),
                         ])
def test_int_read_n_dump_Dipo_Analysis_load_data_iframe(inputs, iframe, outputs):
    DA = Dipo_Analysis(*inputs)
    data = DA.load_data(is_dump=True, iframe=iframe)
    assert len(data)==outputs

###

@pytest.mark.parametrize('inputs, pax, outputs',
                         [(f_td('example_t180.dipoRavg'), (0,0,1), 10-1/1),
                          (f_td('example_t180.dipoRavg'), (0,1,1), np.sqrt(2)*10/2-1/np.sqrt(2)),
                          (f_td('example_t180.dipoRavg'), (0,-1,1), np.sqrt(2)*10/2-1/np.sqrt(2)),
                          (f_td('example_t180.dipoRavg'), (1,1,1), np.sqrt(3)*10/2-1/np.sqrt(3)),
                          (f_td('example_t180.dipoRavg'), (-1,1,-1), np.sqrt(3)*10/2-1/np.sqrt(3)),
                         ])
def test_DA_cal_iproj_imax(inputs, pax, outputs):
    DA = Dipo_Analysis(inputs)
    DA.load_data()
    res = DA.cal_iproj(pax=pax)
    imax = np.max(res[0])
    assert np.abs(imax-outputs)<1e-6

@pytest.mark.parametrize('inputs, pax, outputs',
                         [(f_td('example_sd.dipoRavg'), (0,0,1), 0.1),
                          (f_td('example_sd.dipoRavg'), (0,1,0), 0.0),
                          (f_td('example_sd.dipoRavg'), (1,0,0), 0.0),
                          (f_td('example_sd.dipoRavg'), (0,1,1), 0.1/np.sqrt(2)),
                          (f_td('example_sd.dipoRavg'), (1,1,1), 0.1/np.sqrt(3)),
                         ])
def test_DA_cal_uproj_sd(inputs, pax, outputs):
    DA = Dipo_Analysis(inputs)
    DA.load_data()
    res = DA.cal_uproj(pax=pax, solid=None)
    assert np.abs(np.unique(res[0])[0]-outputs)<1e-6

@pytest.mark.parametrize('inputs, rax, sax, outputs',
                         [(f_td('example_sd.dipoRavg'), (0,0,1), (1,0,0), 0),
                          (f_td('example_t180.dipoRavg'), (0,0,1), (1,0,0), 0.1),
                          (f_td('example_t180.dipoRavg'), (0,0,1), (-1,0,0), 0.1),
                          (f_td('example_t180.dipoRavg'), (0,1,0), (1,0,0), 0),
                          (f_td('example_t180.dipoRavg'), (0,0,1), (0,1,0), 0),
                          (f_td('example_t180.dipoRavg'), (1,0,0), (0,0,1), 0),
                         ])
def test_DA_cal_proj_avg_ur_stdev(inputs, rax, sax, outputs):
    DA = Dipo_Analysis(inputs)
    DA.load_data()
    res = DA.cal_proj_avg(rax=rax, sax=sax, solid=None, display=False)
    ur = np.array(res[0][0]['ur'])
    assert np.abs(np.std(ur)-outputs)<1e-2

###

@pytest.mark.parametrize('inputs, norm, div, outputs',
                         [(f_td('example.defects'), 0, 1, 50),
                          (f_td('example.defects'), 0, 2, 25),
                          (f_td('example.defects'), 1, 1, 50),
                          (f_td('example.defects'), 1, 2, 50),
                          (f_td('example.defects'), 2, 1, 50),
                          (f_td('example.defects'), 2, 2, 25),
                          (f_td('example.defects'), 2, 5, 4),
                          (f_td('example.defects'), 2, 10, 1),
                         ])
def test_DA_allocate_defects_ag2d(inputs, norm, div, outputs):
    DA = Dipo_Analysis()
    Xd = DA.alloc_defects_2d(inputs, norm=norm, size=(10,10,10), div=div, is_dump=False)
    if div==1:
        assert len(Xd[(0,0)]['x'])==outputs
    else:
        assert len(Xd[(1,0)]['x'])==outputs

###