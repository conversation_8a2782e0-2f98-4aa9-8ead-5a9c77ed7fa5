import os, glob
import subprocess as spb
import numpy as np
import pandas as pd

class Modulation():
    """ Createing modulation files for Feram """
    def __init__(self, Lbox=None, r_ba=0.5, seed=1000):
        """
        Parameters
        ----------
        Lbox: int or tuple(3)
        r_ba: float
            ratio of Ba sites (0<=r_ba<=1)
        seed: int
            random seed
        """
        if Lbox==None:
            print('Warning: Modulation file will not be created!')
            return
        elif isinstance(Lbox, int):
            self.Lx = self.Ly = self.Lz = Lbox
        elif isinstance(Lbox, tuple) and len(Lbox)==3:
            self.Lx = Lbox[0]
            self.Ly = Lbox[1]
            self.Lz = Lbox[2]
        ntot = self.Lx * self.Ly * self.Lz
        self.Sites = {'Ba': int(ntot*r_ba), 'Sr': ntot-int(ntot*r_ba)}
        self.MOD = {}
        for k in range(self.Lz):
            for j in range(self.Ly):
                for i in range(self.Lx):
                    self.MOD[(i,j,k)] = 0
        np.random.seed(seed)
        print(self.Sites)

    def set_rand(self):
        sites=[8 for _ in range(self.Sites['Ba'])]+[-8 for _ in range(self.Sites['Sr'])]
        np.random.shuffle(sites)
        sites_ary=np.array(sites).reshape(self.Lz, self.Ly, self.Lx)
        for k in range(self.Lz):
            for j in range(self.Ly):
                for i in range(self.Lx):
                    self.MOD[(i,j,k)] = sites_ary[k][j][i]

    def set_cubic(self, o=(0,0,0), stype='Ba'):
        t = (8, -8) if stype=='Ba' else (-8, 8)
        self.MOD = {key: t[1] for key in self.MOD}
        n = self.Sites[stype]
        a = int(np.ceil(n**(1/3)))
        sites=[t[0] for _ in range(n)]+[t[1] for _ in range(a**3-n)]
        np.random.shuffle(sites)
        sites_ary=np.array(sites).reshape(a, a, a)
        for i in range(a):
            for j in range(a):
                for k in range(a):
                    self.MOD[(o[0]+i, o[1]+j, o[2]+k)] = sites_ary[k][j][i]

    def set_layer(self, normal=0, o=0, stype='Ba'):
        t = (8, -8) if stype=='Ba' else (-8, 8)
        self.MOD = {key: t[1] for key in self.MOD}
        n = self.Sites[stype]
        ax = [0,1,2]
        ax.pop(normal)
        L = [self.Lx, self.Ly, self.Lz]
        area = L[ax[0]]*L[ax[1]]
        L[normal] = int(np.ceil(n/area))
        sites=[t[0] for _ in range(n)]+[t[1] for _ in range(L[normal]*area-n)]
        np.random.shuffle(sites)
        sites_ary=np.array(sites).reshape(L[2], L[1], L[0])
        vec = [0,0,0]
        vec[normal] = 1
        for i in range(L[0]):
            for j in range(L[1]):
                for k in range(L[2]):
                    ind = np.array([i,j,k])+np.array(vec)*o
                    self.MOD[tuple(ind)] = sites_ary[k][j][i]

    def write_modulation(self, PATH=None, FILE='bst'):
        PATH = PATH if PATH!=None else os.getcwd()
        if not os.path.exists(PATH):
            os.makedirs(PATH)
        fw = open(os.path.join(PATH,'{}.modulation'.format(FILE)), 'w')
        for k in range(self.Lz):
            for j in range(self.Ly):
                for i in range(self.Lx):
                    fw.write('{} {} {} {}\n'.format(i,j,k,self.MOD[(i,j,k)]))
        fw.close()

# constants
c_kb = 8.617333262*1e-5 # eV/K

def prefactor(solid='bto', mode=None):
    """ Prefactor of dipole moment or lattice constant for feram

    Parameters
    ----------
    solid: str
        ['bto'], 'bst'
    mode: str
        [None], 'a': lattice constant

    Returns
    -------
    prefactor: float
        prefactor in unit of (polarization: uC/cm^3, lattice constant: Angstrom)
    """
    # calculating the prefactor
    epsilon = {'bto': 10.33, 'bst': 9.807238756} #Z_star
    a = {'bto': 3.9859, 'bst': 3.9435} #lattice constant, Ang.
    if mode=='a':
        return a[solid] if solid in a else 1.
    else:
        prefactor = (1.6*10**3)*epsilon[solid]/a[solid]**3 if solid in a else 1
    return prefactor

# parser
def parse_avg(fname=None):
    """ Parse .avg file for feram

    Parameters
    ----------
    fname: str
        filename of .avg [None]

    Returns
    -------
    avg_ary: np.array
        numpy array of avg file
    """
    with open(fname, 'r') as fr:
        raw = fr.readlines()
    # add zeros for original raw data
    avg_list = [tuple(map(float,c+[0 for _ in range(43+6+17-len(c))])) for c in list(map(lambda line: line.strip().split(), raw))]
    columns = 'T Ex Ey Ez exx eyy ezz eyz ezx exy ux uy uz uxux uyuy uzuz uyuz uzux uxuy '
    columns += 'dk lr dEf unhar s_ho c_ho s_inho c_inho etot HNP e2 dkt ak sr mod '
    columns += 'px py pz ppx ppy ppz ppyz ppzx ppxy '
    columns += 'mx my mz amx amy amz '
    columns += 'usq '
    columns += 'ux4 uy4 uz4 uyz2 uzx2 uxy2 '
    columns += 'ux6 uy6 uz6 ux4y2 ux4z2 uy4x2 uy4z2 uz4x2 uz4y2 uxyz2'
    dt = [(_c, np.dtype(float)) for _c in columns.split()]
    return np.array(avg_list, dtype=dt)

def parse_hl(fname=None):
    """ Parse .hl file for feram

    Parameters
    ----------
    fname: str
        filename of .hl [None]

    Returns
    -------
    avg_ary: np.array
        numpy array of hl file
    """
    with open(fname, 'r') as fr:
        raw = fr.readlines()
    hl_list = [tuple(map(float,c)) for c in list(map(lambda line: line.strip().split(), raw))]
    items = 'step T Ex Ey Ez exx eyy ezz eyz ezx exy ux uy uz '
    if len(hl_list[0])==35:
        items += 'dv1 dv2 dv3 dv4 dv5 dv6 '
        items += 'dk sr lr dEf unhar s_ho c_ho s_inho c_inho mod etot HNP etot2 dkosn2 ak '
    columns = items.split()
    dt = [(_c, np.dtype(int)) for _c in columns[:1]]+[(_c, np.dtype(float)) for _c in columns[1:]]
    return np.array(hl_list, dtype=dt)

def parse_data(filename=None, fmt=None, is_raw=False):
    """ Parsing space-separated data

    Parameters
    ----------
    filename: str
        file name of *.coord or *.dipoRavg
    fmt: str
        [None]: auto-detection, 'coord'|'dipoRavg'|'modulation'|'defects'|'localfield'
    is_raw: boolean
        [False] if using file name, True if using raw text

    Returns
    -------
    out: numpy.array
        named array of data, column number depends on the raw data
    """
    if is_raw:
        raw = filename   # raw text as input
    else:
        with open (filename, 'r') as fr:
            raw = fr.readlines()
    data_list = [tuple(map(float,c)) for c in list(map(lambda line: line.strip().split(), raw))]
    nrow, ncol = np.shape(data_list)
    # format
    FMT = {18: "x y z ux uy uz ppx ppy ppz ddx ddy ddz arx ary arz apx apy apz",
           6: "x y z ux uy uz",
           4: "x y z mod"}
    if fmt in ['coord']:
        nc = 18
    elif fmt in ['dipoRavg', 'defects', 'localfield']:
        nc = 6
    elif fmt in ['modulation']:
        nc = 4
    elif fmt is None:
        if ncol in FMT:
            nc = ncol
        else:
            print(f'[Error]: undefined format!')
    else:
        print(f'[Error]: format {fmt} not found!')
    # trim data
    if ncol!=nc:
        data_list = [_[:nc] for _ in data_list]
    columns = FMT[nc]
    dt = [(_c, np.dtype(int)) for _c in "x y z".split()]+[(_c, np.dtype(float)) for _c in columns.split()[3:]]
    return np.array(data_list, dtype=dt)

# feram
def write_feram(PATH=None, FILE='bto', text='', add_options=None):
    """ Writing a Feram input file

    Parameters
    ----------
    PATH: str
    FILE: str
    text: str
    add_options: list
         [('tag', val, [line number]), ...]

    Returns
    -------
    None
    """
    PATH = PATH if PATH!=None else os.getcwd()
    if not os.path.exists(PATH):
        os.makedirs(PATH)
    TEXT = text.split('\n')
    if add_options!=None:
        for option in add_options:
            if len(option)==2:
                tag, val = option
                num = len(TEXT)
            elif len(option)==3:
                tag, val, num = option
            TEXT.insert(num, '{} = {}'.format(tag, val))
    text = '\n'.join(TEXT)
    with open(os.path.join(PATH, FILE+'.feram'), 'w') as fw:
        fw.write(text)

def run_feram(PATH=None, FILE='bst', tag=None, f_out='data.avg', header='', mode=1, cores=None, exe='feram'):
    """ Running Feram 

    Parameters
    ----------
    PATH: str
        path for working directory
    FILE: str
        file name without extension ['bst']
    tag: str
        tag for naming output files, i.e. coord, dipoRavg, log, hl
    f_out: str
        file name for cumulative avg file
    header: str
        string at first column of cumulative avg file
    mode: int
        0: Feram default avg, 1: +mx,my,mz,amx,amy,amz, 2: 1+<u^2>,<u^4>,<u^6> (more details please refer to `stat_dipo`)
    cores: int
        number of cores
    exe: str
        Feram executable

    Returns
    -------
    None
    """
    #spb.call("module load feram/0.26.04", shell=True)
    owd=os.getcwd()
    if PATH==None:
        PATH=owd
    try:
        os.chdir(PATH)
        spb.call('OMP_NUM_THREADS={} '.format(cores)*(cores!=None)+'{} {}.feram'.format(exe,FILE), shell=True)
        if mode==0:
            spb.call('cat {}.avg >> data.avg'.format(FILE), shell=True)
        elif mode==1:
            avg_dipo(f_dipo='{}.dipoRavg'.format(FILE), f_avg='{}.avg'.format(FILE), f_out=f_out, header=header)
        elif mode==2:
            stat_dipo(f_dipo=f'{FILE}.dipoRavg', f_avg=f'{FILE}.avg', f_out=f_out, header=header)
        f_coords=sorted(glob.glob(r'*.coord'), key=lambda x: x.split('.')[1])
        if tag!=None:
            os.system('mkdir logfile')
            os.system('cp {}.dipoRavg logfile/{}.dipoRavg'.format(FILE, tag))
            os.system('cp {}.log logfile/{}.log'.format(FILE, tag))
            os.system('cp {}.hl logfile/{}.hl'.format(FILE, tag))
        if len(f_coords)!=0:
            f_coord=f_coords[-1]
            if tag!=None:
                os.system('cp '+f_coord+' logfile/{}.coord'.format(tag))
            print('rename {} {}.restart'.format(f_coord, FILE))
            os.rename(f_coord, '{}.restart'.format(FILE))
    finally:
        os.chdir(owd)

# post-processing
def avg_dipo(f_dipo=None, f_avg=None, f_out='data.avg', header=''):
    """ Averaging local polarizations of a .dipoRavg file """
    if not os.path.exists(f_avg):
        return

    with open(f_avg, 'r') as fr:
        avg = fr.readlines()
    mode = 'a+' if os.path.exists(f_out) else 'w'
    with open(f_out, mode) as fw:
        if os.path.exists(f_dipo):
            M_dipo = parse_data(filename=f_dipo, fmt='dipoRavg')
            ux_ary, uy_ary, uz_ary = M_dipo['ux'], M_dipo['uy'], M_dipo['uz']
            mux, muy, muz = np.mean(ux_ary), np.mean(uy_ary), np.mean(uz_ary)
            amux, amuy, amuz = np.mean(np.abs(ux_ary)), np.mean(np.abs(uy_ary)), np.mean(np.abs(uz_ary))
        else:
            mux = muy = muz = 0
            amux = amuy = amuz = 0
        fw.write('{} '.format(header)+avg[0].strip()+f' {mux:.7E} {muy:.7E} {muz:.7E} ')
        fw.write(f'{amux:.7E} {amuy:.7E} {amuz:.7E}\n')
    return (mux, muy, muz), (amux, amuy, amuz)

def stat_dipo(f_dipo=None, f_avg=None, f_out='data.avg', header=''):
    """ Statistics for higher-order terms of local time-averaged polarizations """
    if not os.path.exists(f_avg):
        return
    with open(f_avg, 'r') as fr:
        avg = fr.readlines()
    mode = 'a+' if os.path.exists(f_out) else 'w'
    with open(f_out, mode) as fw:
        if os.path.exists(f_dipo):
            M_dipo = parse_data(filename=f_dipo, fmt='dipoRavg') 
            ux, uy, uz = M_dipo['ux'], M_dipo['uy'], M_dipo['uz']
            mux, muy, muz = np.mean(ux), np.mean(uy), np.mean(uz)
            amux, amuy, amuz = np.mean(np.abs(ux)), np.mean(np.abs(uy)), np.mean(np.abs(uz))
            usq = np.mean(ux**2+uy**2+uz**2)
            ux4, uy4, uz4 = np.mean(ux**4), np.mean(uy**4), np.mean(uz**4)
            uyz2, uzx2, uxy2 = np.mean((uy*uz)**2), np.mean((uz*ux)**2), np.mean((ux*uy)**2)
            ux6, uy6, uz6 = np.mean(ux**6), np.mean(uy**6), np.mean(uz**6)
            ux4y2, ux4z2 = np.mean(ux**2*(ux*uy)**2), np.mean(ux**2*(ux*uz)**2)
            uy4x2, uy4z2 = np.mean(uy**2*(uy*ux)**2), np.mean(uy**2*(uy*uz)**2)
            uz4x2, uz4y2 = np.mean(uz**2*(uz*ux)**2), np.mean(uz**2*(uz*uy)**2)
            uxyz2 = np.mean((ux*uy*uz)**2)
        else:
            mux = muy = muz = 0
            amux = amuy = amuz = 0
            usq = 0
            ux4 = uy4 = uz4 = 0
            uyz2 = uzx2 = uxy2 = 0
            ux6 = uy6 = uz6 = 0
            ux4y2 = ux4z2 = 0
            uy4x2 = uy4z2 = 0
            uz4x2 = uz4y2 = 0
            uxyz2 = 0
        fw.write('{} '.format(header)+avg[0].strip()+f' {mux:.7E} {muy:.7E} {muz:.7E} ')
        fw.write(f'{amux:.7E} {amuy:.7E} {amuz:.7E} ')
        fw.write(f'{usq:.7E} ')
        fw.write(f'{ux4:.7E} {uy4:.7E} {uz4:.7E} {uyz2:.7E} {uzx2:.7E} {uxy2:.7E} ')
        fw.write(f'{ux6:.7E} {uy6:.7E} {uz6:.7E} ')
        fw.write(f'{ux4y2:.7E} {ux4z2:.7E} {uy4x2:.7E} {uy4z2:.7E} {uz4x2:.7E} {uz4y2:.7E} ')
        fw.write(f'{uxyz2:.7E}\n')
    u4 = (ux4, uy4, uz4, uyz2, uzx2, uxy2)
    u6 = (ux6, uy6, uz6, ux4y2, ux4z2, uy4x2, uy4z2, uz4x2, uz4y2, uxyz2)
    return (mux, muy, muz), (amux, amuy, amuz), usq, u4, u6
