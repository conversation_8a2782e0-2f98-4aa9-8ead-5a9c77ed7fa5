import os
import numpy as np
import shutil
import subprocess as spb
import glob
from collections import OrderedDict
# user-defined modules
from utilities_feram import Modulation, write_feram, run_feram
from generate_defects import Defects
from create_domains import DomainWall
from analyze_feram import Dipo_Analysis
from feram_converter import feram2dump

# feram input
input_feram = lambda verbose = 2, method = 'md', Q_Nose = 0.1, GPa = 0, kelvin = 300, bulk_or_film = 'bulk', \
                     L0 = 12, L1 = 12, L2 = 12, \
                     dt = 0.002, n_thermalize = 40000, n_average = 20000, \
                     n_hl_freq = 5000, n_coord_freq = 60000, \
                     coord_directory = 'never', slice_directory = 'never', distribution_directory = 'never', \
                     external_E_field0 = 0.0, external_E_field1 = 0.0, external_E_field2 = 0.0, \
                     init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0, \
                     init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03: \
"""# Parameters for BaTiO3 effective Hamiltonian
# from https://journals.aps.org/prb/abstract/10.1103/PhysRevB.82.134106
#--- Method, Temperature, and mass ---------------
verbose = {verbose}
method = '{method}'
Q_Nose = {Q_Nose}
GPa = {GPa}
kelvin = {kelvin}
mass_amu = 38.24
# acoustic_mass_amu = 41.67

#--- System geometry -----------------------------
bulk_or_film = '{bulk_or_film}'
L = {L0} {L1} {L2}

#--- Elastic Constants ---------------------------
B11 = 126.731671475652
B12 = 41.7582963902598
B44 = 49.2408864348646

#--- From perovskite-optcell2-p.gp ---------------
B1xx = -185.347187551195 [eV/Angstrom^2]
B1yy = -3.28092949275457 [eV/Angstrom^2]
B4yz = -14.5501738943852 [eV/Angstrom^2]
P_k1 = -267.98013991724 [eV/Angstrom^6]
P_k2 = 197.500718362573 [eV/Angstrom^6]
P_k3 = 830.199979293529 [eV/Angstrom^6]
P_k4 = 641.968099408642 [eV/Angstrom^8]
P_alpha = 78.9866142426818 [eV/Angstrom^4]
P_gamma = -115.484148812672 [eV/Angstrom^4]

#--- Time step -----------------------------------
dt = {dt} [pico second]
n_thermalize = {n_thermalize}
n_average = {n_average}
n_hl_freq = {n_hl_freq}
n_coord_freq = {n_coord_freq}
coord_directory = '{coord_directory}'
slice_directory = '{slice_directory}'
distribution_directory = '{distribution_directory}'

#--- External electric field ---------------------
external_E_field = {external_E_field0} {external_E_field1} {external_E_field2}

#--- From eigenvalues2j --------------------------
P_kappa2 = 8.53400622096412 [eV/Angstrom^2]
j = -2.08403 -1.12904  0.68946 -0.61134  0.00000  0.27690  0.00000  [eV/Angstrom^2]
a0 = 3.98597 [Angstrom]
Z_star = 10.33000
epsilon_inf = 6.86915

#--- Initial dipole configrations ----------------
init_dipo_avg = {init_dipo_avg0} {init_dipo_avg1} {init_dipo_avg2} [Angstrom]
init_dipo_dev = {init_dipo_dev0} {init_dipo_dev1} {init_dipo_dev2} [Angstrom]
""".format(verbose=verbose, method=method, Q_Nose=Q_Nose, GPa=GPa, kelvin=kelvin, bulk_or_film=bulk_or_film, 
           L0=L0, L1=L1, L2=L2,
           dt=dt, n_thermalize=n_thermalize, n_average=n_average, n_hl_freq=n_hl_freq, n_coord_freq=n_coord_freq, 
           coord_directory=coord_directory, slice_directory=slice_directory,
           distribution_directory=distribution_directory, external_E_field0=external_E_field0,
           external_E_field1=external_E_field1, external_E_field2=external_E_field2,
           init_dipo_avg0=init_dipo_avg0, init_dipo_avg1=init_dipo_avg1, init_dipo_avg2=init_dipo_avg2,
           init_dipo_dev0=init_dipo_dev0, init_dipo_dev1=init_dipo_dev1, init_dipo_dev2=init_dipo_dev2)

# workflow
class WorkChain_T180DW_Defects():
    def __init__(self, PATH='./', FILE='bto', xsize=164, ysize=48, zsize=48, 
                 seed=1000, seed1=123456789, ncore=20, tag=0, worklog='worklog'):
        self.PATH = PATH
        self.FILE = FILE
        self.xsize = xsize
        self.ysize = ysize
        self.zsize = zsize
        self.seed = seed
        self.seed1 = seed1
        self.ncore = ncore
        self.tag = tag
        self.worklog = worklog

    # calculation functions
    def cf_set_seed1(self, seed1=123456789):
        self.seed1 = seed1

    def cf_set_tag(self, tag=0):
        self.tag = tag

    def cf_create_T180DW(self, temp=260, field_stages=[100,], w_domain=82, n_domain=1, plane_norm=(1,0,0)):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        print('seed1',seed1)
        for ef in field_stages:
            # create domains
            DW = DomainWall(Lbox=(xsize,ysize,zsize), w_domain=w_domain, n_domain=n_domain, plane_norm=plane_norm)
            loc_ef = 0.00001*(ef) # (kV/cm)
            DW.set_P_domain((0,loc_ef,0), (0,-1*loc_ef,0)) # T180
            DW.write_domain(f_out='{}.localfield'.format(FILE), mode='localfield', seed=seed)
            
            # initialization
            text = input_feram(verbose=2, method='md', Q_Nose=15, dt=0.001,
                               GPa=0, 
                               kelvin=temp,
                               L0=xsize, L1=ysize, L2=zsize,
                               n_thermalize=20000,
                               n_average=10000,
                               n_hl_freq=30000,
                               n_coord_freq=30000,
                               coord_directory = './',
                               external_E_field0=0.0, external_E_field1=0.0, external_E_field2=0.0,
                               init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                               init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
            options = [('seed', '{} 987654321'.format(seed1), 10)]
            write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
            run_feram(PATH=PATH, FILE=FILE, tag='init', header='', f_out='data.avg', cores=ncore)
    
        # move localfield to folder & backup
        os.system('mv {}.localfield data.avg logfile'.format(FILE))
    
    def cf_run_thermalization(self, temp=260, dt=0.001, n_thermalize=20000, n_average=10000, tag='therm'):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        # thermalization w/o localfield
        n_total = n_thermalize + n_average
        text = input_feram(verbose=2, method='md', Q_Nose=15, dt=dt,
                           GPa=0, 
                           kelvin=temp,
                           L0=xsize, L1=ysize, L2=zsize,
                           n_thermalize=n_thermalize,
                           n_average=n_average,
                           n_hl_freq=n_total,
                           n_coord_freq=n_total,
                           coord_directory = './',
                           external_E_field0=0.0, external_E_field1=0.0, external_E_field2=0.0,
                           init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                           init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
        options = [('seed', '{} 987654321'.format(seed1), 10)]
        write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
        run_feram(PATH=PATH, FILE=FILE, tag=tag, header='', f_out='data.avg', cores=ncore)
    
        # backup
        os.system('mv {}.feram data.avg logfile'.format(FILE))
    
    def cf_generate_defects_T180DW(self, percent=0, dd_strength=0.1):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        # generate defects
        pdd = dd_strength
        ndd = -1*dd_strength
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        rnd1 = D.random_df(percent=percent, defects=(0.0,pdd,0.0), seed=seed, xlim=[0, xsize//4], ylim=None, zlim=None)
        rnd2 = D.random_df(percent=percent, defects=(0.0,ndd,0.0), seed=seed, xlim=[xsize//4, xsize*3//4], ylim=None, zlim=None)
        rnd3 = D.random_df(percent=percent, defects=(0.0,pdd,0.0), seed=seed, xlim=[xsize*3//4, xsize], ylim=None, zlim=None)
        D.write(defects=[rnd1, rnd2, rnd3]) 

    def cf_generate_n_defects(self, n=1, a=1, d=1, axis=2, dd_strength=0.1):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        # generate defects
        ndd = -1*dd_strength
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        defects = []
        if axis==1:
            y = ysize//2 - (n>1)*d//n - a//(n%2+1)
            for i in range(n):
                cb = D.cubic_df(o=(xsize//4+12,y,zsize//2), a=a, defects=(0.0, ndd, 0.0))
                defects.append(cb)
                y+=a+d
        elif axis==2:
            z = zsize//2 - (n>1)*d//n - a//(n%2+1)
            for i in range(n):
                cb = D.cubic_df(o=(xsize//4+12,ysize//2,z), a=a, defects=(0.0, ndd, 0.0))
                defects.append(cb)
                z+=a+d
        D.write(defects=defects)

    def cf_generate_layer_defects(self, normal=0, ilo=0, ihi=1, ratio=0, dd_strength=0.1, seed=0):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        # generate defects
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        defects = []
        for i in range(ilo,ihi+1):
            d = D.plane_df(normal=normal, o=i, ratio=ratio, defects=(0.0, dd_strength, 0.0), seed=seed)
            defects.append(d)
        D.write(defects=defects)

    def cf_run_field_ramping(self, ef_kVocm=100, temp=260, n_thermalize=150000, 
                             n_hl_freq=1000, n_coord_freq=15000):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        seed = self.seed
        seed1 = self.seed1
        ncore = self.ncore
        # field ramping
        efield = 0.00001*(ef_kVocm) # kV/cm
        text = input_feram(verbose=1, method='md', Q_Nose=15, dt=0.001,
                           GPa=0, 
                           kelvin=temp,
                           L0=xsize, L1=ysize, L2=zsize,
                           n_thermalize=n_thermalize,
                           n_average=0,
                           n_hl_freq=n_hl_freq,
                           n_coord_freq=n_coord_freq,
                           coord_directory = './',
                           external_E_field0=0.0, external_E_field1=efield, external_E_field2=0.0,
                           init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                           init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
        options = [('seed', '{} 987654321'.format(seed1), 10),
                   ('n_E_wave_period', 0, 37), ('E_wave_type', "'ramping_on'", 38)]
        write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
        run_feram(PATH=PATH, FILE=FILE, tag='ramp', header='', f_out='data.avg', cores=ncore)
    
        os.system('rm -f {}.restart'.format(FILE))
        os.system('mv fort.12 {}.hl'.format(FILE))
        os.system('mv {}.* data.avg logfile'.format(FILE))

    def cf_dw_analysis(self, norm=0, ui=1, xlim=(0,82), ylim=(0,48), zlim=(0,48), rmse_crit=0.01, 
                       log='dw_analysis', overwrite=False, keep_all=True):
        FILE = self.FILE
        tag = self.tag
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        ymin, ymax = ylim
        zmin, zmax = zlim
        f_coords = sorted(glob.glob('logfile/{}.0*.coord'.format(FILE)))
        RES=[]
        backup=0
        for i,f in enumerate(f_coords):
            print(f)
            DA = Dipo_Analysis(f)
            Dipos = DA.load_data()
            # quick check DW pos
            res_qk = DA.find_DW_pos(norm=norm, ui=ui, rmse_crit=0.01, mode='quick', display=False)
            xdws = res_qk[0]['x']['x0']
            wdws = res_qk[0]['x']['w0']
            if xlim==None:
                if isinstance(xdws, int) and xdws==-1:
                    xmin, xmax = 0, 162
                else:
                    xmed = (xdws[0]+xdws[-1])/2
                    xmin = xdws[0]-(xmed-xdws[0]) if len(xdws)>=2 else 0
                    xmax = xmed if len(xdws)>=2 else xdws[0]+wdws[0]
            else:
                xmin, xmax = xlim
            DA.select_data(i_list=range(int(xmin),int(xmax)), j_list=range(int(ymin),int(ymax)), k_list=range(int(zmin),int(zmax)))
            # fitting
            try:
                res = DA.find_DW_pos(norm=norm, ui=ui, rmse_crit=rmse_crit, display=False)
                if not res[0]['status']:
                    backup+=1
            except:
                res = None
            RES.append(res)
        rw = 'w' if overwrite else 'a+'
        with open(log, rw) as fw:
            fw.write(str({tag: RES})+'\n')
        # dump coord/defects
        feram2dump(path='./logfile/', ext='0.coord', step=1, reverse=False, size=(xsize,ysize,zsize), f_mod=None)
        feram2dump(path='./logfile/', ext='.defects', step=1, reverse=False, size=(xsize,ysize,zsize), f_mod=None)
        # check fitting status
        backup = 1 if keep_all else backup
        if backup==0:
            print('# fitting successful')
            os.system('rm -f logfile/*.coord logfile/*.dipoRavg')
        else:
            print('# tar files')
            os.system('tar -jcvf logfile/`date +"%Y%m%d"`_coord.tar.bz2 logfile/*.coord && rm -f logfile/*.coord')
        os.system('mv dump* logfile')
        os.system('mv logfile logfile_ramp_{}'.format(tag))

    def cf_dw_record(self, log_in='dw_analysis', log_out='dw_record', overwrite=True):
        with open(log_in, 'r') as fr:
            raw = fr.readlines()
        SUM=OrderedDict()
        for line in raw:
            data = eval(line)
            for i in data:
                X0 = [_[0]['x']['x0'] for _ in data[i]]
                W0 = [_[0]['x']['w0'] for _ in data[i]]
                dX0 = [x-X0[0] for x in X0]
                V0 = np.gradient(dX0).tolist()
                SUM[i] = {'x0': X0, 'w0': W0, 'v0': V0}
        rw = 'w' if overwrite else 'a+'
        with open(log_out, rw) as fw:
            fw.write(f'{log_in}: '+str(SUM)+'\n')
        # tar files
        os.system(f'mkdir dir_{log_in}')
        os.system(f'mv logfile_ramp_* dir_{log_in}')

# helper functions
def multi_run(cls, outline, n=10):
    for i in range(n):
        cls.cf_set_seed1(seed1=123456789+i)
        cls.cf_set_tag(tag=i)
        for func, param in outline:
            print('# function:', func.__name__)
            print('# parameters:', param)
            func(**param)

# main program
WC = WorkChain_T180DW_Defects(PATH='./', FILE='bto',
                              xsize=164, ysize=48, zsize=48,
                              seed=0, ncore=16)

for temp in [260, 280, 240]:
    record = f'rec_{temp}K'
    os.system(f'mkdir {temp}K')
    dd = 0.09 # 0.00, 0.09, 0.15 (+-+)
    for pt in [0.5, 1, 2,]: # 0.5%, 1%, 2%,
        efield = 100
        analysis = f'ana_dd{dd:.2f}_{pt:.1f}pt_s0_{efield}kVocm'
        outline = [(WC.cf_create_T180DW, dict(temp=temp, field_stages=[100,], w_domain=82, n_domain=1, plane_norm=(1,0,0)) ),
                   (WC.cf_generate_defects_T180DW, dict(percent=pt, dd_strength=dd) ),
                   (WC.cf_run_thermalization, dict(temp=temp, dt=0.001, n_thermalize=20000, n_average=10000, tag='0-therm0')),
                   (WC.cf_run_field_ramping, dict(ef_kVocm=efield, temp=temp, n_thermalize=100000,
                                                  n_hl_freq=1000, n_coord_freq=10000) ),
                   (WC.cf_dw_analysis, dict(norm=0, ui=1, xlim=None, ylim=(0,48), zlim=(0,48), rmse_crit=0.01, 
                                            log=analysis, overwrite=False))]
        multi_run(WC, outline, n=10)
        WC.cf_dw_record(log_in=analysis, log_out=record, overwrite=False)
    os.system(f'mv ana_dd* dir_ana_dd* {temp}K')
