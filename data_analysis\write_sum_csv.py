import os, glob
import numpy as np
from analyze_feram import Dipo_Analysis
from custom_csv import DWCSV_Helper
from Data import fdata

# Analysis
def run_analysis(Data, kernel, key, fcsv_out=None):
    H = DWCSV_Helper()
    H.set_sim_params(dt=10, solid='bto') # ps
    divs = [1,3,4,12] # division along length axis of 2D area
    H.set_dw_params(norm=0, ui=1, divs=divs, kernel=kernel, key=key, x0=None, v0=0) # x:0, y:1, z:2

    # pristine
    disp_r = False # display roughness
    disp_v = False # display velocity
    if_print = False
    H.set_disp(disp_r=disp_r, disp_v=disp_v)
    H.initialize_dw_sum()
    
    for temp in [240, 260, 280]: #[150, 200, 240, 260, 280, 290]: # K
        for field in [0, 75, 100, 150]: # kV/cm
            for i in range(10):
                system = f'pt_f{field}_{i}'
                f_dump = Data[temp][system]+'dump_feram_0.coord'
                H.set_defect_params(f_defects='')
                H.run_dw_summary(f_dump, temp, system, if_print)
    H.output(to_csv=True, f_out=fcsv_out, if_replace=True) # from scratch
    
    # 1L (1,2,2.5,3,3.5,4%)
    disp_r = False # display roughness
    disp_v = False # display velocity
    if_print = False
    H.set_disp(disp_r=disp_r, disp_v=disp_v)
    H.initialize_dw_sum()
    
    for temp in [240, 260, 280]: #[150, 200, 240, 260, 280, 290]: # K
        for ratio in [1, 2, 2.5, 3, 3.5, 4]: # defect concentrations
            for s in range(2):
                for i in range(5):
                    system = f'1L_{ratio}%_s{s}_{i}'
                    f_dump = Data[temp][system]+'dump_feram_0.coord'
                    f_defects = Data[temp][system]+'dump_feram_defects'
                    H.set_defect_params(f_defects=f_defects)
                    H.run_dw_summary(f_dump, temp, system, if_print)
    H.output(to_csv=True, f_out=fcsv_out, if_replace=False)
    
    # 1L (5,6,7,8%)
    disp_r = False # display roughness
    disp_v = False # display velocity
    if_print = False
    H.set_disp(disp_r=disp_r, disp_v=disp_v)
    H.initialize_dw_sum()
    
    for temp in [260]: # K
        for ratio in [5,6,7,8]: # defect concentrations
            for s in range(2):
                for i in range(5):
                    system = f'1L_{ratio}%_s{s}_{i}'
                    f_dump = Data[temp][system]+'dump_feram_0.coord'
                    f_defects = Data[temp][system]+'dump_feram_defects'
                    H.set_defect_params(f_defects=f_defects)
                    H.run_dw_summary(f_dump, temp, system, if_print)
    H.output(to_csv=True, f_out=fcsv_out, if_replace=False)

    # rand.3D
    disp_r = False # display roughness
    disp_v = False # display velocity
    if_print = False
    H.set_disp(disp_r=disp_r, disp_v=disp_v)
    H.initialize_dw_sum()
    
    for temp in [240, 260, 280]: # K
        for ratio in [0.5, 1, 2]: # defect concentrations
            for s in [0]: # dd: 0.09
                for i in range(10):
                    system = f'3D_{ratio}%_s{s}_{i}'
                    f_dump = Data[temp][system]+'dump_feram_0.coord'
                    f_defects = Data[temp][system]+'dump_feram_defects'
                    H.set_defect_params(f_defects=f_defects)
                    H.run_dw_summary(f_dump, temp, system, if_print)
    for temp in [240, 260, 280]: # K
        for ratio in [1,]: # defect concentrations
            for s in [3, 4]: # dd: 0.11, 0.15
                for i in range(10):
                    system = f'3D_{ratio}%_s{s}_{i}'
                    f_dump = Data[temp][system]+'dump_feram_0.coord'
                    f_defects = Data[temp][system]+'dump_feram_defects'
                    H.set_defect_params(f_defects=f_defects)
                    H.run_dw_summary(f_dump, temp, system, if_print)
    H.output(to_csv=True, f_out=fcsv_out, if_replace=False)



# main
kernel = (1,1,11) # (nz,ny,nx)
key = {'uy': lambda u: 0.1 if u>=0 else -0.1, 'ux': lambda u: 0, 'uz': lambda u: 0}
knl = np.ones(kernel)/np.prod(kernel)
run_analysis(fdata, knl, key, fcsv_out='DW_summary_{}x{}x{}.csv'.format(*kernel[::-1]))
os.system('mkdir csv_files/')
os.system('cp DW_summary_{}x{}x{}.csv DW_summary.csv'.format(*kernel[::-1])) # {nx}x{ny}x{nz}
os.system('mv DW_summary_*.csv csv_files')
