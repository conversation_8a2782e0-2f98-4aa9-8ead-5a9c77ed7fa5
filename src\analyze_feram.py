import os, glob
import numbers
import numpy as np
import pandas as pd
from collections import OrderedDict
from scipy.interpolate import InterpolatedUnivariateSpline
from sklearn.neighbors import NearestNeighbors
from scipy import signal
from scipy.optimize import minimize
from scipy.signal import find_peaks, peak_widths
import matplotlib as mpl
import matplotlib.pyplot as plt
from matplotlib.pyplot import MultipleLocator
from matplotlib.ticker import MaxNLocator
from mpl_toolkits.axes_grid1 import make_axes_locatable
# user-defined modules
from utilities_feram import prefactor, parse_avg, parse_hl, parse_data
from feram_converter import read_dump

# constants
kb = 8.617333262e-5 # eV/K

def cal_suscept(temp, ui, uj, uiuj, solid='bto', unit_cells=1):
    """ Calculate susceptibility
    
    Parameters
    ----------
    temp: float
        temperature in K
    ui: np.array
        array for component i of polarization displacements in Ang.
    uj: np.array
        array for component j of polarization displacements in Ang.
    uiuj: np.array
        array for cross-terms for components i & j of polarization displacements in Ang.**2
    solid: str
        ['bto'], 'bst'
    unit_cells: int
        total number of unit cells
    
    Returns
    -------
    s: np.array
        susceptibility_{i,j}
    
    References
    ----------
    1. Documentation in example folder of [Feram](https://loto.sourceforge.net/feram/) source code
    """
    z = {'bto': 10.33, 'bst': 9.807238756} #Z_star
    a = {'bto': 3.9859, 'bst': 3.9435} #lattice constant, Ang.
    # constants
    N                = unit_cells
    k_B              = 1.3806503e-23            # J/K
    epsilon_0        = 8.854e-12                # F/m
    a_0              = a[solid]*1.0e-10         # m
    unit_cell_volume = a_0**3                   # m^-3
    Z_star           = z[solid]*1.60217733e-19  # Coulomb
    C = Z_star**2 * 1.0e-10**2 / epsilon_0 / unit_cell_volume / k_B
    # calculate susceptibility
    s = C*(uiuj - N*ui*uj)/temp
    return s

def tanh(x, x0, w0, p0, d0):
    """ tanh function """
    eps = np.finfo(float).eps
    w0 = w0 + eps
    return p0*(np.tanh((x-x0)/w0)+d0)

def fit_tanh(x, y, rmse_crit=None, display=False):
    """ Fitting to a tanh function

    Parameters
    ----------
    x: list or np.array
        x index of array
    y: list or np.array
        fitted array
    rmse_crit: float
        rmse criterion for the fitting
    display: boolean
        [False], True to display the fitting result, None to display failure results only
    
    Returns
    -------
    status: boolean
        fitting status
    rmse: float
        rmse of the fit
    y_fit: np.array
        fitting array
    x0_fit, w0_fit, p0_fit, d0_fit: float, float, float, float
        fitted parameters for DW position, half width of DW, magnitude of u, offset of u
    """
    def loss_func(params):
        x0, w0, p0, d0 = params
        return np.sum((tanh(x, x0, w0, p0, d0) - y) ** 2)

    # initial guess
    x0_init = np.mean(x)
    w0_init = np.std(x)
    p0_init = np.max(y) - np.min(y)
    d0_init = np.min(y)

    # minimize
    res = minimize(loss_func, [x0_init, w0_init, p0_init, d0_init])
    x0_fit, w0_fit, p0_fit, d0_fit = res.x
    status = res.success

    # rmse of the fit
    y_fit = tanh(x, x0_fit, w0_fit, p0_fit, d0_fit)
    rmse = np.sqrt(np.mean((y - y_fit) ** 2))

    # refit
    if not status or rmse > rmse_crit:
        print('>>> Retry')
        res = minimize(loss_func, [x0_fit, w0_fit, p0_fit, d0_fit], method='L-BFGS-B')
        x0_fit, w0_fit, p0_fit, d0_fit = res.x
        status = res.success

    # rmse of the fit
    y_fit = tanh(x, x0_fit, w0_fit, p0_fit, d0_fit)
    rmse = np.sqrt(np.mean((y - y_fit) ** 2))

    if rmse_crit!=None:
        if rmse > rmse_crit:
            print(f'[Warning]: RMSE of the fit {rmse:.4f} > {rmse_crit}')
    
    if display in [True, None]:
        if display==None and status:
            pass
        else:
            print(res)
            fig, ax = plt.subplots(figsize=(6,3), dpi=100)
            ax.plot(x, y, 'r.', label='data')
            ax.plot(x, y_fit, 'b-', label='fit')
            plt.tick_params(which='major', labelsize=14)
            ax.set_xlabel('x', fontweight='bold', fontsize=16, labelpad=None)
            ax.set_ylabel(r'$\mathbf{{\langle u_y\rangle}^x}$', fontweight='bold', fontsize=16, labelpad=None)
            ax.legend(loc=3, frameon=False, prop={'size':14, 'weight': 'normal'})
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            plt.show()
    return status, rmse, y_fit, (x0_fit, w0_fit, p0_fit, d0_fit)

def fit_tanh2(x, y, rmse_crit=None, display=False):
    """ Fitting to a tanh-tanh function
    
    Parameters
    ----------
    x: list or np.array
        x index of array
    y: list or np.array
        fitted array
    rmse_crit: float
        rmse criterion for the fitting
    display: boolean
        [False], True to display the fitting result, None to display failure results only
    
    Returns
    -------
    status: boolean
        fitting status
    rmse: float
        rmse of the fit
    y_fit: np.array
        fitting array
    x0_fit, x1_fit, w0_fit, w1_fit, p0_fit, d0_fit, d1_fit: float, float, float, float
        fitted parameters for DW position, half width of DW, magnitude of u, offset of u
    """
    def loss_func(params):
        x0, x1, w0, w1, p0, d0, d1 = params
        return np.sum((tanh(x, x0, w0, p0, d0)+tanh(x, x1, w1, p0*(-1), d1) - y) ** 2)

    # initial guess
    x0_init = np.mean(x)-1
    x1_init = np.mean(x)+1
    w0_init = np.std(x)
    w1_init = np.std(x)
    p0_init = np.max(y) - np.min(y)
    d0_init = np.min(y)
    d1_init = np.max(y)

    # minimize
    res = minimize(loss_func, [x0_init, x1_init, w0_init, w1_init, p0_init, d0_init, d1_init], method='L-BFGS-B')
    x0_fit, x1_fit, w0_fit, w1_fit, p0_fit, d0_fit, d1_fit = res.x
    status = res.success

    # rmse of the fit
    y_fit = tanh(x, x0_fit, w0_fit, p0_fit, d0_fit)+tanh(x, x1_fit, w1_fit, p0_fit*(-1), d1_fit)
    rmse = np.sqrt(np.mean((y - y_fit) ** 2))

    # refit
    if not status or rmse > rmse_crit:
        print('>>> Retry')
        res = minimize(loss_func, [x0_fit, x1_fit, w0_fit, w1_fit, p0_fit, d0_fit, d1_fit], method='L-BFGS-B')
        x0_fit, x1_fit, w0_fit, w1_fit, p0_fit, d0_fit, d1_fit = res.x
        status = res.success
    
    # rmse of the fit
    y_fit = tanh(x, x0_fit, w0_fit, p0_fit, d0_fit)+tanh(x, x1_fit, w1_fit, p0_fit*(-1), d1_fit)
    rmse = np.sqrt(np.mean((y - y_fit) ** 2))
    
    if rmse_crit!=None:
        if rmse > rmse_crit:
            print(f'[Warning]: RMSE of the fit {rmse:.4f} > {rmse_crit}')
    
    if display in [True, None]:
        if display==None and status:
            pass
        else:
            print(res)
            fig, ax = plt.subplots(figsize=(6,3), dpi=100)
            ax.plot(x, y, 'r.', label='data')
            ax.plot(x, y_fit, 'b-', label='fit')
            plt.tick_params(which='major', labelsize=14)
            ax.set_xlabel('x', fontweight='bold', fontsize=16, labelpad=None)
            ax.set_ylabel(r'$\mathbf{{\langle u_y\rangle}^x}$', fontweight='bold', fontsize=16, labelpad=None)
            ax.legend(loc=3, frameon=False, prop={'size':14, 'weight': 'normal'})
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            plt.show()
    return status, rmse, y_fit, (x0_fit, x1_fit, w0_fit, w1_fit, p0_fit, d0_fit, d1_fit)

def find_nn_coords(f_coord=None, nn=0, axis=None, nmax=1000, boundary=(10,10,10), wrap=True, invert=False, show_dist=False):
    """ Finding nearest neighbors

    Parameters
    ----------
    f_coord: str
        file for reference coordinates
    nn: int
        order of nearest neighbor
    axis: int
        [None]: along all directions, 0:x, 1:y, 2:z (only works for nn=1)
    nmax: int
        maximum number of neighbors
    boundary: tuple
        (Lx, Ly, Lz)
    wrap: bool
        wrap periodic sites into [(0,Lx),(0,Ly),(0,Lz)]
    invert: bool
        [False], True if inverted selection
    show_dist: bool
        showing the distance for specific nn

    Returns
    -------
    distance: float
        (return if show_dist==True): distance of the specific nn (u.c.)
    sites: list
        list of neighbors (x,y,z)
    """
    # load ref
    raw = parse_data(f_coord)
    sites_ref = np.vstack((raw['x'], raw['y'], raw['z'])).T
    # load sites
    lx, ly, lz = boundary
    x, y, z = np.meshgrid(np.arange(0-nn, lx+nn), np.arange(0-nn, ly+nn), np.arange(0-nn, lz+nn))
    sites_all = np.vstack( (x.ravel(), y.ravel(), z.ravel()) ).T
    # find nn
    nmax = lx*ly*lz if lx*ly*lz<1000 else nmax
    nbrs = NearestNeighbors(n_neighbors=nmax, algorithm='ball_tree').fit(sites_all)
    distances, index = nbrs.kneighbors(sites_ref, return_distance=True)
    SUM = {}
    for _ in range(len(sites_ref)):
        for i, d in zip(index[_],distances[_]):
            if d not in SUM:
                SUM[d]=[]
            if axis is not None:
                vax_ref = sites_ref[_][axis]
                if sites_all[i][axis]==vax_ref:
                    continue
            SUM[d].append(i)
    inn = sorted(SUM.keys())
    if nn==0:
        sites = sites_ref
    elif nn>=1:
        iu = np.unique(SUM[inn[nn]], axis=0)
        if len(iu)==0:
            return []
        sites = sites_all[iu]
    # wrap periodic sites
    def f_mv(x, l):
        if x<0:
            x+=l
        elif x>=l:
            x-=l
        return x
    if wrap:
        _sites = sites.copy()
        sites = []
        for x,y,z in _sites:
            _x, _y, _z = f_mv(x, lx), f_mv(y, ly), f_mv(z, lz)
            if [_x,_y,_z] not in sites:
                sites.append([_x,_y,_z])
        sites = np.array(sites)
    # invert selection
    if invert:
        x, y, z = np.meshgrid(np.arange(lx), np.arange(ly), np.arange(lz))
        sites_all_w = np.vstack( (x.ravel(), y.ravel(), z.ravel()) ).T
        _sites = sites_all_w.tolist()
        for x,y,z in sites.tolist():
            _sites.remove([x,y,z])
        sites = np.array(_sites)
    # exclude ref if nn>=1
    if nn>=1:
        _sites = np.unique(sites, axis=0).copy().tolist()
        for x,y,z in sites_ref.tolist():
            if [x,y,z] in _sites:
                _sites.remove([x,y,z])
        sites = np.array(_sites)
    # include ref if invert and nn>=1
    if invert and nn>=1:
        sites = np.array(sites.tolist()+sites_ref.tolist())
    if show_dist:
        return inn[nn], sites
    return sites



class Tc_Calculator():
    """ Calculate Tc from polarization-, strain-T

    Parameters
    ----------
    f_dipo: str
        avg file(s)

    Attributes
    ----------
    Dataset: dict
        dataset of avg file(s)
    clabel_on: boolean
        [False], True if xind is assigned
    pmode: int
        [0]: |<p>|, 1: <|p|>, #+10: +higher-order terms
    legend1_pos: int/str
        [1]: 'upper right'
    legend2_pos: tuple
        'center left' + bbox_to_anchor = [(1, 0.5)]
    """
    def __init__(self, f_avg=None, *args):
        self.files=[f_avg]
        for arg in args:
            self.files.append(arg)
        tags = ['xind', 'temp']
        tags+= ['ux', 'uy', 'uz']
        tags+= ['exx', 'eyy', 'ezz', 'eyz', 'ezx', 'exy']
        # higher-order terms
        tags+= ['usq']
        tags+= ['uxux', 'uyuy', 'uzuz', 'uyuz', 'uzux', 'uxuy']
        tags+= ['ux4', 'uy4', 'uz4', 'uyz2', 'uzx2', 'uxy2']
        tags+= ['ux6', 'uy6', 'uz6']
        tags+= ['ux4y2', 'ux4z2', 'uy4x2', 'uy4z2', 'uz4x2', 'uz4y2']
        tags+= ['uxyz2']
        self.Dataset = {_:[] for _ in tags}
        self.clabel_on = False
        self.pmode = 0
        self.legend1_pos = 1
        self.legend2_pos = (1, 0.5)

    def load_data(self, xind=None, mode=0):
        """ Loading data

        Parameters
        ----------
        xind: list
            index for each .avg file [None]
        mod: int
            [0]: |<ui>|, 1: <|ui|>, #+10: +higher-order terms

        Returns
        -------
        None

        """
        self.Dataset['xind'] = xind if xind!=None else []
        self.clabel_on = True if xind!=None else False
        self.pmode = mode
        for i, _f in enumerate(self.files):
            ary = parse_avg(_f)
            if xind==None:
                self.Dataset['xind'].append(i)
            self.Dataset['temp'].append(ary['T'])
            for _e in ['exx', 'eyy', 'ezz', 'eyz', 'ezx', 'exy']:
                self.Dataset[_e].append(ary[_e])
            if mode%10==0:
                self.Dataset['ux'].append(ary['ux'])
                self.Dataset['uy'].append(ary['uy'])
                self.Dataset['uz'].append(ary['uz'])
            elif mode%10==1:
                self.Dataset['ux'].append(ary['amx'])
                self.Dataset['uy'].append(ary['amy'])
                self.Dataset['uz'].append(ary['amz'])
            if mode>=10:
                self.Dataset['usq'].append(ary['usq'])
                for _uu in ['uxux', 'uyuy', 'uzuz', 'uyuz', 'uzux', 'uxuy']:
                    self.Dataset[_uu].append(ary[_uu])
                for _uu in ['ux4', 'uy4', 'uz4', 'uyz2', 'uzx2', 'uxy2']:
                    self.Dataset[_uu].append(ary[_uu])
                for _uu in ['ux6', 'uy6', 'uz6']:
                    self.Dataset[_uu].append(ary[_uu])
                for _uu in ['ux4y2', 'ux4z2', 'uy4x2', 'uy4z2', 'uz4x2', 'uz4y2']:
                    self.Dataset[_uu].append(ary[_uu])
                self.Dataset['uxyz2'].append(ary['uxyz2'])

    def set_legend_pos(self, index=1, pos=1):
        """ Setting legend's position

        Parameters
        ----------
        index: int
            index of legend: [1], 2
        pos: int/str/tuple
            posiiton of legend: [1] ('upper right')

        Returns
        -------
        None
        """
        if index==1:
            self.legend1_pos = pos
        elif index==2:
            pos = pos if isinstance(pos, tuple) else (1, 0.5)
            self.legend2_pos = pos

    def find_Tc(self, display=False, window=1, height=0.001, distance=1, prominence=None, width=1):
        """ Finding Tc using find_peak

        Parameters
        ----------
        display: boolean
            [False], True to plot figure
        window: int
            window size for moving average
        height: float
            criterion for peak's height
        distance: float
            criterion for distance between peaks
        prominence: float
            criterion for prominence of peak
        width: float
            criterion for peak's width

        Returns
        -------
        res: dict
            results of Tc
        """
        cal_mavg = lambda x, w: pd.Series(x).rolling(window=w, min_periods=1).mean().to_numpy()
        res = {}
        for i, _f in enumerate(self.files):
            _x = self.Dataset['xind'][i]
            temp = self.Dataset['temp'][i]
            slope = {}
            ind_max = {}
            properties = {}
            for u in ['x', 'y', 'z']:
                _u = cal_mavg(self.Dataset[f'u{u}'][i], window)
                slope[u] = np.gradient(np.abs(_u), temp)
                ind_max[u], properties[u] = find_peaks(np.abs(slope[u]), height=height, distance=distance,
                                                 prominence=prominence, width=width)
                if _x not in res:
                    res[_x]={_: [] for _ in ['x', 'y', 'z']}
                res[_x][u]+=temp[ind_max[u]].tolist()

            # display
            colors = ['r', 'g', 'b']
            ylab = r'|<ui>|' if self.pmode%10==0 else r'<|ui|>'
            if display:
                print(res[_x])
                fig,ax1 = plt.subplots()
                ax2 = ax1.twinx()
                for c, u in enumerate(['x', 'y', 'z']):
                    _u = cal_mavg(self.Dataset[f'u{u}'][i], window)
                    ax2.plot(temp, abs(slope[u]), 'k-')
                    ax1.plot(temp, np.abs(_u), colors[c]+'-', label=f'u{u}')
                    ax1.plot(temp[ind_max[u]], np.abs(_u)[ind_max[u]], colors[c]+'o')
                ax2.set_ylabel('du/dT')
                ax1.set_xlabel('T')
                ax1.set_ylabel(ylab)
                ax1.legend()
                plt.show()
        return res

    def find_strain_maxmin(self, display=False, window=1):
        """ Finding temperatures with maximum/minimum strain

        Parameters
        ----------
        display: boolean
            [False], True to show strain curves
        window: int
            window size for moving average

        Returns
        -------
        res:
            results of temperatures with max./min. strain
        """
        cal_mavg = lambda x, w: pd.Series(x).rolling(window=w, min_periods=1).mean().to_numpy()
        res = {}
        for i, _f in enumerate(self.files):
            _x = self.Dataset['xind'][i]
            temp = self.Dataset['temp'][i]
            ind_max_min = [None, None]
            e_max = -1
            e_min = 1
            for e in ['exx', 'eyy', 'ezz']:
                _e = cal_mavg(self.Dataset[e][i], window)
                i_max = np.argmax(_e)
                i_min = np.argmin(_e)
                if _e[i_max] > e_max:
                    ind_max_min[1] = i_max
                    e_max = _e[i_max]
                if _e[i_min] < e_min:
                    ind_max_min[0] = i_min
                    e_min = _e[i_min]
            res[_x] = temp[ind_max_min].tolist()

            # display
            colors = ['r', 'g', 'b']
            if display:
                print(res[_x])
                fig,ax1 = plt.subplots()
                for c, e in enumerate(['exx', 'eyy', 'ezz']):
                    _e = cal_mavg(self.Dataset[e][i], window)
                    ax1.plot(temp, _e, colors[c]+'-', label=e)
                ax1.plot(res[_x], [e_min, e_max], 'k*')
                ax1.set_xlabel('T')
                ax1.set_ylabel('strain')
                ax1.legend()
                plt.show()
        return res

    def get_Curie_Weiss_const(self, si=0, Tmin=300, Tmax=None, solid='bto', unit_cells=1, display=False):
        """ Getting Curie-Weiss constants from susceptibility

        Parameters
        ----------
        si: int
            [0]: xx & yy & zz , 1:xx, 2:yy, 3:zz
        Tmin: float
            minimum of temperature range
        Tmax: float
            maximum of temperature range
        solid: str
            ['bto'], 'bst'
        unit_cells: int
            total number of unit cells

        Returns
        -------
        res: dict
            {INDEX: namedtuple(C, Tc)}
        """
        def fit_line(x, y):
            """ 1/chi = (T-Tc)/C """
            coef = np.polyfit(x, y, 1)
            x_fit = np.linspace(np.min(x),np.max(x),1000)
            y_fit = x_fit*coef[0]+coef[1]
            return coef, x_fit, y_fit
        if si in [1, 2, 3,]:
            sind = [si-1]
        elif si==0:
            sind = [0,1,2]
        # calculate susceptibility
        res = {}
        for i, _f in enumerate(self.files):
            data = self.Dataset
            _x = data['xind'][i]
            temp = data['temp'][i]
            ux, uy, uz = [data[_][i] for _ in ('ux', 'uy', 'uz')]
            ui_ary = [ux, uy, uz, uy, uz, ux]
            uj_ary = [ux, uy, uz, uz, ux, uy]
            uu_ary = [data[_][i] for _ in ('uxux', 'uyuy', 'uzuz', 'uyuz', 'uzux', 'uxuy')]
            SS = []
            for si in [1, 2, 3,]:
                ss = cal_suscept(temp, ui_ary[si-1], uj_ary[si-1], uu_ary[si-1], solid, unit_cells)
                SS.append(ss)
            # select data
            condi = (temp>=Tmin)
            if Tmax is not None:
                condi *= (temp<=Tmax)
            T = temp[condi]
            S = [_ss[condi] for _ss in SS]
            # fit line
            if display:
                fig, ax = plt.subplots(figsize=(5,4), dpi=100)
                colors = 'rgb'
                labels = [r'$\chi_{xx}$', r'$\chi_{yy}$', r'$\chi_{zz}$']
            ans = []
            for _si in sind:
                _ss = S[_si]
                coef, x_fit, y_fit = fit_line(T, 1/_ss)
                C = 1/coef[0]
                Tc = -1*coef[1]/coef[0]
                CW = namedtuple('xyz'[_si]*2, 'C Tc')
                ans.append(CW(C, Tc))
                if display:
                    ax.plot(T, 1/_ss, 's', color=colors[_si], label=labels[_si])
                    ax.plot(x_fit, y_fit, '--', color=colors[_si])
                    formula = f'y={coef[0]:.2e}x+({coef[1]:.2e}), Tc={Tc:.1f}'
                    xpos = np.min(x_fit)
                    ymin, ymax = np.min(y_fit), np.max(y_fit)
                    ypos = ymax-(ymax-ymin)*0.1*_si
                    plt.text(xpos, ypos, formula, size=12, color=colors[_si])
            res[i] = ans
            if display:
                ax.tick_params(labelsize=14)
                ax.set_xlabel('T (K)', size=16)
                ax.set_ylabel(r'$1/\chi$', size=16)
                ax.legend(loc='lower right', prop={'size':14})
        return res

    def plot_PT(self, ui=3, solid='bto', xlim=None, ylim=None, window=1, overlap=True,
                figsize=None, fig_out=None):
        """ Plotting P-T curves

        Parameters
        ----------
        ui: int
            1:x, 2:y, [3]:z
        solid: str
            ['bto'], 'bst'
        xlim: list
            [xlo, xhi], x range of plotting
        ylim: list
            [ylo, yhi], y range of plotting
        window: int
            window size for moving average
        overlap: boolean
            [True], False to not overlap multiple curves
        figzie: tuple
            (xsize, ysize) of the output figure
        fig_out: str
            file name of the output figure [None]

        Returns
        -------
        None
        """
        cal_mavg = lambda x, w: pd.Series(x).rolling(window=w, min_periods=1).mean().to_numpy()
        ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        colors = 'rbgcymk'
        if ui in [0, 1, 2]:
            uind = [ind[ui]]
            linestyles = [['--', ':', '-'][ui]]
            markers = [['', '', '.'][ui]]
            clabels = [[r'$\it{i:x}$', r'$\it{i:y}$', r'$\it{i:z}$'][ui]]
        elif ui==3:
            uind = ['ux', 'uy', 'uz']
            linestyles = ['--', ':', '-']
            markers = ['', '', '.']
            clabels = [r'$\it{i:x}$', r'$\it{i:y}$', r'$\it{i:z}$']

        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        for i, _f in enumerate(self.files):
            _x = self.Dataset['xind'][i]
            temp = self.Dataset['temp'][i]
            c = colors[i%7]
            if ui in [0, 1, 2]:
                u_ary = self.Dataset[ind[ui]][i]*prefactor(solid)
                u_ary = cal_mavg(u_ary, window)
                ax.plot(temp, np.abs(u_ary), c+markers[0]+linestyles[0], label=ind[ui])
            elif ui==3:
                for _ui in [0, 1, 2]:
                    u_ary = self.Dataset[ind[_ui]][i]*prefactor(solid)
                    u_ary = cal_mavg(u_ary, window)
                    ax.plot(temp, np.abs(u_ary), c+markers[_ui]+linestyles[_ui], label=ind[_ui])

            # limits
            ax.set_xlim(xlim)
            ax.set_ylim(ylim)

            # legend
            if not self.clabel_on:
                ax.legend(loc=self.legend1_pos, frameon=False, prop={'size':18, 'weight': 'normal'})

            # labels
            ylab = r'$|\langle P_i\rangle|$' if self.pmode%10==0 else r'$\langle|P_i|\rangle$'
            ax.set_xlabel("T (K)",fontsize=20, fontweight='normal', labelpad=12)
            ax.set_ylabel(fr"{ylab} ($\mu C/cm^{2}$)", fontsize=20, fontweight='normal', labelpad=12)

            # ticks
            plt.xticks(fontsize=20, fontweight='normal')
            plt.yticks(fontsize=20, fontweight='normal')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.xaxis.set_tick_params(width=2)
            ax.yaxis.set_tick_params(width=2)
            ax.spines['bottom'].set_linewidth(2)
            ax.spines['left'].set_linewidth(2)

            plt.tight_layout()
            if not overlap:
                plt.show()
                if fig_out!=None:
                    fig.savefig(_x+'_'+fig_out, dpi=fig.dpi)

        # custom legend
        if self.clabel_on:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_], lw=2) for _ in range(len(self.files))]
            leg=ax.legend(custom_lines, self.Dataset['xind'], loc=self.legend1_pos, prop={'size':18, 'weight': 'normal'})
            custom_lines_1 = [Line2D([0], [0], linestyle=lsty, marker=mk, color='k', lw=2) for lsty, mk in zip(linestyles, markers)]
            ax.legend(custom_lines_1, clabels, loc='center left', frameon=False,
                      prop={'size':18, 'weight': 'normal'}, bbox_to_anchor=self.legend2_pos)
            plt.gca().add_artist(leg)

        if overlap and fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')

    def plot_StT(self, ei=0, solid='bto', xlim=None, ylim=None, window=1, overlap=True,
                figsize=None, fig_out=None):
        """ Plotting Strain-T curves

        Parameters
        ----------
        ei: int
            [0]: xx & yy & zz , 1:xx, 2:yy, 3:zz, 4:yz, 5:xz, 6:xy, [7]: yz & xz & xy
        solid: str
            ['bto'], 'bst'
        xlim: list
            [xlo, xhi], x range of plotting
        ylim: list
            [ylo, yhi], y range of plotting
        window: int
            window size for moving average
        overlap: boolean
            [True], False to not overlap multiple curves
        figzie: tuple
            (xsize, ysize) of the output figure
        fig_out: str
            file name of the output figure [None]

        Returns
        -------
        None
        """
        cal_mavg = lambda x, w: pd.Series(x).rolling(window=w, min_periods=1).mean().to_numpy()
        ind = {1: 'exx', 2: 'eyy', 3: 'ezz', 4: 'eyz', 5: 'ezx', 6: 'ezy'}
        colors = 'rbgcymk'
        if ei in [1, 2, 3, 4, 5, 6]:
            eind = [ind[ei]]
            linestyles = [['--', ':', '-'][ei%3-1]]
            markers = [['', '', '.'][ei%3-1]]
            clabels = [[r'$\it{e_{xx}}$',r'$\it{e_{yy}}$',r'$\it{e_{zz}}$',r'$\it{e_{yz}}$',r'$\it{e_{xz}}$',r'$\it{e_{xy}}$'][ei-1]]
        elif ei==0:
            eind = ['exx', 'eyy', 'ezz']
            linestyles = ['--', ':', '-']
            markers = ['', '', '.']
            clabels = [r'$\it{e_{xx}}$',r'$\it{e_{yy}}$',r'$\it{e_{zz}}$']
        elif ei==7:
            eind = ['eyz', 'exz', 'exy']
            linestyles = ['--', ':', '-']
            markers = ['', '', '.']
            clabels = [r'$\it{e_{yz}}$',r'$\it{e_{xz}}$',r'$\it{e_{xy}}$']

        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        for i, _f in enumerate(self.files):
            _x = self.Dataset['xind'][i]
            temp = self.Dataset['temp'][i]
            c = colors[i%7]
            if ei in [1, 2, 3, 4, 5, 6]:
                delta = 0 if solid==None else 1
                ee_ary = (self.Dataset[ind[ei]][i] + delta)*prefactor(solid, mode='a')
                ee_ary = cal_mavg(ee_ary, window)
                ax.plot(temp, np.abs(ee_ary), c+markers[0]+linestyles[0], label=ind[ei])
            elif ei==0:
                for _ei in [1, 2, 3]:
                    delta = 0 if solid==None else 1
                    ee_ary = (self.Dataset[ind[_ei]][i] + delta)*prefactor(solid, mode='a')
                    ee_ary = cal_mavg(ee_ary, window)
                    ax.plot(temp, np.abs(ee_ary), c+markers[_ei-1]+linestyles[_ei-1], label=ind[_ei])
            elif ei==7:
                for _ei in [4, 5, 6]:
                    delta = 0 if solid==None else 1
                    ee_ary = (self.Dataset[ind[_ei]][i] + delta)*prefactor(solid, mode='a')
                    ee_ary = cal_mavg(ee_ary, window)
                    ax.plot(temp, np.abs(ee_ary), c+markers[_ei-4]+linestyles[_ei-4], label=ind[_ei])

            # limits
            ax.set_xlim(xlim)
            ax.set_ylim(ylim)

            # legend
            if not self.clabel_on:
                ax.legend(loc=self.legend1_pos, frameon=False, prop={'size':18, 'weight': 'normal'})

            # labels
            ylab = r"Strain" if solid==None else r"Lattice constants ($\AA$)"
            ax.set_xlabel("T (K)",fontsize=20, fontweight='normal', labelpad=12)
            ax.set_ylabel(ylab, fontsize=20, fontweight='normal', labelpad=12)

            # ticks
            plt.xticks(fontsize=20, fontweight='normal')
            plt.yticks(fontsize=20, fontweight='normal')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_linewidth(2)
            ax.spines['left'].set_linewidth(2)
            ax.xaxis.set_tick_params(width=2)
            ax.yaxis.set_tick_params(width=2)

            plt.tight_layout()
            if not overlap:
                plt.show()
                if fig_out!=None:
                    fig.savefig(_x+'_'+fig_out, dpi=fig.dpi)

        # custom legend
        if self.clabel_on:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_], lw=2) for _ in range(len(self.files))]
            leg=ax.legend(custom_lines, self.Dataset['xind'], loc=self.legend1_pos, prop={'size':18, 'weight': 'normal'})
            custom_lines_1 = [Line2D([0], [0], linestyle=lsty, marker=mk, color='k', lw=2) for lsty, mk in zip(linestyles, markers)]
            if solid==None:
                cleg = clabels
            else:
                if ei==0:
                    cleg = [r'$\it{a}$',r'$\it{b}$',r'$\it{c}$']
                elif ei==7:
                    cleg = [r'$\it{bc}$',r'$\it{ac}$',r'$\it{ab}$']
                elif ei in [1,2,3,4,5,6]:
                    cleg = [[r'$\it{a}$',r'$\it{b}$',r'$\it{c}$',r'$\it{bc}$',r'$\it{ac}$',r'$\it{ab}$'][ei-1]]
            ax.legend(custom_lines_1, cleg ,loc='center left', bbox_to_anchor=self.legend2_pos,
                      frameon=False, prop={'size':18, 'weight': 'normal'})
            plt.gca().add_artist(leg)
        if overlap and fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')

    def plot_SusT(self, si=0, solid='bto', unit_cells=1, inverse=False, xlim=None, ylim=None, window=1, overlap=True,
                  figsize=None, fig_out=None):
        """ Plotting Suscept-T curves

        Parameters
        ----------
        si: int
            [0]: xx & yy & zz , 1:xx, 2:yy, 3:zz, 4:yz, 5:xz, 6:xy, [7]: yz & xz & xy
        solid: str
            ['bto'], 'bst'
        unit_cells: int
            total number of unit cells
        inverse: bool
            inverse susceptibility [False]
        xlim: list
            [xlo, xhi], x range of plotting
        ylim: list
            [ylo, yhi], y range of plotting
        window: int
            window size for moving average
        overlap: boolean
            [True], False to not overlap multiple curves
        figzie: tuple
            (xsize, ysize) of the output figure
        fig_out: str
            file name of the output figure [None]

        Returns
        -------
        None
        """
        if self.pmode!=10:
            print('[Error]: To calculate susceptibility, `load_data` must use `mode=10`!')
            return
        cal_mavg = lambda x, w: pd.Series(x).rolling(window=w, min_periods=1).mean().to_numpy()
        ind = {1: 'sxx', 2: 'syy', 3: 'szz', 4: 'syz', 5: 'szx', 6: 'sxy'}
        colors = 'rbgcymk'
        if si in [1, 2, 3, 4, 5, 6]:
            sind = [si-1]
            linestyles = [['--', ':', '-'][si%3-1]]
            markers = [['', '', '.'][si%3-1]]
            clabels = [[r'$\it{\chi_{xx}}$',r'$\it{\chi_{yy}}$',r'$\it{\chi_{zz}}$',r'$\it{\chi_{yz}}$',r'$\it{\chi_{zx}}$',r'$\it{\chi_{xy}}$'][si-1]]
        elif si==0:
            sind = [0,1,2]
            linestyles = ['--', ':', '-']
            markers = ['', '', '.']
            clabels = [r'$\it{\chi_{xx}}$',r'$\it{\chi_{yy}}$',r'$\it{\chi_{zz}}$']
        elif si==7:
            sind = [3,4,5]
            linestyles = ['--', ':', '-']
            markers = ['', '', '.']
            clabels = [r'$\it{\chi_{yz}}$',r'$\it{\chi_{xz}}$',r'$\it{\chi_{xy}}$']

        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        for i, _f in enumerate(self.files):
            data = self.Dataset
            _x = data['xind'][i]
            temp = data['temp'][i]
            ux, uy, uz = [data[_][i] for _ in ('ux', 'uy', 'uz')]
            ui_ary = [ux, uy, uz, uy, uz, ux]
            uj_ary = [ux, uy, uz, uz, ux, uy]
            uu_ary = [data[_][i] for _ in ('uxux', 'uyuy', 'uzuz', 'uyuz', 'uzux', 'uxuy')]

            c = colors[i%7]
            if si in [1, 2, 3, 4, 5, 6]:
                ss = cal_suscept(temp, ui_ary[si-1], uj_ary[si-1], uu_ary[si-1], solid=solid, unit_cells=unit_cells)
                ss = cal_mavg(ss, window) if not inverse else 1/cal_mavg(ss, window)
                ax.plot(temp, np.abs(ss), c+markers[0]+linestyles[0], label=ind[si])
            elif si==0:
                for _si in [1, 2, 3]:
                    ss = cal_suscept(temp, ui_ary[_si-1], uj_ary[_si-1], uu_ary[_si-1], solid=solid, unit_cells=unit_cells)
                    ss = cal_mavg(ss, window) if not inverse else 1/cal_mavg(ss, window)
                    ax.plot(temp, np.abs(ss), c+markers[_si-1]+linestyles[_si-1], label=ind[_si])
            elif si==7:
                for _si in [4, 5, 6]:
                    ss = cal_suscept(temp, ui_ary[_si-1], uj_ary[_si-1], uu_ary[_si-1], solid=solid, unit_cells=unit_cells)
                    ss = cal_mavg(ss, window) if not inverse else 1/cal_mavg(ss, window)
                    ax.plot(temp, np.abs(ss), c+markers[_si-4]+linestyles[_si-4], label=ind[_si])

            # limits
            ax.set_xlim(xlim)
            ax.set_ylim(ylim)

            # legend
            if not self.clabel_on:
                ax.legend(loc=self.legend1_pos, frameon=False, prop={'size':18, 'weight': 'normal'})

            # labels
            ylab = r"$\chi$ (a.u.)" if not inverse else r"$1/\chi$ (a.u.)"
            ax.set_xlabel("T (K)",fontsize=20, fontweight='normal', labelpad=12)
            ax.set_ylabel(ylab, fontsize=20, fontweight='normal', labelpad=12)

            # ticks
            plt.xticks(fontsize=20, fontweight='normal')
            plt.yticks(fontsize=20, fontweight='normal')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['bottom'].set_linewidth(2)
            ax.spines['left'].set_linewidth(2)
            ax.xaxis.set_tick_params(width=2)
            ax.yaxis.set_tick_params(width=2)

            plt.tight_layout()
            if not overlap:
                plt.show()
                if fig_out!=None:
                    fig.savefig(_x+'_'+fig_out, dpi=fig.dpi)

        # custom legend
        if self.clabel_on:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_], lw=2) for _ in range(len(self.files))]
            leg=ax.legend(custom_lines, self.Dataset['xind'], loc=self.legend1_pos, prop={'size':18, 'weight': 'normal'})
            custom_lines_1 = [Line2D([0], [0], linestyle=lsty, marker=mk, color='k', lw=2) for lsty, mk in zip(linestyles, markers)]
            ax.legend(custom_lines_1, clabels ,loc='center left', bbox_to_anchor=self.legend2_pos,
                      frameon=False, prop={'size':18, 'weight': 'normal'})
            plt.gca().add_artist(leg)
        if overlap and fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')



class HL_Analyzer():
    """ Analyze hl files from field simulations

    Parameters
    ----------
    f_hl: str
        hl file(s)
    
    Attributes
    ----------
    Dataset: dict
    labels: list
    """
    def __init__(self, f_hl=None, *args):
        self.files=[f_hl]
        for i, arg in enumerate(args):
            self.files.append(arg)
        self.Dataset = None
        self.labels = None

    def load_data(self, labels=None, repeat=1):
        """ Loading data
        
        Parameters
        ----------
        labels: list
            specify labels for different groups of hl files
        repeat: int
            repeat n time(s) for each label (default: 1)
        
        Returns
        -------
        None
        """
        Dataset = OrderedDict()
        if labels is None:
            labels = [0]*len(self.files)
        else:
            labels = labels if repeat==1 else sum([[lab]*repeat for lab in labels],[])
        if len(labels)!=len(self.files):
            print('[Warning]: inconsistent number of labels and files!')
        for i, _f in zip(labels, self.files):
            if i not in Dataset:
                Dataset[i] = []
            hl_ary = parse_hl(fname=_f)
            Dataset[i].append(hl_ary)
        self.Dataset = Dataset
        self.labels = labels

    def cal_derivative(self, xaxis='Ex', yaxis='ux', w_len=1, peak_min=None, display=False):
        """ Calculating derivatives
        
        Parameters
        ----------
        xaxis: str
            item name for x-axis (e.g. 'Ex', 'Ey', 'Ez')
        yaxis: str
            item name for y-axis (e.g. 'ux', 'uy', 'uz', 'exx', 'eyy', 'ezz')
        w_len: int
            the length of smoothing window (odd number)
        peak_min: float
            the lower bound for the prominence of peaks (default: None, auto)
        display: bool
            display derivative plot
        
        Returns
        -------
        D: dict
            results of derivatives
        S: dict
            results of sorted arrays
        """
        D = {_:[] for _ in self.Dataset}
        S = {_:[] for _ in self.Dataset}
        for i,key in enumerate(self.Dataset):
            hl_data = self.Dataset[key]
            for hl in hl_data:
                x_ary = hl[xaxis]
                y_ary = hl[yaxis]
                # sort ascend
                ind = np.argsort(x_ary)
                x_sort = x_ary[ind]
                y_sort = y_ary[ind]
                # derivative
                slope = np.gradient(y_sort, x_sort)
                # smoothing (https://scipy-cookbook.readthedocs.io/items/SignalSmooth.html)
                w_len = w_len if w_len%2==1 else w_len+1
                w = np.blackman(w_len)
                slope_m = np.convolve(w/w.sum(), slope, mode='same')
                D[key].append(slope_m)
                S[key].append( (x_sort, y_sort) )
        if display:
            colors='rgbcmyk'
            for i,key in enumerate(D):
                fig, ax = plt.subplots(2,1, figsize=(5,4), sharex=True, dpi=100)
                for j,((xx,yy),ss) in enumerate(zip(S[key],D[key])):
                    ax[0].plot(xx, yy, '-', color=colors[j%7], label=key, lw=2)
                    ax[1].plot(xx, ss, '.-', color=colors[j%7], label=key, lw=1)
                    # find peaks
                    smean, sdev = np.mean(ss), np.std(ss)
                    _pm = sdev*0.25 if peak_min is None else peak_min
                    _h = np.min(ss)
                    peaks, properties = find_peaks(ss, prominence=(_pm,None),
                                                   height=_h, width=1)
                    ax[1].plot(xx[peaks], ss[peaks], "x", color='k', ms=8)
                    ax[1].axhline(y=smean, ls='--', color=colors[j%7], lw=1)
                    ax[1].axhline(y=smean+sdev, ls=':', color=colors[j%7], lw=1)
                ax[0].axhline(y = 0, color = 'k', linestyle = '-', lw=1)
                ax[0].axvline(x = 0, color = 'k', linestyle = '-', lw=1)
                ax[1].set_xlabel(xaxis)
                ax[0].set_ylabel(yaxis)
                ax[1].set_ylabel(f"D[{yaxis}]")
                plt.show()
        return D, S

    def find_Ec(self, E_axis=0, P_axis=0, w_len=1, peak_min=None, E_scale=None, P_scale='bto'):
        """ Finding coercive fields
        
        Parameters
        ----------
        E_axis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        P_axis: int
            y-axis ([0]: Px, 1: Py, 2: Pz)
        w_len: int
            the length of smoothing window (odd number)
        peak_min: float
            the lower bound for the prominence of peaks (default: None, auto)
        E_scale: float
            scaling factor for E
        P_scale: str
            scaling factor for P (default: 'bto')
        
        Returns
        -------
        Ec: dict
            results of Ec
        """
        E_ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        P_ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        E_scale = E_scale if E_scale!=None else 1e5*1e-3 # MV/cm
        P_scale = P_scale if not isinstance(P_scale, str) else prefactor(solid=P_scale) # uC/cm^2
        # calculate derivatives
        D, S = self.cal_derivative(xaxis=E_ind[E_axis], yaxis=P_ind[P_axis], w_len=w_len)
        Ec = {}
        for i,key in enumerate(D):
            if key not in Ec:
                Ec[key] = []
            sl_data = D[key]
            hl_data = S[key]
            for slope,hl in zip(sl_data,hl_data):
                smean, sdev = np.mean(slope), np.std(slope)
                _pm = sdev*0.25 if peak_min is None else peak_min
                _h = np.min(slope)
                cind, properties = find_peaks(slope, prominence=(_pm,None),
                                              height=_h, width=1)
                Ec[key]+=list(hl[0][cind]*E_scale)
            Ec[key] = sorted(Ec[key])
        return Ec

    def find_Ebias(self, E_axis=0, P_axis=0, w_len=1, peak_min=None, E_scale=None, P_scale='bto'):
        """ Finding bias fields
        
        Parameters
        ----------
        E_axis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        P_axis: int
            y-axis ([0]: Px, 1: Py, 2: Pz)
        w_len: int
            the length of smoothing window (odd number)
        peak_min: float
            the lower bound for the prominence of peaks (default: None, auto)
        E_scale: float
            scaling factor for E
        P_scale: str
            scaling factor for P (default: 'bto')
        
        Returns
        -------
        Ebias: dict
            results of Ebias
        """
        E_ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        P_ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        E_scale = E_scale if E_scale!=None else 1e5*1e-3 # MV/cm
        P_scale = P_scale if not isinstance(P_scale, str) else prefactor(solid=P_scale) # uC/cm^2
        # find Ec
        Ec = self.find_Ec(E_axis=E_axis, P_axis=P_axis, w_len=w_len, peak_min=peak_min,
                          E_scale=E_scale, P_scale=P_scale)
        Ebias = {}
        for i,key in enumerate(Ec):
            if key not in Ebias:
                Ebias[key] = []
            ec_data = Ec[key]
            Ebias[key].append(np.mean(ec_data))
        return Ebias

    def find_Pr(self, E_axis=0, P_axis=0, Ebias=0, E_scale=None, P_scale='bto'):
        """ Finding remnant polarizations
        
        Parameters
        ----------
        E_axis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        P_axis: int
            y-axis ([0]: Px, 1: Py, 2: Pz)
        Ebias: float
            Ebias for finding Pr
        E_scale: float
            scaling factor for E
        P_scale: str
            scaling factor for P (default: 'bto')
        
        Returns
        -------
        Pr: dict
            results of Pr
        """
        E_ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        P_ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        E_scale = E_scale if E_scale!=None else 1e5*1e-3 # MV/cm
        P_scale = P_scale if not isinstance(P_scale, str) else prefactor(solid=P_scale) # uC/cm^2
        Pr = {}
        for i,key in enumerate(self.Dataset):
            if key not in Pr:
                Pr[key] = []
            hl_data = self.Dataset[key]
            for hl in hl_data:
                e_ary = hl[E_ind[E_axis]]
                p_ary = hl[P_ind[P_axis]]
                # sort ascend
                ind = np.argsort(e_ary)
                e_sort = e_ary[ind]*E_scale
                p_sort = p_ary[ind]*P_scale
                Pr[key].append(p_sort[np.argmin(np.abs(e_sort-Ebias))])
        return Pr

    def cal_PE_integral(self, E_axis=0, P_axis=0, E_scale=None, P_scale='bto', E_ref=0, reverse=False, display=False):
        """ Calculating PE integral
        
        Parameters
        ----------
        E_axis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        P_axis: int
            y-axis ([0]: Px, 1: Py, 2: Pz)
        E_scale: float
            scaling factor for E
        P_scale: str
            scaling factor for P (default: 'bto')
        E_ref: float
            reference point for E (default: 0)
        reverse: bool
            reverse P/E axes (default: False)
        display: bool
            display plotting with filled color

        Returns
        -------
        res: dict
            results of PE integral
        EPm: dict
            list of [EPmin, EPmax]
        """
        E_ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        P_ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        E_scale = E_scale if E_scale!=None else 1e5*1e-3 # MV/cm
        P_scale = P_scale if not isinstance(P_scale, str) else prefactor(solid=P_scale) # uC/cm^2
        r_pm = -1 if reverse else 1
        EAX = E_axis if isinstance(E_axis, list) else [E_axis]*len(self.Dataset)
        PAX = P_axis if isinstance(P_axis, list) else [P_axis]*len(self.Dataset)
        # find Pmax, Pmin
        res = {}
        EPm = {}
        for i,key in enumerate(self.Dataset):
            hl_data = self.Dataset[key]
            EPmin = {}
            EPmax = {}
            for hl in hl_data:
                e_ary = hl[E_ind[EAX[i]]]*r_pm
                p_ary = hl[P_ind[PAX[i]]]*r_pm
                # sort ascend
                ind = np.argsort(e_ary)
                e_sort = e_ary[ind]*E_scale
                p_sort = p_ary[ind]*P_scale
                # select E>=E_ref
                if np.all(e_sort>E_ref*r_pm):
                    print('[Warning]: E_ref is outside the range of E-field data points!')
                e_s = e_sort[e_sort>=E_ref*r_pm]
                p_s = p_sort[e_sort>=E_ref*r_pm]
                for _e,_p in zip(e_s,p_s):
                    _e = _e*r_pm
                    # Pmin
                    if _e not in EPmin:
                        EPmin[_e] = _p
                    else:
                        if EPmin[_e]>_p:
                            EPmin[_e] = _p
                    # Pmax
                    if _e not in EPmax:
                        EPmax[_e] = _p
                    else:
                        if EPmax[_e]<_p:
                            EPmax[_e] = _p
            EPmin = {_:EPmin[_]*r_pm for _ in EPmin}
            EPmax = {_:EPmax[_]*r_pm for _ in EPmax}
            EPm[key] = (EPmin, EPmax)
            # calculate work
            E = np.array(list(EPmin.keys()))
            ind = np.argsort(E)
            E = E[ind]
            Pmin = np.array(list(EPmin.values()))[ind]
            Pmax = np.array(list(EPmax.values()))[ind]
            w_gain = np.trapz(E-E_ref, x=Pmax)
            w_loss = np.trapz(E-E_ref, x=Pmin)-np.trapz(E-E_ref, x=Pmax)
            res[key] = (w_gain*r_pm, w_loss*r_pm)
        if display:
            for i,key in enumerate(self.Dataset):
                fig, ax = plt.subplots(figsize=(4,3), dpi=100)
                hl_data = self.Dataset[key]
                for hl in hl_data:
                    E0 = hl[E_ind[E_axis]]*E_scale
                    P0 = hl[P_ind[P_axis]]*P_scale
                    ax.plot(E0, P0, 'k-', lw=2)
                EPmin, EPmax = EPm[key]
                E = np.array(list(EPmin.keys()))
                ind = np.argsort(E)
                E = E[ind]
                Pmin = np.array(list(EPmin.values()))[ind]
                Pmax = np.array(list(EPmax.values()))[ind]
                if reverse:
                    ax.fill_between(E, Pmin, Pmax, color='k', alpha=0.3)
                    ax.fill_between(E, np.ones(len(E))*np.min(Pmax), Pmax, color='b', alpha=0.7)
                    print('Eloss:', np.trapz(Pmin-Pmax, x=E))
                    print('Egain:', np.trapz(Pmax-np.ones(len(E))*np.min(Pmax), x=E))
                else:
                    ax.fill_between(E, Pmin, Pmax, color='k', alpha=0.3)
                    ax.fill_between(E, Pmax, np.ones(len(E))*np.max(Pmax), color='b', alpha=0.7)
                    print('Eloss:', np.trapz(Pmax-Pmin, x=E))
                    print('Egain:', np.trapz(np.ones(len(E))*np.max(Pmax)-Pmax, x=E))
                ax.axhline(y = 0, color = 'k', linestyle = '-', lw=1)
                ax.axvline(x = 0, color = 'k', linestyle = '-', lw=1)
                m='-' if r_pm==-1 else ''
                ax.set_xlabel(f'{m}E (MV/cm)')
                ax.set_ylabel(fr'{m}P ($\mu C/cm^2$)')
                plt.show()
        return res, EPm

    def plot_PE(self, xaxis=0, yaxis=0, solid=None, xlim=None, ylim=None, fill_mode=0, fig_out=None):
        """ Plotting P-E loops
        
        Parameters
        ----------
        xaxis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        yaxis: int
            y-axis ([0]: ux, 1: uy, 2: uz)
        solid: str
            prefactor for u (None, 'bto', 'bst')
        xlim: list
            [xlo, xhi]
        ylim: list
            [ylo, yhi] 
        fill_mode: int
            [0]: no fill, 1: fill P+, -1: fill P-
        fig_out: str
            output figure

        Returns
        -------
        None
        """
        f_slope = lambda x: 1*( np.max(np.abs(np.gradient(x)))>np.finfo(float).eps )
        factor = prefactor(solid=solid) if solid!=None else 1
        colors = 'rgbcmyk'
        # xdata
        ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        if xaxis in [0,1,2]:
            xind = ind[xaxis]
            XIND = [xind]*len(self.Dataset)
        elif isinstance(xaxis, list):
            XIND = [ind[_] for _ in xaxis]
        # ydata
        ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        if yaxis in [0,1,2]:
            yind = [ind[yaxis]]
            ylabels = ['P'+'xyz'[yaxis]]
            ystyles = ['-']
            YIND = [yind]*len(self.Dataset)
        elif yaxis==3:
            yind = ['ux', 'uy', 'uz']
            ylabels = ['Px', 'Py', 'Pz']
            ystyles = ['--', ':', '-']
            YIND = [yind]*len(self.Dataset)
        elif isinstance(yaxis, list):
            YIND = [[ind[_]] for _ in yaxis]
            ylabels = ['P'+'xyz'[yaxis[0]]]
            ystyles = ['-']

        fig,ax = plt.subplots(figsize=(7,4), dpi=150)
        fw = 'normal'
        for i,key in enumerate(self.Dataset):
            hl_data = self.Dataset[key]
            for hl in hl_data:
                efxyz_ary = [hl[_]*1e5 for _ in ('Ex','Ey','Ez')] # kV/cm
                ef_ary = hl[XIND[i]]*1e5 # kV/cm
                u_ary = [hl[_]*factor for _ in YIND[i]] # Ang. or uC/cm^2
                efdir = 'xyz'[xaxis]
                for j,u in enumerate(u_ary):
                    ax.plot(ef_ary, u, linestyle=ystyles[j], color=colors[i%7], label=ylabels[j], lw=2)

            # limits
            if xlim!=None:
                plt.xlim(xlim)
            if ylim!=None:
                plt.ylim(ylim)

            # labels
            plt.xlabel(fr"$E_{efdir}$ (kV/cm)", fontsize=20, fontweight=fw, labelpad=10)
            if factor==1:
                ylabel = r'$u$' if yaxis==3 else fr"$u_{'xyz'[yaxis]}$"
                plt.ylabel(ylabel+r" ($\AA$)", fontsize=20, fontweight=fw, labelpad=10)
            else:
                ylabel = r'$P$' if yaxis==3 else fr"$P_{'xyz'[yaxis]}$"
                plt.ylabel(ylabel+r" (${\mu C/cm^{2}}$)", fontsize=20, fontweight=fw, labelpad=10)

            # legend
            if self.labels==None:
                ax.legend(loc=0, frameon=False, labelspacing=0.2, prop={'size':16, 'weight': fw})

            # ticks
            if xlim!=None:
                xmax = np.max(np.abs(xlim))//50*50
                xticks = np.linspace(-xmax, xmax, xmax//50*2+1)
                ichk = np.where(xticks==0)[0][0]%2
                xticklabels = [int(xt) if _%2==ichk else '' for _,xt in enumerate(xticks)]
                ax.set_xticks(xticks)
                ax.set_xticklabels(xticklabels, fontsize=18, fontweight=fw)
            if ylim!=None:
                ymax = np.max(np.abs(ylim))//5*5
                yticks = np.linspace(-ymax, ymax, ymax//5*2+1)
                ichk = np.where(yticks==0)[0][0]%2
                yticklabels = [int(yt) if _%2==ichk else '' for _,yt in enumerate(yticks)]
                ax.set_yticks(yticks)
                ax.set_yticklabels(yticklabels, fontsize=18, fontweight=fw)
            plt.xticks(fontsize=18, fontweight=fw)
            plt.yticks(fontsize=18, fontweight=fw)

        # fill PE
        hatchs = ['//','\\\\']
        if fill_mode!=0:
            rev = True if fill_mode==-1 else False
            _,EPm = self.cal_PE_integral(E_axis=xaxis, P_axis=yaxis, E_scale=1e5, P_scale='bst', E_ref=0, reverse=rev)
            for i,key in enumerate(EPm):
                EPmin, EPmax = EPm[key]
                E = np.array(list(EPmax.keys()))
                ind = np.argsort(E)
                E = E[ind]
                Pmin = np.array(list(EPmin.values()))[ind]
                Pmax = np.array(list(EPmax.values()))[ind]
                if fill_mode==-1:
                    ax.fill_between(E, Pmax, Pmin, color='k', alpha=0.2)
                    ax.fill_between(E, np.ones(len(E))*np.min(Pmax), Pmax, color=colors[i%7], alpha=0.3, hatch=hatchs[i//2%2])
                else:
                    ax.fill_between(E, Pmin, Pmax, color='k', alpha=0.2)
                    ax.fill_between(E, Pmax, np.ones(len(E))*np.max(Pmax), color=colors[i%7], alpha=0.3, hatch=hatchs[i//2%2])

        # custom legends
        if self.labels!=None:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[i%7], lw=2) for i in range(len(self.Dataset.keys()))]
            leg=ax.legend(custom_lines, self.Dataset.keys(), frameon=False, labelspacing=0.25,
                          loc='center left', bbox_to_anchor=(1, 0.5), prop={'size':18, 'weight': fw})

        # zero-axes
        plt.axhline(y=0, color='k', linestyle='-', lw=0.5, zorder=0.5)
        plt.axvline(x=0, color='k', linestyle='-', lw=0.5, zorder=0.5)

        plt.tight_layout()
        if fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')

    def plot_StE(self, xaxis=0, yaxis=0, y0_shift=False, xlim=None, ylim=None, fig_out=None):
        """ Plotting Strain-E loops
        
        Parameters
        ----------
        xaxis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        yaxis: int
            y-axis ([0]: exx, 1: eyy, 2: ezz)
        y0_shift: bool
            shift strain(E=0) to 0 (default: False)
        xlim: list
            [xlo, xhi] (default: None)
        ylim: list
            [ylo, yhi] (default: None)

        Returns
        -------
        None
        """
        f_slope = lambda x: 1*( np.max(np.abs(np.gradient(x)))>np.finfo(float).eps )
        colors = 'rgbcmyk'
        # xdata
        ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        if xaxis in [0,1,2]:
            xind = ind[xaxis]
            XIND = [xind]*len(self.Dataset)
        elif isinstance(xaxis, list):
            XIND = [ind[_] for _ in xaxis]
        # ydata
        ind = {0: 'exx', 1: 'eyy', 2: 'ezz'}
        if yaxis in [0,1,2]:
            yind = [ind[yaxis]]
            ylabels = yind
            ystyles = ['-']
            YIND = [yind]*len(self.Dataset)
        elif yaxis==3:
            yind = ['exx', 'eyy', 'ezz']
            ylabels = yind
            ystyles = ['--', ':', '-']
            YIND = [yind]*len(self.Dataset)
        elif isinstance(yaxis, list):
            YIND = [[ind[_]] for _ in yaxis]
            ylabels = YIND
            ystyles = ['-']

        fig,ax = plt.subplots(figsize=(7,4), dpi=150)
        fw = 'normal'
        for i,key in enumerate(self.Dataset):
            hl_data = self.Dataset[key]
            ee0 = {_: 0 for _ in YIND[i]}
            if y0_shift:
                _ee0 = {_:[] for _ in YIND[i]}
                for hl in hl_data:
                    ef_ary = hl[XIND[i]]*1e5 # kV/cm
                    for _ in YIND[i]:
                        _ee = hl[_]*100
                        _ee0[_].append(_ee[np.argmin(np.abs(ef_ary))])
                ee0 = {_:np.mean(_ee0[_]) for _ in YIND[i]}
            for hl in hl_data:
                efxyz_ary = [hl[_]*1e5 for _ in ('Ex','Ey','Ez')] # kV/cm
                ef_ary = hl[XIND[i]]*1e5 # kV/cm
                ee_ary = [hl[_]*100-ee0[_] for _ in YIND[i]] # %
                efdir = 'xyz'[xaxis]
                for j,ee in enumerate(ee_ary):
                    ax.plot(ef_ary, ee, linestyle=ystyles[j], color=colors[i%7], label=ylabels[j], lw=2)

            # limits
            if xlim!=None:
                plt.xlim(xlim)
            if ylim!=None:
                plt.ylim(ylim)

            # labels
            plt.xlabel(fr"$E_{efdir}$ (kV/cm)", fontsize=20, fontweight=fw, labelpad=10)
            plt.ylabel("Strain (%)",fontsize=20, fontweight=fw, labelpad=10)

            # legend
            if self.labels==None:
                ax.legend(loc=0, frameon=False, labelspacing=0.25, prop={'size':16, 'weight': fw})

            # ticks
            if xlim!=None:
                xmax = np.max(np.abs(xlim))//50*50
                xticks = np.linspace(-xmax, xmax, xmax//50*2+1)
                ichk = np.where(xticks==0)[0][0]%2
                xticklabels = [int(xt) if _%2==ichk else '' for _,xt in enumerate(xticks)]
                ax.set_xticks(xticks)
                ax.set_xticklabels(xticklabels, fontsize=18, fontweight=fw)
            plt.xticks(fontsize=18, fontweight=fw)
            plt.yticks(fontsize=18, fontweight=fw)

        # custom legends
        if self.labels!=None:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[i%7], lw=2) for i in range(len(self.Dataset.keys()))]
            leg=ax.legend(custom_lines, self.Dataset.keys(), frameon=False, labelspacing=0.25,
                          loc='center left', bbox_to_anchor=(1, 0.5), prop={'size':18, 'weight': fw})

        # zero-axes
        plt.axhline(y=0, color='k', linestyle='-', lw=0.5, zorder=0.5)
        plt.axvline(x=0, color='k', linestyle='-', lw=0.5, zorder=0.5)

        plt.tight_layout()
        if fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')


class Coord_Navigator():
    """ Navigate coordinates
    
    Parameters
    ----------
    f_coord: str
        coordinate file (x, y, z)
    size: tuple/list(3)
        size of box

    Attributes
    ----------
    file: str
        filename of coordinate file
    Coord: np.array
        numpy array of input data
    x/y/z: np.array
        x/y/z of coordinates
    Size: tuple/list(3)
        x/y/z size of box
    """
    def __init__(self, f_coord=None, size=None):
        self.file = f_coord
        self.Coord = None
        self.x = None
        self.y = None
        self.z = None
        self.Size = size

    def load_data(self, is_dump=False, fmt=None, is_raw=False):
        """ Loading data
        
        Parameters
        ----------
        is_dump: bool
            True if using dump format [False]
        fmt: str
            format of file [None]
        is_raw: bool
            True if using raw input

        Returns
        -------
        x/y/z: np.array
            array of coordinates x/y/z
        """
        if is_dump:
            Coord = read_dump(self.file)[0]
        else:
            Coord = parse_data(filename=self.file, fmt=fmt, is_raw=is_raw)
        pfx = 'u' if 'id' in Coord.dtype.names else ''
        x = Coord['x'+pfx]
        y = Coord['y'+pfx]
        z = Coord['z'+pfx]
        self.Coord = Coord
        self.x, self.y, self.z = x, y, z
        return x,y,z

    def pbc(self):
        """ Relocate coordinates if Size is given

        Parameters
        ----------
        None

        Returns
        -------
        None
        """
        if self.Size is None:
            return
        def _pbc(ary, size):
            ary_1 = np.where(ary>=size, ary-size, ary)
            ary_2 = np.where(ary_1<0, ary_1+size, ary_1)
            return ary_2
        self.x, self.y, self.z = [_pbc(_, self.Size[i]) for i,_ in enumerate([self.x, self.y, self.z])]

    def add_data(self, x, y, z):
        """ Adding coordinates

        Parameters
        ----------
        x/y/z: np.array
            array of coordinates x/y/z

        Returns
        -------
        x/y/z: np.array
            array of coordinates x/y/z
        """
        if self.x is None or self.y is None or self.z is None:
            x0, y0, z0 = [], [], []
        else:
            x0, y0, z0 = self.x, self.y, self.z
        self.x = np.concatenate( (x0, x) )
        self.y = np.concatenate( (y0, y) )
        self.z = np.concatenate( (z0, z) )
        self.pbc()
        x, y, z = self.x, self.y, self.z
        return x,y,z

    def filter_sites(self, key=None):
        """ Using key to filter sites [20240628: bug for repeated points]

        Parameters
        ----------
        key: bool array
            boolean array for filtering

        Returns
        -------
        x/y/z: np.array
            x/y/z array of coordinates
        """
        x = self.x[key]
        y = self.y[key]
        z = self.z[key]
        self.x, self.y, self.z = x, y, z
        self.pbc()
        x, y, z = self.x, self.y, self.z
        return x,y,z

    def move_sites(self, v=(0,0,0), *args):
        """ Moving sites along vector(s) v

        Parameters
        ----------
        v: tuple(3)
            vector (dx, dy, dz)

        Returns
        -------
        x/y/z: np.array
            x/y/z array of coordinates
        """
        X = [self.x+v[0]]
        Y = [self.y+v[1]]
        Z = [self.z+v[2]]
        for _v in args:
            X.append(self.x+_v[0])
            Y.append(self.y+_v[0])
            Z.append(self.z+_v[0])
        x = np.concatenate(X)
        y = np.concatenate(Y)
        z = np.concatenate(Z)
        self.x, self.y, self.z = x, y, z
        self.pbc()
        x, y, z = self.x, self.y, self.z
        return x,y,z



class Dipo_Analysis():
    """ Analyzing dipoles arrangement & statistics

    Parameters
    ----------
    f_dipo: str
        dipoRavg/coord file(s)

    Attributes
    ----------
    Dataset: dict
        dataset of dipoRavg/coord file(s) (default: None)
    Size: dict
        Size array (x,y,z) of dipoRavg/coord file(s) (default: None)
    Sliceset: dict
        dataset with selected blocks of dipoRavg/coord file(s) (default: None)
    Naviset: dict
        dataset with navigated sites of dipoRavg/coord file(s) (default: None)
    Group: dict
        grouped dataset of dipoRavg/coord file(s) with assigned labels (default: None)
    Size_sl: dict
        Size array (x,y,z) of Sliceset for select_data()
    i_list: list
        i index for select_data()
    j_list: list
        j index for select_data()
    k_list: list
        k index for select_data()
    exclude: list(list,str)
        list of (x,y,z) or files with 'x y z' as to be excluded for select_data()
    key: dict
        conditional selection, e.g. {'ux': lambda u: u>=0} for select_data()
    """
    def __init__(self, f_dipo=None, *args):
        self.files=[f_dipo]
        for arg in args:
            self.files.append(arg)
        self.Dataset = None
        self.Size = None
        self.Sliceset = None
        self.Size_sl = None
        self.Naviset = None
        self.Group = None
        self.i_list=None
        self.j_list=None
        self.k_list=None
        self.exclude = None
        self.key = None
        self.DW = None
        self.group = None

    def load_data(self, is_dump=False, is_raw=False, iframe=':'):
        """ Loading data

        Parameters
        ----------
        is_dump: bool
            check if file is in dump format (default: False)
        is_raw: boolean
            [False] if using file name, True if using raw text
        iframe: str
            index string for dump frames, e.g. '0', '1:3', '-1'

        Returns
        -------
        Dataset: dict
            x, y, z, ux, uy, uz from dipoRavg/coord file(s) or dump file reshaped to (size_z, size_y, size_x)

        Examples
        --------
        >>> DA = Dipo_Analysis('example_sd.dipoRavg')
        >>> DA.load_data()
        {0: {'x': array([[[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        ...
        >>> DA = Dipo_Analysis('example_sd.dipoRavg', 'example_t180.dipoRavg')
        >>> DA.load_data()
        {0: {'x': array([[[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        ...
        1: {'x': array([[[0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        ...
        """
        # https://wiki.fysik.dtu.dk/ase/_modules/ase/utils.html
        f_str2ind = lambda x: int(x) if x.replace('-','').isdigit() else slice(*[int(_) if _!='' else None for _ in x.split(':')])
        Size = OrderedDict()
        Dataset = OrderedDict()
        ntot = 0
        NF = []
        if is_dump:
            fx = lambda x: x+'u' if len(x)==1 else 'f'+x[-1]
            for i,_f in enumerate(self.files):
                nframe = 0
                Dump = read_dump(_f)
                index = list(Dump.keys())[f_str2ind(iframe)]
                keys = [index] if isinstance(index, int) else index
                for j in keys:
                    ary_dipos = np.ma.array(Dump[j])
                    size_x = ary_dipos['xu'].max()+1
                    size_y = ary_dipos['yu'].max()+1
                    size_z = ary_dipos['zu'].max()+1
                    Size[ntot] = (size_x, size_y, size_z)
                    Dataset[ntot] = {_: Dump[j][fx(_)].reshape(size_z, size_y, size_x) for _ in ['x','y','z','ux','uy','uz']}
                    nframe+=1
                    ntot+=1
                NF.append(nframe)
        else:
            keys = [0]
            for i,_f in enumerate(self.files):
                ary_dipos = np.ma.array(parse_data(_f))
                size_x = ary_dipos['x'].max()+1
                size_y = ary_dipos['y'].max()+1
                size_z = ary_dipos['z'].max()+1
                Size[i] = (size_x, size_y, size_z)
                Dataset[i] = {_: ary_dipos[_].reshape(size_z, size_y, size_x) for _ in ['x','y','z','ux','uy','uz']}
                ntot+=1
            NF.append(ntot)
        print('>>> Loading complete: {} frame(s) @ index={} / {} file(s).'.format(NF, keys, len(self.files)))
        self.Size = Size
        self.Dataset = Dataset
        return Dataset

    def assign_group(self, nframe=None):
        """ Assigning frames into groups for g_* functions

        Parameters
        ----------
        nframe: int
            every `nframe` as a group (default: None)

        Returns
        -------
        None
        """
        keys = list(self.Dataset.keys())
        self.group = [keys[k:k+nframe] for k in keys[::nframe]]

    def select_data(self, i_list=None, j_list=None, k_list=None, exclude=None, key=None):
        """ Selecting data from the specific region of coord/dipoRavg

        Parameters
        ----------
        i_list: list/tuple/array
            list of i index
        j_list: list/tuple/array
            list of j index
        k_list: list/tuple/array
            list of k index
        exclude: list(list,str)
            list of (x,y,z) or files with 'x y z' as to be excluded
        key: dict
            conditional selection, e.g. {'ux': lambda u: u>=0}
        
        Returns
        -------
        Slice: dict
            selected data according to i, j, k index
        
        Examples
        --------
        >>> DA = Dipo_Analysis('example_t180.dipoRavg')
        >>> DA.load_data()
        >>> DA.select_data(i_list=range(5),j_list=range(1),k_list=range(1))
        OrderedDict([(0,
                      {'x': array([[[0, 1, 2, 3, 4]]]),
                       'y': array([[[0, 0, 0, 0, 0]]]),
                       'z': array([[[0, 0, 0, 0, 0]]]),
                       'ux': array([[[ 0.00727686,  0.00415789, -0.00800267,  0.00306084,
                                 0.0183889 ]]]),
                       'uy': array([[[ 0.00051952,  0.00121175, -0.01316228,  0.00248685,
                                -0.00640046]]]),
                       'uz': array([[[0.08664316, 0.1072026 , 0.1163482 , 0.1086856 , 0.1020624 ]]])})])
        >>> DA.select_data(i_list=range(5,10),j_list=range(1),k_list=range(1))
        OrderedDict([(0,
                      {'x': array([[[5, 6, 7, 8, 9]]]),
                       'y': array([[[0, 0, 0, 0, 0]]]),
                       'z': array([[[0, 0, 0, 0, 0]]]),
                       'ux': array([[[ 0.00459424,  0.01884034, -0.01531335, -0.02580363,
                                 0.00305313]]]),
                       'uy': array([[[-0.00697136, -0.00042835, -0.00579635, -0.00752144,
                                -0.01227893]]]),
                       'uz': array([[[-0.09646029, -0.09661005, -0.08981769, -0.08370374,
                                -0.09848465]]])})])

        Notes
        -----
        * Use it carefully with some plotting functions as inconsistent indexing may occur, e.g. `plot_2d`
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        Size = self.Size_sl if self.Sliceset!=None else self.Size
        i_list = i_list if self.i_list is None else self.i_list-np.min(self.i_list)
        j_list = j_list if self.j_list is None else self.j_list-np.min(self.j_list)
        k_list = k_list if self.k_list is None else self.k_list-np.min(self.k_list)
        # exclude data
        def exclude_data(Data, exclude):
            invert = 0
            Data_new = OrderedDict()
            for i in Data:
                if isinstance(exclude[i], (list,tuple)):
                    ec_list = exclude
                elif isinstance(exclude[i], str):
                    invert = 1 if exclude[i][0]=='~' else 0
                    f = exclude[i][1:] if exclude[i][0]=='~' else exclude[i]
                    raw = parse_data(f)
                    ec_list = [(x,y,z) for x,y,z in zip(raw['x'], raw['y'], raw['z'])]
                elif exclude[i] is None:
                    ec_list = []
                mask=np.ones(np.shape(Data[i]['x']), bool) # (lz,ly,lx)
                for x,y,z in ec_list:
                    mask[z,y,x] = False
                if invert:
                    Data_new[i] = {_: np.ma.masked_array(Data[i][_], mask=mask)
                               for _ in ['x','y','z','ux','uy','uz']}
                else:
                    Data_new[i] = {_: np.ma.masked_array(Data[i][_], mask=~mask)
                               for _ in ['x','y','z','ux','uy','uz']}
            return Data_new
        if exclude is None:
            Sliceset = Dataset
        else:
            Sliceset = exclude_data(Dataset, exclude)
        # key selection
        def key_data(Data, key):
            Data_new = OrderedDict()
            for i in Data:
                mask = np.zeros(np.shape(Data[i]['x']), bool)
                for u,f in key.items():
                    if not callable(f):
                        print('[Error]: Undefined funcition for key!')
                        return
                    else:
                        data = Data[i][u]
                        mask = np.where(np.array(list(map(f, data.ravel()))).reshape(np.shape(data)), mask, True)
                    Data_new[i] = {_: np.ma.masked_array(Data[i][_], mask=mask)
                                   for _ in ['x','y','z','ux','uy','uz']}
            return Data_new
        Sliceset = Dataset if Sliceset is None else Sliceset
        if key!=None:
            Sliceset = key_data(Sliceset, key)
        # slice data
        Size_new = OrderedDict()
        Sliceset_new = OrderedDict()
        for i in Sliceset:
            ijk_list = [i_list, j_list, k_list]
            ii, jj, kk = [ary if ary is not None else range(Size[i][ax]) for ax,ary in enumerate(ijk_list)]
            Sliceset_new[i] = {_: Sliceset[i][_][:,:,ii][:,jj,:][kk,:,:]
                           for _ in ['x','y','z','ux','uy','uz']}
            Size_new[i] = Sliceset_new[i]['x'].shape[::-1]
        self.Size_sl = Size_new
        self.Sliceset = Sliceset_new
        self.i_list = i_list
        self.j_list = j_list
        self.k_list = k_list
        self.exclude = exclude
        self.key = key
        return Sliceset_new
    
    def unselect_data(self):
        """ Unselecting data

        Parameters
        ----------
        None
        
        Returns
        -------
        None
        """
        self.Sliceset = None
        self.i_list = None
        self.j_list = None
        self.k_list = None
        self.exclude = None
        self.key = None

    def navigate_data(self, x=None, y=None, z=None):
        """ Navigating data from the specific sites
        
        Parameters
        ----------
        x/y/z: list/tuple/np.array
            coordinates of the specified sites

        Returns
        -------
        Naviset: dict
            selected data based on the specified sites
        """
        # navigate data
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        Size = self.Size_sl if self.Sliceset!=None else self.Size
        Naviset = OrderedDict()
        for i in Dataset:
            Naviset[i] = {_: Dataset[i][_][z,y,x] for _ in ['x','y','z','ux','uy','uz']}
        self.Naviset = Naviset
        return Naviset

    def unnavigate_data(self):
        """ Unnavigating data

        Parameters
        ----------
        None
        
        Returns
        -------
        None
        """
        self.Naviset = None

    def group_data(self, Dataset, label=None):
        """ Assigning data in labelled group(s)

        Parameters
        ----------
        Dataset: dict
            output dict from `select_data` or `navigate_data`
        label: int/str
            label of dataset

        Returns
        -------
        None
        """
        Group = OrderedDict() if self.Group is None else self.Group
        if label not in Group:
            Group[label] = Dataset
        else:
            for t in Dataset:
                for item in Dataset[t]:
                    ary_o0 = Group[label][t][item]
                    ary_o1 = Dataset[t][item]
                    ary_c = np.concatenate( (ary_o0, ary_o1) )
                    Group[label][t][item] = ary_c
        self.Group = Group

    def ungroup_data(self):
        """ Ungroupping data

        Parameters
        ----------
        None
        
        Returns
        -------
        None
        """
        self.Group = None
    
    def cal_corr(self, ui0=0, ui1=0, vshift=[0,0,0], display=False):
        """ Calculating correlation

        Parameters
        ----------
        ui0: int
            component of reference u array0 ([0]:ux, 1:uy, 2:uz)
        ui1: int
            component of comparison u array1 ([0]:ux, 1:uy, 2:uz)
        vshift: list(3)/tuple(3)
            3-dimensional shift vector for ui1
        display: boolean
            display a scatter plot of the data, where x=ui0, y=ui1
        
        Returns
        -------
        res: list
            a list of Pearson correlation coefficients for u array0 & array1
            
        Examples
        --------
        >>> DA = Dipo_Analysis('example_t180.dipoRavg')
        >>> Dipo = DA.load_data()
        >>> DA.cal_corr(ui0=2, ui1=2, vshift=[0,0,5]) # r[uz,uz(x+5)]
        [-0.9905596186960219]
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        i0 = ind[ui0]
        i1 = ind[ui1]
        res = []
        for i in Dataset:
            u_ary0 = Dataset[i][i0].ravel()
            u_ary1 = self.Dataset[i][i1]
            for _ in range(3):
                u_ary1 = np.roll(u_ary1, shift=vshift[_], axis=[2,1,0][_]) # correspond to x, y, z in coord/dipoRavg
            if self.Sliceset!=None:
                u_ary1 = u_ary1[:,:,self.i_list][:,self.j_list,:][self.k_list,:,:]
            u_ary1 = u_ary1.ravel()
            uq0 = np.unique(u_ary0)
            uq1 = np.unique(u_ary1)
            if len(uq0)==len(uq1)==1: # filter for single-value array
                corr = (uq0==uq1)*1 # 1 if same, 0 otherwise
            else:
                r = np.corrcoef(u_ary0, u_ary1)
                corr = r[0,1]
            res.append(corr)
            
            if display:
                fig, ax = plt.subplots(dpi=100)
                ax.plot(u_ary0, u_ary1, 'bo', label=f'corr.={corr:.3f}')
                ax.set_xlabel(f'{i0}0')
                ax.set_ylabel(f'{i1}1 '+str(vshift))
                ax.set_title(i)
                ax.legend()
                ax.set_aspect('equal')
                plt.show()
        return res

    def cal_time_corr(self, t0=0, ui=0, display=False):
        """ Calculating time correlation

        Parameters
        ----------
        t0: int
            time step for the reference snapshot according to the index of self.Dataset
        ui: int
            component of u array ([0]:ux, 1:uy, 2:uz)

        Returns
        -------
        res: list
            a list of Pearson correlation coefficients for u array of t0 and t0+dt
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        u = ind[ui]
        u_ary0 = Dataset[t0][u].ravel()
        res = []
        for i in Dataset:
            u_ary1 = Dataset[i]['uy'].ravel()
            uq0 = np.unique(u_ary0)
            uq1 = np.unique(u_ary1)
            if len(uq0)==len(uq1)==1: # filter for single-value array
                corr = (uq0==uq1)*1 # 1 if same, 0 otherwise
            else:
                r = np.corrcoef(u_ary0.ravel(), u_ary1.ravel())
                corr = r[0,1]
            res.append(corr)
        if display:
            fig, ax = plt.subplots(dpi=100)
            ax.plot(res, 'bs-', label=u)
            ax.set_xlabel('t')
            ax.set_ylabel(f'corr.[t0={t0},t]')
            ax.legend()
            plt.show()
        return res

    def cal_stat(self, ui=0, is_urev_list=None, is_abs=False):
        """ Calculating statistics of dipoles

        Parameters
        ----------
        ui: int
            component of reference u array0 ([0]:ux, 1:uy, 2:uz)
        is_urev_list: list
            default: None; list of boolean; [True] if output data w.r.t the reversed u-axis
        is_abs: bool
            [False], if True, then output after abs() operation

        Returns
        -------
        mean: list
            a list of averages of ui data
        stdev: list
            a list of standard deviations of ui data
        is_urev_list: list or bool
            None, [True] if output data w.r.t the reversed u-axis

        Examples
        --------
        >>> DA = Dipo_Analysis('example_t180.dipoRavg')
        >>> Dipo = DA.load_data()
        >>> DA.cal_stat(ui=2)
        ([-0.00010381436000000025], [0.10073467580829955])
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        if is_urev_list==None:
            iurev = [False]*len(Dataset)
        else:
            if isinstance(is_urev_list, bool):
                iurev = [is_urev_list]*len(Dataset)
            elif isinstance(is_urev_list, list):
                iurev = is_urev_list
        ind = {0:'ux', 1:'uy', 2:'uz'}
        mean = []
        stdev = []
        f_abs = lambda u,i: np.abs(u) if i else u
        for i in Dataset:
            ir = -1 if iurev[i] else 1
            u_ary = Dataset[i][ind[ui]]
            m = f_abs(u_ary, is_abs).mean()*ir if np.ma.count(u_ary)!=0 else np.nan
            std = f_abs(u_ary, is_abs).std()*ir if np.ma.count(u_ary)!=0 else np.nan
            mean.append(m)
            stdev.append(std)
        return mean, stdev
    
    def cal_hist(self, ui=0, umin=-0.2, umax=0.2, bin_size=0.01, is_norm=False, is_urev_list=None):
        """ Calculating histogram of dipoles

        Parameters
        ----------
        ui: int
            component of reference u array0 ([0]:ux, 1:uy, 2:uz)
        umin: float
            u maximum for the histogram
        umax: float
            u minimum for the histogram
        bin_size: float
            bin size of the histogram
        is_norm: bool
            [False], True if normalized data
        is_urev_list: list or bool
            None, [True] if output data w.r.t the reversed u-axis
        
        Returns
        -------
        hist: list
            a list of histogram array of ui
        bins_w0: np.array
            bin edges
        
        Examples
        --------
        >>> DA = Dipo_Analysis('example_t180.dipoRavg')
        >>> DA.load_data()
        >>> DA.cal_hist(ui=2)
        ([array([  0,   0,   0,   0,   0,   0,   1,  14,  61, 164, 174,  73,  13,
                   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,   0,
                   0,  11,  69, 164, 173,  72,  10,   1,   0,   0,   0,   0,   0,
                   0])],
         array([-0.2 , -0.19, -0.18, -0.17, -0.16, -0.15, -0.14, -0.13, -0.12,
                -0.11, -0.1 , -0.09, -0.08, -0.07, -0.06, -0.05, -0.04, -0.03,
                -0.02, -0.01,  0.  ,  0.01,  0.02,  0.03,  0.04,  0.05,  0.06,
                 0.07,  0.08,  0.09,  0.1 ,  0.11,  0.12,  0.13,  0.14,  0.15,
                 0.16,  0.17,  0.18,  0.19,  0.2 ]))
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        # make sure that [-bin_size/2, bin_size/2] is always included if starting from a negative value
        _x, _y = np.array([umin, bin_size])*10**max([len(str(float(_)).split('.')[-1]) for _ in [umin, bin_size]])
        if _x%_y!=0:
            print('[Warning] 0 is not at the middle of the bin!'+f' [{_x}, {_y}]')
        ind = {0:'ux', 1:'uy', 2:'uz'}
        bins = np.arange(umin - bin_size/2, umax + bin_size, bin_size)
        bins_w0 = np.where(np.abs(bins)<np.finfo(float).eps, 0, bins) # 0 if close to zero
        hist = []
        if is_urev_list==None:
            iurev = [False]*len(Dataset)
        else:
            if isinstance(is_urev_list, bool):
                iurev = [is_urev_list]*len(Dataset)
            elif isinstance(is_urev_list, list):
                iurev = is_urev_list
        for i in Dataset:
            u_ary = Dataset[i][ind[ui]]
            h, _ = np.histogram(np.ma.compressed(u_ary.ravel()), bins=bins_w0)
            ntot = self.Size[i][0]*self.Size[i][1]*self.Size[i][2]
            if is_norm:
                h = h/ntot
            rv = -1 if iurev[i] else 1
            hist.append(h[::rv])
        return hist, bins_w0

    def cal_hist2d(self, norm=2, bin_size=None, umax=None, solid=None, f_reduce=None):
        """ Calculating 2D histogram of dipoles

        Parameters
        ----------
        norm: int
            0: x, 1: y, [2]: z as 2D plane normal
        bin_size: float
            [None], size of 2D bins in unit of `solid`
        umax: float
            [None], maximum of u in unit of `solid`
        solid: str
            [None], 'bto', 'bst'
        f_reduce: function
            [None], e.g., lambda x: np.sum(x, axis=0, keepdims=True)

        Returns
        -------
        hist: list
            2D histograms of dipoles
        bins_w0: np.array
            bin edges
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        uf = prefactor(solid=solid)
        ind = {0:'ux', 1:'uy', 2:'uz'}
        normal = [0, 1, 2]
        normal.pop(norm)
        ui, uj = normal
        hist = []
        for i in Dataset:
            # check u range
            ui_ary = uf*Dataset[i][ind[ui]]
            uj_ary = uf*Dataset[i][ind[uj]]
            umag = np.max(np.abs([ui_ary, uj_ary])) if umax is None else umax
            # calculate histogram
            bins = np.arange(-umag - bin_size/2, umag + bin_size, bin_size)
            bins_w0 = np.where(np.abs(bins)<np.finfo(float).eps, 0, bins) # 0 if close to zero
            _h, _xedges, _yedges = np.histogram2d(uj_ary.ravel(), ui_ary.ravel(), bins=[bins_w0, bins_w0])
            hist.append(_h)
        # reduce
        f_reduce = (lambda x: x) if f_reduce is None else f_reduce
        return f_reduce(hist), bins_w0

    def cal_u_evo(self, ui=0):
        """ Calculating averaged polarization evolution

        Parameters
        ----------
        ui: int
            [0]: ux, 1: uy, 2: uz

        Returns
        -------
        u_evo: list
            list of averaged u
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        u_evo = []
        for i in Dataset:
            _um = np.mean(Dataset[i][ind[ui]])
            u_evo.append(_um)
        return u_evo

    def g_cal_u_evo(self, ui=0):
        """ Calculating averaged polarization evolution (for `assign_group`)

        Parameters
        ----------
        ui: int
            [0]: ux, 1: uy, 2: uz

        Returns
        -------
        g_u_evo: dict
            dict of u_evo
        """
        if self.group is None:
            print('[Error]: Run `assign_group` before using this function!')
            return
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        g_u_evo = {}
        for ig,frames in enumerate(self.group):
            g_u_evo[ig] = [np.mean(Dataset[i][ind[ui]]) for i in frames]
        return g_u_evo

    def rg_cal_u_evo(self, ui=0, n=None):
        """ Calculating reduced averaged polarization evolution (for `assign_group`)

        Parameters
        ----------
        ui: int
            [0]: ux, 1: uy, 2: uz
        n: int
            [None], number of reduced groups

        Returns
        -------
        rg_u_evo: dict
            reduced group dict of np.array of u_evo(s)
        """
        g_u_evo = self.g_cal_u_evo(ui=ui)
        gkeys = list(g_u_evo.keys())
        if n is None:
            rkeys = [gkeys]
        else:
            rkeys = [gkeys[k:k+n] for k in gkeys[::n]]
        rg_u_evo = {}
        for ir,groups in enumerate(rkeys):
            rg_u_evo[ir] = np.array([g_u_evo[g] for g in groups])
        return rg_u_evo

    def cal_ul_evo(self, ui=0, norm=0):
        """ Calculating layer-averaged (switched) polarization evolution

        Parameters
        ----------
        ui: int
            [0]/1/2: ux/uy/uz, 10/11/12: ni+/ni_tot
        norm: int
            [0]/1/2: x/y/z as layer normal
        
        Returns
        -------
        ur_evo: list
            list of layer-averaged (switched) polarization evolution
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        normals = [2, 1, 0] # x,y,z
        normals.pop(norm)
        if ui not in (0,1,2,10,11,12):
            return
        ul_evo = []
        for i in Dataset:
            if ui in (0,1,2):
                _um = np.mean(Dataset[i][ind[ui]], axis=tuple(normals))
            elif ui in (10,11,12):
                _um = np.mean(Dataset[i][ind[ui%10]]>0, axis=tuple(normals))
            ul_evo.append(_um)
        return ul_evo

    def g_cal_ul_evo(self, ui=0, norm=0):
        """ Calculating layer-averaged polarization evolution (for `assign_group`)

        Parameters
        ----------
        ui: int
            [0]/1/2: ux/uy/uz, 10/11/12: ni+/ni_tot
        norm: int
            [0]/1/2: x/y/z as layer normal

        Returns
        -------
        g_ul_evo: dict
            dict of ul_evo
        """
        if self.group is None:
            print('[Error]: Run `assign_group` before using this function!')
            return
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        normals = [2, 1, 0] # x,y,z
        normals.pop(norm)
        if ui not in (0,1,2,10,11,12):
            return
        def _cal_ul_evo(Dataset, frames, ui, normals):
            ul_evo = []
            for i in frames:
                if ui in (0,1,2):
                    _um = np.mean(Dataset[i][ind[ui]], axis=tuple(normals))
                elif ui in (10,11,12):
                    _um = np.mean(Dataset[i][ind[ui%10]]>0, axis=tuple(normals))
                ul_evo.append(_um)
            return ul_evo

        g_ul_evo = {}
        for ig,frames in enumerate(self.group):
            g_ul_evo[ig] = _cal_ul_evo(Dataset, frames, ui, normals)
        return g_ul_evo

    def rg_cal_ul_evo(self, ui=0, norm=0, n=None):
        """ Calculating reduced layer-averaged polarization evolution (for `assign_group`)

        Parameters
        ----------
        ui: int
            [0]/1/2: ux/uy/uz, 10/11/12: ni+/ni_tot
        norm: int
            [0]/1/2: x/y/z as layer normal
        n: int
            [None], reduction per number of groups

        Returns
        -------
        rg_ul_evo: dict
            reduced group dict of np.array of ul_evo(s)
        """
        g_ul_evo = self.g_cal_ul_evo(ui=ui, norm=norm)
        gkeys = list(g_ul_evo.keys())
        if n is None:
            rkeys = [gkeys]
        else:
            rkeys = [gkeys[k:k+n] for k in gkeys[::n]]
        rg_ul_evo = {}
        for ir,groups in enumerate(rkeys):
            rg_ul_evo[ir] = np.array([g_ul_evo[g] for g in groups])
        return rg_ul_evo

    def gcal_u_stat(self, gi=None, ui=0, solid='bto', display=False):
        """ Calculating statistics for grouped dipoles

        Parameters
        ----------
        gi: int/str
            label of group, [None]: all
        ui: int
            [0]: ux, 1: uy, 2: uz, 3: ux/y/z
        solid: str
            ['bto'], 'bst'
        display: bool
            show plotting

        Returns
        -------
        STAT: dict
            statistics of grouped dipoles
        """
        Group = self.Group
        if Group is None:
            print('[Warning]: Please use `group_data` before using this function!')
            return
        colors = 'krbgcym'
        items = ['ux', 'uy', 'uz']
        if ui in [0, 1, 2]:
            items = [items[ui]]
        groups = list(Group.keys())
        if gi is not None:
            groups = [gi]

        f_gu = lambda i,u,f: [f(Group[i][_][u])*prefactor(solid) for _ in Group[i]]
        STAT = {}
        for i,ig in enumerate(Group):
            if ig not in groups:
                continue
            if ig not in STAT:
                STAT[ig] = {}
            for item in items:
                u_mean = f_gu(ig, item, np.mean)
                u_std = f_gu(ig, item, np.std)
                STAT[ig][item] = (u_mean, u_std)

        # display
        if display:
            fig, ax = plt.subplots(figsize=(6,4), dpi=100)
            for i,ig in enumerate(STAT):
                for item in STAT[ig]:
                    y, yerr = STAT[ig][item]
                    x = np.arange(len(y))
                    c = colors[i%7]
                    ax.errorbar(x, y, yerr, ls=':', marker='s', mfc='None', mec=c, color=c, mew=1.5, capsize=3, label=ig)
                    ax.set_xticks(x)
                    ax.set_xticklabels([_x if _i%2==0 else '' for _i,_x in enumerate(x)])

            ax.set_xlim(None)
            ax.set_ylim(None)
            ax.tick_params(which='major', labelsize=18, length=4, width=2)
            ax.axhline(y=0, ls='-', lw=1, color='gray', zorder=0)
            ax.spines['left'].set_linewidth(2)
            ax.spines['bottom'].set_linewidth(2)
            ax.spines['right'].set_linewidth(0)
            ax.spines['top'].set_linewidth(0)
            ax.set_xlabel('t', fontsize=20)
            ax.set_ylabel(r'$P_{||}$ ($\mu C/cm^2$)', fontsize=20)
            ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size':18, 'weight': 'normal'})
            plt.show()
        return STAT

    def gcal_u_angle(self, gi=None, v_0=(1,0,0), v_n=(0,0,1), display=False):
        """ Calculating angles of grouped dipoles

        Parameters
        ----------
        gi: int/str
            label of group, [None]: all
        v_0: tuple/list(3)
            reference axis for 0 degree
        v_n: tuple/list(3)
            normal of projection plane
        display: bool
            show plotting

        Returns
        -------
        ANS: dict
            Angles of grouped dipoles
        """
        Group = self.Group
        if Group is None:
            print('[Warning]: Please use `group_data` before using this function!')
            return
        colors = 'krbgcym'
        groups = list(Group.keys())
        if gi is not None:
            groups = [gi]

        f_tangle = lambda u,v,n: np.arctan2(np.dot(np.cross(u,v),n), np.dot(u,v))*180/np.pi
        f_ga = lambda i,f: [f([f_tangle(v_0,_,v_n) for _ in np.stack([Group[i][t][f'u{_}'] for _ in 'xyz'], axis=1)]) for t in Group[i]]
        ANS = {}
        for i,ig in enumerate(Group):
            if ig not in groups:
                continue
            a_mean = f_ga(ig, np.mean)
            a_std = f_ga(ig, np.std)
            ANS[ig] = (a_mean, a_std)

        if display:
            fig, ax = plt.subplots(figsize=(6,4), dpi=100)
            for i,ig in enumerate(ANS):
                y, yerr = ANS[ig]
                x = np.arange(len(y))
                c = colors[i%7]
                ax.errorbar(x, y, yerr, ls=':', marker='s', mfc='None', mec=c, color=c, mew=1.5, capsize=3, label=ig)
                ax.set_xticks(x)
                ax.set_xticklabels([_x if _i%2==0 else '' for _i,_x in enumerate(x)])

            ax.set_xlim(None)
            ax.set_ylim(None)
            ax.tick_params(which='major', labelsize=18, length=4, width=2)
            ax.axhline(y=0, ls='-', lw=1, color='gray', zorder=0)
            ax.spines['left'].set_linewidth(2)
            ax.spines['bottom'].set_linewidth(2)
            ax.spines['top'].set_linewidth(0)
            ax.spines['right'].set_linewidth(0)
            ax.set_xlabel('t', fontsize=20)
            ax.set_ylabel(r'angle ($^\circ$)', fontsize=20)
            ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size':18, 'weight': 'normal'})
            plt.show()
        return ANS

    def cal_iproj(self, pax=None):
        """ Calculating projection of index

        Parameters
        ----------
        pax: list/tuple(3)
            projection axis vector, default: None

        Returns
        -------
        SUM: dict
            {#: array of projected values}
        """
        if pax is None or len(pax)!=3:
            print('[Error]: Please assign a projection vector, e.g. [0,0,1]!')
            return
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        f_proj = lambda u,v: np.dot(u,v)/np.linalg.norm(v)
        f_pm = lambda x: (pax[x]>=0)*1+(pax[x]<0)*(-1)
        f_pbc = lambda x,L: np.where(x<0, x+L, x)
        SUM = OrderedDict()
        for i in Dataset:
            Size = self.Size[i]
            Data = self.Dataset[i]
            Lx, Ly, Lz = Size
            # mapping to Octant I
            x, y, z = [f_pbc(Data[_]*f_pm(j), Size[j]) for j,_ in enumerate('xyz')]
            u = np.stack([x+1, y+1, z+1], axis=-1)
            v = np.abs(pax)
            pj = f_proj(u, v)
            lh = f_proj([Lx, Ly, Lz], v)/2
            if np.count_nonzero(v)==1:
                proj = pj
            else:
                proj = np.where(pj<=lh, pj, pj-lh)
            proj -= np.min(proj) # shift the lowest to 0
            SUM[i] = proj
        return SUM

    def cal_uproj(self, pax=None, solid='bto'):
        """ Calculating projection of u-vector 

        Parameters
        ----------
        pax: list/tuple(3)
            projection axis vector
        solid: str
            prefactor of u, ['bto'] or 'bst'

        Returns
        -------
        SUM: dict
            {#: array of projected values}
        """
        if pax is None or len(pax)!=3:
            print('[Error]: Please assign a projection vector, e.g. [0,0,1]!')
            return
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        f_proj = lambda u,v: np.dot(u,v)/np.linalg.norm(v)
        SUM = OrderedDict()
        for i in Dataset: 
            ux, uy, uz = [Dataset[i]['u'+_]*prefactor(solid=solid) for _ in 'xyz']
            u = np.stack([ux, uy, uz], axis=-1)
            v = pax
            proj = f_proj(u, v)
            SUM[i] = proj
        return SUM

    def cal_proj_avg(self, rax=[1,0,0], sax=[0,0,1], solid='bto', display=False):
        """ Calculating average of projected polarization along projection axis [1]

        Parameters
        ----------
        rax: list/tuple(3)
            projection axis r such that ur//[u(inf)-u(-inf)]
        sax: list/tuple(3)
            projection axis s such that [u(inf)-u(-inf)] dot us = 0
        solid: str
            prefactor of u, ['bto'] or 'bst'
        display: bool
            showing polarization(r,s,t) profile along projected axis

        Returns
        -------
        RES: dict
            {#: ({dict of projected values for 'ur', 'us' and 'ut'}, projected_index)}

        References
        ----------
        [1] https://journals.aps.org/prb/abstract/10.1103/PhysRevB.81.144125
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        f_proj = lambda u,v: np.dot(u,v)/np.linalg.norm(v)
        tax = np.cross(rax, sax)
        IPJ = self.cal_iproj(sax)
        UR = self.cal_uproj(rax, solid)
        US = self.cal_uproj(sax, solid)
        UT = self.cal_uproj(tax, solid)
        RES = {}
        for i in Dataset:
            Size = self.Size[i]
            ipj = IPJ[i].ravel()
            ip = (np.max(ipj)-np.min(ipj))/(f_proj(Size, np.abs(sax))-1) # bin_size
            ur = UR[i].ravel()
            us = US[i].ravel()
            ut = UT[i].ravel()
            PJ = OrderedDict()
            for j,_pj in enumerate(ipj):
                ind = _pj//ip # convert to integer indice
                if ind not in PJ:
                    PJ[ind] = {'u'+_:[] for _ in 'rst'}
                PJ[ind]['ur'].append(ur[j])
                PJ[ind]['us'].append(us[j])
                PJ[ind]['ut'].append(ut[j])
            Ipj = []
            SUM = {'u'+_:[] for _ in 'rst'}
            for ind in sorted(PJ):
                Ipj.append(ind*ip) # restore to original value
                SUM['ur'].append(np.mean(PJ[ind]['ur']))
                SUM['us'].append(np.mean(PJ[ind]['us']))
                SUM['ut'].append(np.mean(PJ[ind]['ut']))
            RES[i] = (SUM, Ipj)
            if display:
                fig, ax = plt.subplots(dpi=100)
                ax.plot(Ipj, SUM['ur'], 'r-s', label=r'$P_\vec{r}$')
                ax.plot(Ipj, SUM['us'], 'g-s', label=r'$P_\vec{s}$')
                ax.plot(Ipj, SUM['ut'], 'b-s', label=r'$P_\vec{t}$')
                ax.tick_params(which='major', labelsize=18)
                ax.legend(frameon=False, prop={'size': 18})
                ax.set_xlabel('s (u.c.) || [{}]'.format(''.join([str(_) for _ in sax])), fontsize=20)
                ax.set_ylabel(r'P ($\mu C/cm^2$)', fontsize=20)
                plt.show()
        return RES

    @staticmethod
    def subdiv_2dary(ary, div=1):
        """ Dividing 2D array with quadratic bins of size=div """
        ary_0 = np.array(ary)
        lu, lv = np.shape(ary_0)
        if lu%div!=0 or lv%div!=0:
            print(f'[Error]: Undividable by `div={div}`!')
            return
        else:
            du = lu//div
            dv = lv//div
        ary_all = []
        for i in np.arange(0,lu,du):
            ary_v = []
            for j in np.arange(0,lv,dv):
                ary_s = ary_0[i:i+du, j:j+dv]
                ary_v.append(ary_s)
            ary_all.append(ary_v)
        return np.array(ary_all)

    @staticmethod
    def alloc_defects_2d(f_defects, norm=0, size=None, div=1, is_dump=False):
        """ Allocating defects to 2D subdivisions

        Parameters
        ----------
        f_defects: str
            filename for defects
        norm: int
            normal of 2D-plane ([0]:x, 1:y, 2:z)
        size: tuple(3)/list(3)
            (Lx, Ly, Lz)
        div: int
            for subdivided area by quadratic bins with size=div
        is_dump: bool
            [False], True if using dump format

        Returns
        -------
        SUM: dict
            allocated defects based on divisions
        """
        CN = Coord_Navigator(f_defects, size=size)
        x, y, z = CN.load_data(is_dump=is_dump)
        coord = [x, y, z]
        normal = [0, 1, 2]
        axn = normal.pop(norm)
        axu, axv = normal
        um, vm = [size[_] for _ in normal]
        u,v = np.meshgrid(range(um), range(vm))
        us,vs = [Dipo_Analysis.subdiv_2dary(_, div=div) for _ in (u,v)]
        lu, lv, _, _ = us.shape
        SUM = {}
        for iu in range(lu):
            for iv in range(lv):
                umin, umax = np.min(us[iu,iv]), np.max(us[iu,iv])
                vmin, vmax = np.min(vs[iu,iv]), np.max(vs[iu,iv])
                condi_u = (coord[axu]>=umin) & (coord[axu]<=umax)
                condi_v = (coord[axv]>=vmin) & (coord[axv]<=vmax)
                ind_s = np.where(condi_u & condi_v)[0]
                SUM[(iu,iv)] = {ax: coord[_][ind_s].tolist() for _,ax in enumerate('xyz')}
        return SUM

    def find_DW_pos(self, norm=0, ui=0, rmse_crit=0.01, mode='tanh', display=False):
        """ Finding domain walls

        Parameters
        ----------
        norm: int
            normal of DW ([0]:x, 1:y, 2:z)
        ui: int
            component of reference u array ([0]:ux, 1:uy, 2:uz)
        rmse_crit: float
            convergence criterion for fitting
        mode: str
            mode on how to find DW
            quick: using derivative to detect abrupt jump
            [tanh]: using tanh to fit ui_avg profile
        display: boolean
            [False], True if want to display the figure

        Returns
        -------
        SUM: dict
            SUM[i]['status']: fitting status
            SUM[i]['u_mean']: raw data for fitting
            SUM[i]['u_fit']: fitting data
            SUM[i]['rmse']: RMSE
            SUM[i]['x']: fitted parameters for (x0, w0, p0, d0)

        Examples
        --------
        >>> DA = Dipo_Analysis('example_t180.dipoRavg')
        >>> DA.load_data()
        >>> res = DA.find_DW_pos(norm=0, ui=2, display=1)
        >>> res[0]['x']
        OrderedDict([('x0', 4.591303691156161),
                     ('w0', 0.1218771202125048),
                     ('p0', -0.09943861234252863),
                     ('d0', -0.0017733576987882008)])
        >>> res[0]['rmse']
        0.0008317045177048763
        """
        def _mode_tanh(u_ind, u_mean, rmse_crit, display):
            # fitting tanh
            status, rmse, u_fit, (x0_fit, w0_fit, p0_fit, d0_fit) = fit_tanh(u_ind, u_mean, rmse_crit, display)
            if status:
                ANS = OrderedDict(x0=x0_fit, w0=w0_fit, p0=p0_fit, d0=d0_fit)
            else:
                print('[Warning]: Fail to minimize the fitting!')
                ANS = OrderedDict(x0=-1, w0=-1)
            if np.mean(np.abs(u_mean - np.mean(u_mean))) < rmse_crit:
                print(f'[Warning]: No DW! (rmse_crit = {rmse_crit})')
                ANS = OrderedDict(x0=-1, w0=-1)
            return ANS, status, rmse, u_fit
        
        def _mode_quick(u_ind, u_mean, display):
            # calculating derivative
            ind_o = np.min(u_ind) # origin of u_ary
            du = np.gradient(u_mean, u_ind[1]-u_ind[0])
            ind_max, properties = find_peaks(np.abs(du), height=0.01, distance=1, prominence=None, width=1)
            res_fwhm = peak_widths(np.abs(du), ind_max, rel_height=0.5) # widths, heights, wi_mins, wi_maxs
            if len(ind_max)!=0:
                rmse = res_fwhm[0]
                x0 = ind_o + (res_fwhm[2] + res_fwhm[3])/2
                ANS = OrderedDict(x0=x0, w0=rmse)
            else:
                rmse = -1
                ANS = OrderedDict(x0=-1, w0=-1)
            if display:
                fig,ax1 = plt.subplots()
                ax2 = ax1.twinx()
                ax2.plot(u_ind, abs(du), 'k-')
                ax2.hlines(res_fwhm[1], ind_o+res_fwhm[2], ind_o+res_fwhm[3], color='grey')
                ax2.set_ylabel('du/dx')
                ax1.plot(u_ind, u_mean, 'b-o')
                ax1.plot(u_ind[ind_max], u_mean[ind_max], 'ro')
                ax1.set_xlabel('x')
                ax1.set_ylabel('u')
                plt.show()
            return ANS, rmse
            
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        ax = [2, 1, 0] # correspond to x, y, z in coord/dipoRavg
        ax.pop(norm)
        SUM = OrderedDict()
        for i in Dataset:
            u_ind = Dataset[i]['xyz'[norm]].mean(axis=tuple(ax))
            u_ary = Dataset[i][ind[ui]]
            u_mean = u_ary.mean(axis=tuple(ax))
            
            if mode=='tanh':
                ANS, status, rmse, u_fit = _mode_tanh(u_ind, u_mean, rmse_crit, display)
            elif mode=='quick':
                ANS, rmse = _mode_quick(u_ind, u_mean, display)
                status = True
                u_fit = u_mean
            
            # output
            if i not in SUM:
                SUM[i]=OrderedDict()
            SUM[i]['status'] = status
            SUM[i]['u_mean'] = list(u_mean)
            SUM[i]['u_fit'] = list(u_fit)
            SUM[i]['rmse'] = rmse
            SUM[i]['x'] = ANS
        return SUM
    
    def find_DW_surf(self, norm=0, ui=0, kernel=None, key=None, mode='ct', display=False):
        """ Finding 2D domain wall segments
        [Note]: The length of data along the wall normal (`norm`) should be >3.

        Parameters
        ----------
        norm: int
            normal of DW ([0]:x, 1:y, 2:z)
        ui: int
            component of reference u array ([0]:ux, 1:uy, 2:uz)
        kernel: np.array
            [None], 3-dimensional numpy array as convolution kernel
        key: dict
            [None], dict of functions for transformation, e.g. {'ux', lambda u: -1 if u<0 else u}
        mode: str
            ['ct']: convolve-transform, 'tc': transform-convolve
        display: boolean
            [False], True if want to display the figure

        Returns
        -------
        SUM: dict
            SUM[i]: 2D array of index of DW
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        Size = self.Size
        ind = {0:'ux', 1:'uy', 2:'uz'}
        normal = [2, 1, 0] # correspond to x, y, z in coord/dipoRavg
        normal.pop(norm)
        # select data
        ijk_list = [self.i_list, self.j_list, self.k_list]
        ijk_o = [np.min(_) if _ is not None else 0 for _ in ijk_list]
        # convolution
        def convolve(D, ui, kernel):
            C = OrderedDict()
            for i in D:
                C[i] = {_: D[i][_] for _ in D[i]}
                for _u in [ui]:
                    u = ind[_u]
                    data = C[i][u]
                    conv = signal.convolve(data, np.array(kernel), mode='same')
                    C[i][u] = conv
            return C
        # mapping
        def transform(D, key):
            T = OrderedDict()
            for i in D:
                T[i] = {_: D[i][_] for _ in D[i]}
                for u,f in key.items():
                    if not callable(f):
                        print('[Error]: Undefined funcition for key!')
                        return
                    else:
                        data = T[i][u]
                        T[i][u] = np.array(list(map(f, data.ravel()))).reshape(np.shape(data))
            return T
        # find DW
        if mode=='ct':
            Dataset = convolve(Dataset, ui=ui, kernel=kernel) if kernel is not None else Dataset
            Dataset = transform(Dataset, key=key) if key is not None else Dataset
        elif mode=='tc':
            Dataset = transform(Dataset, key=key) if key is not None else Dataset
            Dataset = convolve(Dataset, ui=ui, kernel=kernel) if kernel is not None else Dataset
        imax = 0
        SUM = OrderedDict()
        for i in Dataset:
            u_ind = Dataset[i]['xyz'[norm]].mean(axis=tuple(normal))
            u_ary = Dataset[i][ind[ui]]
            u_mean = u_ary.mean(axis=tuple(normal))
            if i==0:
                imax = len(u_mean)-1 if u_mean[-1]<0 else 0   # DW_L(+|-)
            du_ary = np.abs(np.gradient(u_ary, axis=[2,1,0][norm]))
            du_imax_s = np.argmax(du_ary, axis=[2,1,0][norm])
            du_imax_s[du_imax_s==0] = imax
            du_imax_o = u_ind[du_imax_s]
            SUM[i] = du_imax_o + 0.5 # Wall position is between unit cells
            # display
            if display:
                fig = plt.figure(figsize=(10,10))
                ax = fig.add_subplot(projection="3d")
                x0 = SUM[i]
                y = np.arange(len(x0[0]))
                z = np.arange(len(x0))
                (y ,z) = np.meshgrid(y,z)

                ax.plot_surface(x0, y+ijk_o[1], z+ijk_o[2], alpha=0.3)
                #ax.plot_wireframe(x0,y,z, rstride=1, cstride=1)
                #ax.view_init(elev=5, azim=-85)
                ax.set_xlim([0,Size[i][0]])
                ax.set_ylim([0,Size[i][1]])
                ax.set_zlim([0,Size[i][2]])
                ax.set_xlabel('X')
                ax.set_ylabel('Y')
                ax.set_zlabel('Z')
                ax.grid(False)
                plt.show()
        self.DW = SUM
        return SUM

    def plot_wall_line(self, norm=0, ui=0, xref=None, xlim=None, dt=1, if_sfc=False, i_arr=None, figsize=(4,4), fig_out=None):
        """ Plotting 1D DW line

        Parameters
        ----------
        norm: int
            [0]: x, 1: y, 2: z
        ui: int
            [0]: ux, 1: uy, 2: uz
        xref: float
            [None], reference line for x-axis
        dt: float
            [1], timestep for DW propagation (in ps)
        if_sfc: bool
            display special format
        i_arr: int
            [None], frame indice for arrows decoration
        figsize: tuple(2)
            size of figure
        fig_out: str
            filename of the output figure

        Returns
        -------
        WL: dict
            {ind: (wall_mean, wall_std)}
        """
        if self.DW is None:
            print('[Error]: No DW found! Please run `find_DW_surf` before using this function!')
            return
        # initialization
        DW = self.DW
        Size = self.Size
        normal = [0, 1, 2]
        normal.pop(norm)
        uij = normal[::-1]
        iavg = uij.index(ui)
        uij.pop(iavg)
        iax = uij[0]
        # functions
        f_xy = lambda x,y: (np.mean(x), np.mean(y))
        def dec_arrow(ax, dw_mean, rlim):
            Arr = []
            lmin = np.min(dw_mean) if xref is None else xref
            lmax = np.max(dw_mean)
            illo = np.where(dw_mean<lmin+(lmax-lmin)*rlim[0])[0]
            ilhi = np.where(dw_mean<lmin+(lmax-lmin)*rlim[1])[0]
            if 0<len(illo)<Lax:
                ilo0, ilo1 = illo[0]+1, illo[-1]
                ihi0, ihi1 = ilhi[0], ilhi[-1]+1
                ihi0 = ilo0-7 if ilo0-ihi0>Lax//6 else ihi0
                ihi1 = ilo1+7 if ihi1-ilo1>Lax//6 else ihi1
                ax.plot(dw_mean[ihi0:ilo0], np.arange(ihi0,ilo0), '-', color=cmap(cnorm(ind)), lw=3)
                ax.plot(dw_mean[ilo1:ihi1], np.arange(ilo1,ihi1), '-', color=cmap(cnorm(ind)), lw=3)
                Arr.append( f_xy(dw_mean[ihi0:ilo0], np.arange(ihi0,ilo0)) )
                Arr.append( f_xy(dw_mean[ilo1:ihi1], np.arange(ilo1,ihi1)) )
            return Arr
        # main
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        cnorm = mpl.colors.Normalize(vmin=0, vmax=len(DW))
        cmap = plt.cm.brg
        wmin = np.min([DW[_] for _ in DW])
        wmax = np.max([DW[_] for _ in DW])
        WL = {}
        Arr_head, Arr_tail = [], []
        for ind in DW:
            Lax = Size[ind][iax]
            dw = DW[ind]
            dw_mean = np.mean(dw, axis=iavg)
            dw_std = np.std(dw, axis=iavg)
            WL[ind] = (dw_mean, dw_std)
            # plot dw
            wlo, whi = dw_mean-dw_std, dw_mean+dw_std
            ax.plot(dw_mean, np.arange(Lax), '-', color=cmap(cnorm(ind)))
            ax.fill_betweenx(np.arange(Lax), wlo, whi, color=cmap(cnorm(ind)), alpha=0.2)
            if i_arr is not None:
                if ind==i_arr:
                    Arr_tail += dec_arrow(ax, dw_mean, rlim=(0.1, 0.7))
                elif ind==i_arr+1:
                    Arr_head += dec_arrow(ax, dw_mean, rlim=(0.1, 0.9))
            if if_sfc==-1:
                ax.text(dw_mean[ind%2-1]-2, 48+(ind%2==1)*4, fr'$t_{{{ind}}}$', color=cmap(cnorm(ind)), fontsize=18)
        # plot arrows
        for i in range(len(Arr_head)):
            _x, _y = Arr_tail[i]
            _x1, _y1 = Arr_head[i]
            _ux, _uy = _x1-_x, _y1-_y
            ax.quiver(_x1, _y1, _ux*1.2, _uy*1.2, color='k', pivot='tip',
                      width=0.01, headwidth=6, headaxislength=3,
                      angles='xy', scale_units='xy', scale=1, zorder=3)
        # plot xref
        if xref is not None:
            ax.axvline(x=xref, ls='--', lw=1, color='k', zorder=-1)
        ax.tick_params(which='major', labelsize=18)
        ax.set_xlabel('XYZ'[norm], fontsize=20)
        ax.set_ylabel('XYZ'[iax], fontsize=20)
        ax.xaxis.set_major_locator(plt.MultipleLocator(10))
        ax.spines['right'].set_visible(False)
        # custom colorbar
        if if_sfc!=-1:
            t_ticks = np.arange(len(DW))
            sm = plt.cm.ScalarMappable(cmap=cmap, norm=cnorm)
            cbar = plt.colorbar(sm, orientation='horizontal', location='top', extend='max', 
                                fraction=0.05, aspect=18*(wmax-wmin)/Lax, pad=0.04)
            if if_sfc is True:
                cbar.ax.set_xticks([])
                cbar.ax.set_xticklabels([])
                cbar.ax.tick_params(axis='x', which='both', bottom=False, top=False)
                cbar.ax.set_xlabel(rf'Time evolution', fontsize=16)
            else:
                cbar.ax.set_xticks(t_ticks+0.5)
                cbar.ax.set_xticklabels(t_ticks*dt, rotation=90)
                cbar.ax.tick_params(axis='x', pad=0.04, labelsize=14)
                cbar.ax.set_xlabel(rf't (ps)', fontsize=16)
        plt.axis('scaled')
        if xlim is not None:
            ax.set_xlim(xlim)
        ax.set_ylim([0,Lax-1])
        if fig_out is not None:
            plt.savefig(f'{fig_out}', bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')
        return WL

    def cal_DW_pass(self, DW=None, ind=0):
        """ Calculating percentage of passed DW segments

        Parameters
        ----------
        DW: dict
            output from `find_DW_surf`
        ind: float
            [0], checkpoint indice for passed DW segments

        Returns
        -------
        res: list
            list of % of wall segments
        """
        if DW is None:
            print(f'[Error]: Please specify input for DW ({DW})!')
            return
        # calculate wall segments
        res = []
        for i in DW:
            h_ary = DW[i]
            lv, lu = h_ary.shape
            nw = len(np.where(h_ary>=ind)[0])
            res.append(nw/(lv*lu)*100)
        return res

    def plot_DW_pass(self, DW=None, ilo=0, ihi=None, di=1, dt=1, fig_out=None):
        """ Plotting percentage of passed DW segments

        Parameters
        ----------
        DW: dict
            output from `find_DW_surf`
        ilo: int
            [0], lower bound of checkpoint indice for passed DW segments
        ihi: int
            [None], if None, then ilo+di
        di: int
            [1], indice increment
        fig_out: str
            output file name

        Returns
        -------
        None
        """
        ihi = ilo+di+1 if ihi is None else ihi
        fig, ax = plt.subplots(dpi=100, figsize=(6,4))
        for i in range(ilo, ihi+1, di):
            res = self.cal_DW_pass(DW=DW, ind=i)
            xind = np.arange(len(res))*dt
            ax.plot(xind, res, 's-', label=i)
        # reference lines
        ax.axhline(y=0, ls='--', color='k', lw=.8)
        ax.axhline(y=100, ls='--', color='k', lw=.8)
        ax.set_xlim([0, xind[-1]])
        ax.tick_params(which='major', labelsize=20)
        ax.set_xlabel('t (ps)', fontsize=20)
        ax.set_ylabel('passed DW area (%)', fontsize=20)
        ncols = 1 if len(res)<=6 else len(res)//6+1
        leg = ax.legend(loc='center left', ncol=ncols, columnspacing=1, bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
        leg.set_title('Layer index', prop={'size': 18})
        # save figure
        if fig_out!=None:
            plt.savefig(f'{fig_out}', bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def g_cal_DW_pass(self, DW=None, ind=0, reverse=False, dt=10, labels=None, fig_out=None):
        """ Calculating percentage of passed DW segments (for `assign_group`)

        Parameters
        ----------
        DW: dict
            output from `find_DW_surf`
        ind: float
            [0], checkpoint index for passed DW segments
        reverse: bool
            [False]: >=ind, True: <=ind
        labels: list
            [None], labels for group
        fig_out: str
            output file name

        Returns
        -------
        SUM: dict
            dict of passing % wall segments for each group
        """
        if self.group is None:
            print('[Error]: Run `assign_group` before using this function!')
            return
        SUM = {}
        for ig,frames in enumerate(self.group):
            gDW = {_i: DW[_f] for _i,_f in enumerate(frames)}
            res = self.cal_DW_pass(DW=gDW, ind=ind)
            SUM[ig] = res

        if fig_out!=False:
            labels = list(SUM.keys()) if labels is None else labels
            fig, ax = plt.subplots(dpi=100)
            for ig in SUM:
                res = SUM[ig]
                t = np.arange(len(res))*dt
                ax.plot(t, res, 's-', label=labels[ig])
            # reference lines
            ax.axhline(y=0, ls='--', color='k', lw=.8)
            ax.axhline(y=100, ls='--', color='k', lw=.8)
            ax.set_xlim([0, t[-1]])
            ax.tick_params(which='major', labelsize=20)
            ax.set_xlabel('t (ps)', fontsize=20)
            ax.set_ylabel('passed DW area (%)', fontsize=20)
            ax.set_title(f'index = {x}', fontsize=20)
            ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
            # save figure
            if fig_out!=None:
                plt.savefig(f'{fig_out}', bbox_inches='tight')
            else:
                plt.show()
            plt.close('all')
        return SUM

    def g_cal_tpin_stat(self, DW=None, x=0, dx=1, norm=0, dt=1, f_defects=None, igb=2, if_reduce=True, fig_out=None):
        """ Calculating pinning time statistics (for `assign_group`)

        Parameters
        ----------
        DW: dict
            output from `find_DW_surf`
        x: int
            index for pinning site (default:0)
        dx: float
            x-dx < pinning range < x+dx
        norm: int
            normal of DW ([0]:x, 1:y, 2:z)
        dt: float
            time step in unit of ps (default: 1)
        f_defects: str
            [None], file name of defects
        igb: int
            groupby index (0:x, 1:y, [2]:z)
        if_reduce: bool
            [True] for pinning times statistics, False for pinning duration of each group
        fig_out: str
            [None], file name of output; if False, skip plotting

        Returns
        -------
        tmean, tmax, xden: list
            (if_reduce==True): list of mean, max of pinning time and local defect densities
        SUM: dict
            (if_reduce==False): {ig: [Time, xden]}, where ig: group id, Time: pinning times for each groupby index, xden: local defect density
        """
        if self.group is None:
            print('[Error]: Run `assign_group` before using this function!')
            return
        Size = self.Size[0]
        L = Size[igb]
        Lj,Li = [Size[_] for _ in range(3) if _!=norm]

        def cal_t_pin(DWT, if_reduce=True, Time=None):
            """ Calculating pinning time

            Parameters
            ----------
            DWT: np.array
                DW surface in shape of (j,i,t)
            if_reduce: bool
                [True] returns scalar, otherwise list
            Time: dict
                use existing Time

            Returns
            -------
            Time: dict
                dict of one or a list of pinning time(s)
            """
            Time = {} if Time is None else Time
            for z in range(Lj):
                for y in range(Li):
                    ind = [x, y, z][igb] # groupby 0/1/2:x/y/z
                    if ind not in Time:
                        Time[ind] = []
                    sDW = DWT[z,y,:]
                    t = np.where(np.abs(sDW-x)<dx)[0] # x-dw < DW < x+dw
                    if len(t)==0:
                        tpin = 0
                        Time[ind] += [tpin*dt] if if_reduce else []
                    else:
                        tpin = t.max()-t.min()
                        Time[ind] += [tpin*dt] if if_reduce else list(t*dt)
            return Time

        def cal_local_defect_density(f_defects, Size=(164, 48, 48)):
            Xd = self.alloc_defects_2d(f_defects, norm=0, size=Size, div=1, is_dump=False)
            xdata = Xd[(0,0)]['xyz'[igb]]
            xden = []
            for i in range(Size[igb]):
                xden.append(xdata.count(i)/Size[igb])
            return xden

        xden = cal_local_defect_density(f_defects, Size=Size)
        Time = None
        SUM = {}
        for ig, frames in enumerate(self.group):
            DWT = np.concatenate([DW[_][:,:,np.newaxis] for _ in frames], axis=-1)
            if if_reduce:
                Time = cal_t_pin(DWT, if_reduce=True, Time=Time)
            else:
                Time = cal_t_pin(DWT, if_reduce=False)
                xden = cal_local_defect_density(f_defects, Size=Size)
                SUM[ig] = (Time, xden)
                # plot t_pin
                if fig_out!=False:
                    fig, ax = plt.subplots(dpi=100)
                    norm = mpl.colors.Normalize(vmin=0, vmax=np.max(xden), clip=True)
                    cmap = plt.cm.Wistia
                    smap = mpl.cm.ScalarMappable(cmap=cmap, norm=norm)
                    for ind in Time:
                        if len(Time[ind])!=0:
                            t0, t1 = np.min(Time[ind]), np.max(Time[ind])
                            ax.barh(ind, t1-t0, height=1, left=t0, color=cmap(norm(xden[ind])))
                    # grid lines
                    h = 1
                    for i in range(0,L,h):
                        plt.axhline(y=i-0.5, ls='-', color='grey', lw=.8)
                    # tick parameters
                    ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
                    ax.set_xlim([0,10*dt])
                    ax.set_ylim([-0.5,L-0.5])
                    ax.set_xlabel('t (ps)', fontsize=20)
                    ax.set_ylabel('xyz'[igb]+' index', fontsize=20)
                    ax.set_title(f'{x-dx}'+r'$<x_{DW}<$'+f'{x+dx}', fontsize=20)
                    cbar = fig.colorbar(smap, ax=ax)
                    cbar.ax.tick_params(length=4, width=1, labelsize=16)
                    cbar.ax.get_yaxis().labelpad = 24
                    cbar.ax.set_ylabel(r'defect density ($u.c.^{-2}$)', fontsize=18, fontweight='normal', rotation=270)
                    if fig_out!=None:
                        rem, ext = fig_out.rsplit('.', maxsplit=1)
                        fig.savefig(f'{rem}_{ig}.{ext}', dpi=fig.dpi, bbox_inches='tight')
                    else:
                        plt.show()

        if if_reduce:
            STAT = {}
            for i in Time:
                if i not in STAT:
                    STAT[i] = []
                STAT[i] += Time[i]
            tmean = []
            tmin, tmax = [], []
            for i in STAT:
                if len(STAT[i])!=0:
                    m = np.mean(STAT[i])
                    mi, mx = np.min(STAT[i]), np.max(STAT[i])
                    tmean.append(m)
                    tmin.append(mi)
                    tmax.append(mx)
            if fig_out!=False:
                # plot t_pin-stat.
                fig, ax = plt.subplots(dpi=100)
                ax1 = ax.twinx()
                norm = mpl.colors.Normalize(vmin=0, vmax=np.max(xden), clip=True)
                cmap = plt.cm.Wistia
                ax1.bar(range(L), xden, width=1, color=cmap(norm(xden)), label=r'd:density')
                ax.plot(range(L), tmean, 's-', color='k', label=r't:mean')
                ax.plot(range(L), tmax, '.-', color='k', label=r't:max')
                ax.set_zorder(1)
                ax.set_facecolor('none')
                ax.set_xlim([-1,L])
                ax.set_ylim([0,None])
                ax1.set_ylim([0,None])
                ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
                ax1.tick_params(which='major', labelsize=18, length=5, width=0.8)
                ax.set_xlabel('xyz'[igb]+' index', fontsize=20)
                ax.set_ylabel(r'$\Delta t_{pin}$ (ps)'+f' @x={x}', fontsize=20)
                ax1.set_ylabel(r'defect density ($u.c.^{-2}$)', fontsize=20)
                ln, labs = ax.get_legend_handles_labels()
                ln1, labs1 = ax1.get_legend_handles_labels()
                ax1.legend(ln+ln1, labs+labs1, loc='lower center', ncol=3, bbox_to_anchor=(0.5, 1),
                           columnspacing=1, frameon=False, prop={'size':18, 'weight': 'normal'})
                if fig_out!=None:
                    fig.savefig(fig_out, dpi=fig.dpi)
                else:
                    plt.show()
            plt.close('all')
            return tmean, tmax, xden
        else:
            plt.close('all')
            return SUM

    def cal_DW_roughness(self, norm=0, div=1, show_sub=False, mode='mean', f_app=None, vmax=None,
                         wlim=None, display=False, if_sfc=False, figsize=None, fig_out=None):
        """ Calculating DW roughness

        Parameters
        ----------
        norm: int
            normal of DW ([0]:x, 1:y, 2:z)
        div: int
            for subdivided area by quadratic bins with size=div
        show_sub: bool/str
            [False], True if return sub-roughness
            'd'/'dc'/'dr': for showing defect density all/column/row-wise
        mode: str
            ['mean']: color coding w.r.t. mean position; 'defects': color coding w.r.t. defect layer
        f_app: str
            for `mode=defects`, filename of defects
        vmax: float
            [None], upper bound for colorbar of roughness or defect density
        wlim: tuple(2)
            limit of xDW range
        display: boolean
            [False], True if want to display the figure
        if_sfc: bool
            [False], display special style
        figsize: tuple(2)
            size of figure, default: (7,7) for `div==1`, else (14,7)
        fig_out: str
            file name of output figure

        Returns
        -------
        res: list
            a list of roughness for DW from each snapshot
        """
        # find DW
        if self.DW is None:
            print('[Error]: No DW found! Please run `find_DW_surf` before using this function!')
            return
        SUM = self.DW

        # calculate roughness
        f_roughness = lambda x: np.sqrt(np.sum(np.square(np.array(x)-np.mean(x)))/len(np.array(x).ravel()))
        def cal_rsub(h_ary, div=1):
            """ Calculating roughness for subdivided area """
            if div==1:
                roughness = f_roughness(h_ary)
                r_sub = roughness.reshape((1,1))
                h_sub = np.mean(h_ary).reshape((1,1))
            else:
                hs_ary = self.subdiv_2dary(h_ary, div=div)
                ni, nj, _, _ = hs_ary.shape
                r_sub = np.zeros((ni,nj))-1
                h_sub = np.zeros((ni,nj))-1
                for i in range(ni):
                    for j in range(nj):
                        r_sub[i,j] = f_roughness(hs_ary[i,j])
                        h_sub[i,j] = np.mean(hs_ary[i,j])
                roughness = np.mean(r_sub) # average of sub-roughnesses
            return roughness, r_sub, h_sub

        # calculate defects 2D density
        def cal_defects2d_sub(f_defects, norm=0, size=(164,48,48), div=1, mode='d'):
            """ Calculating sub-density of defects ('d': all, 'dc': col, 'dr': row) """
            ax = 'xyz'[norm]
            Li, Lj = [size[_] for _ in range(3) if _!=norm]
            Xd = self.alloc_defects_2d(f_defects, norm=norm, size=size, div=div, is_dump=False)
            D = np.zeros((div,div))
            if mode=='dc':
                for j in range(div):
                    nd = 0
                    for i in range(div):
                        nd += len(Xd[(i,j)][ax])
                    D[:,j] = nd/(Li*Lj/div)
            elif mode=='dr':
                for i in range(div):
                    nd = 0
                    for j in range(div):
                        nd += len(Xd[(i,j)][ax])
                    D[i,:] = nd/(Li*Lj/div)
            else:
                for i,j in Xd:
                    area = (Li/div)*(Lj/div)
                    D[(i,j)] += len(Xd[(i,j)][ax])/area
            return D

        normal = [2, 1, 0] # correspond to x, y, z in coord/dipoRavg
        normal.pop(norm)
        Size = self.Size
        if figsize is None:
            figsize = (6,6) if div==1 else (12,6)

        # defects
        if mode=='defects':
            raw = parse_data(f_app)
            xyz_d = [raw[_] for _ in 'xyz']
            uxyz_d = [raw[f'u{_}'] for _ in 'xyz']
            h_d = xyz_d[norm]-0.5 # shift 0.5 to edge of unit cell
            xyz_d.pop(norm)
            uxyz_d.pop(norm)
            
        res = []
        res_sub = {}
        for i in SUM:
            roughness, r_sub, h_sub = cal_rsub(SUM[i], div=div)
            res.append(roughness)
            lx0, ly0 = SUM[i].shape
            lx1, ly1 = r_sub.shape
            for _x in range(lx1):
                for _y in range(ly1):
                    if (_x,_y) not in res_sub:
                        res_sub[(_x,_y)] = []
                    res_sub[(_x,_y)].append(r_sub[_x,_y])
            d_sub = np.zeros((div,div))
            if mode=='defects' and show_sub in ['d','dc','dr']:
                d_sub = cal_defects2d_sub(f_app, norm=norm, size=Size[0], div=div, mode=show_sub)
            # plot roughness
            if mode=='mean':
                dh = SUM[i]-np.mean(SUM[i])
                hmax = np.max(np.abs(dh))
                hmin = -hmax
            elif mode=='defects':
                dh = SUM[i]-np.mean(h_d)
                hmax, hmin = np.max(dh), np.min(dh)
            else:
                dh = SUM[i]
                hmax, hmin = np.max(dh), np.min(dh)
            rmax = np.max(np.abs([hmax, hmin]))
            if wlim is None:
                wmin, wmax = -rmax, rmax
            else:
                wmin, wmax = wlim
            size = list(Size[i])
            size.pop(norm)
            if display:
                if div==1:
                    fig, ax0 = plt.subplots(dpi=100, figsize=figsize)
                    ax = [ax0, ax0]
                    pos_h = pos_r = ax[0].imshow(dh, plt.cm.seismic, vmin=wmin, vmax=wmax, origin='lower')
                elif div>1:
                    fig, ax = plt.subplots(1,2, dpi=100, figsize=figsize, gridspec_kw={'wspace': 0.5})
                    pos_h = ax[0].imshow(dh, plt.cm.seismic, vmin=wmin, vmax=wmax, origin='lower')
                    if show_sub in ['d','dc','dr']:
                        pos_r = ax[1].imshow(d_sub, plt.cm.binary, vmin=0, vmax=vmax, origin='lower')
                    else:
                        pos_r = ax[1].imshow(r_sub, plt.cm.binary, vmin=0, vmax=vmax, origin='lower')
                    for _x in range(lx1):
                        for _y in range(ly1):
                            if not show_sub or show_sub in ['d','dc','dr']:
                                continue
                            ax[1].text(_y, _x, f'{h_sub[_x,_y]:.1f}', ha='center', va='center', rotation=45,
                                       color='w', fontsize=16, weight='bold')
                if mode=='defects':
                    ax[0].quiver(xyz_d[0], xyz_d[1], uxyz_d[0], uxyz_d[1], color='k', pivot='mid', headlength=1.5, headaxislength=1.5,
                                 scale_units='xy', angles='xy', scale=np.max(np.abs(uxyz_d)))
                # add div grid
                if not if_sfc:
                    for _x in np.arange(0, size[0], size[0]//div):
                        ax[0].axvline(x=_x-0.5, color='k', lw=.9)
                    for _y in np.arange(0, size[1], size[1]//div):
                        ax[0].axhline(y=_y-0.5, color='k', lw=.9)
                # x/ytick
                if size[0]>20:
                    ax[0].xaxis.set_major_locator(plt.MultipleLocator(10))
                if size[1]>20:
                    ax[0].yaxis.set_major_locator(plt.MultipleLocator(10))
                xticks0 = ax[0].get_xticks()
                yticks0 = ax[0].get_yticks()
                xticks1 = [-0.5+lx1/lx0*(_+0.5) for _ in xticks0 if 0<=_<lx0]
                yticks1 = [-0.5+ly1/ly0*(_+0.5) for _ in yticks0 if 0<=_<ly0]
                xticklabels1 = [int(_) for _ in xticks0 if 0<=_<lx0] # integer index
                yticklabels1 = [int(_) for _ in yticks0 if 0<=_<ly0] # integer index
                if div>1:
                    ax[1].set_xticks(xticks1)
                    ax[1].set_yticks(yticks1)
                    ax[1].set_xticklabels(xticklabels1)
                    ax[1].set_yticklabels(yticklabels1)
                # x/ylabel
                for _ in range(2):
                    ax[_].set_xlabel('ZYX'[normal[0]], fontsize=20)
                    ax[_].set_ylabel('ZYX'[normal[1]], fontsize=20)
                    ax[_].tick_params(which='major', labelsize=18, length=4, width=1)
                # colorbar0
                divider = make_axes_locatable(ax[0])
                cax = divider.append_axes('right', size="5%", pad=0.05)
                cbar = fig.colorbar(pos_h, cax=cax, fraction=0.045, pad=0.04)
                cbar.ax.tick_params(length=4, width=1, labelsize=18)
                cbar.ax.yaxis.set_major_locator(mpl.ticker.MaxNLocator(integer=True))
                if not if_sfc:
                    cticks = [hmin]
                    for _t in [0, hmax]:
                        if _t<=hmin or _t>=hmax:
                            continue
                        cticks.append(_t)
                    cbar.set_ticks([hmin]+cticks+[hmax])
                cbar.ax.get_yaxis().labelpad = 22
                if mode=='mean':
                    cylabel0 = rf'${{{"XYZ"[norm]}}}_{{DW}}-\langle {{{"XYZ"[norm]}}}_{{DW}}\rangle$ (u.c.)'
                elif mode=='defects':
                    if if_sfc:
                        cylabel0 = r'$\Delta x_{DW}$ (u.c.)'
                    else:
                        cylabel0 = rf'${{{"XYZ"[norm]}}}_{{DW}}-{{{"XYZ"[norm]}}}_{{d}}$ (u.c.)'
                else:
                    cylabel0 = r'$h$ (u.c.)'
                cbar.ax.set_ylabel(cylabel0, fontsize=20, fontweight='normal', rotation=270)
                if div>1:
                    # colorbar1
                    if show_sub in ['d','dc','dr']:
                        cbar_title = r'Defect density (u.c.$^{-2}$)'
                    else:
                        cbar_title = 'Roughness (u.c.)'
                    divider = make_axes_locatable(ax[1])
                    cax = divider.append_axes('right', size="5%", pad=0.05)
                    cbar = fig.colorbar(pos_r, cax=cax, fraction=0.045, pad=0.04)
                    cbar.ax.tick_params(length=4, width=1, labelsize=18)
                    cbar.ax.get_yaxis().labelpad = 22
                    cbar.ax.set_ylabel(cbar_title, fontsize=20, fontweight='normal', rotation=270)
                #plt.axis('scaled')
                # save figure
                if fig_out!=None:
                    rem, ext = fig_out.rsplit('.', maxsplit=1)
                    plt.savefig(f'{rem}_{i}.{ext}', bbox_inches='tight')
                else:
                    plt.show()
        plt.close('all')
        if show_sub:
            return res_sub
        return res

    def cal_DW_velocity(self, norm=0, div=1, dt=1, solid=None, x0=None, v0=0, Xd=None,
                        show_pos=False, display=False):
        """ Calculating DW 2D segment(s) velocity

        Parameters
        ----------
        norm: int
            normal of DW ([0]:x, 1:y, 2:z)
        div: int
            for subdivided area by quadratic bins with size=div
        dt: float
            time step in unit of ps (default: 1)
        solid: str
            lattice constant of u.c. ([None], 'bto', 'bst')
        x0: int
            initial position of DW (default: None)
        v0: float
            initial velocity (default: 0)
        Xd: int/dict
            showing indicative horizontal line @x=Xd if Xd is int, 
            elif output dict from `alloc_defects_2d` with the same div! [None]
        show_pos: boolean
            [False], True if showing DW position
        display: boolean
            [False], True if want to display the figure

        Returns
        -------
        res: dict
            dict of (positions, velocities) for sub-divided DW
        """
        # find DW
        if self.DW is None:
            print('[Error]: No DW found! Please run `find_DW_surf` before using this function!')
            return
        SUM = self.DW

        # functions
        def cal_vel(x, v0=0, a0=1, dt=1):
            _v0 = np.ones(np.shape(x[0]))*v0
            _v = a0*(np.array(x[1:])-np.array(x[:-1]))/dt
            v = np.concatenate([_v0[np.newaxis,:], _v])
            return v

        # calculate velocity
        normal = [2, 1, 0] # correspond to x, y, z in coord/dipoRavg
        normal.pop(norm)
        Size = self.Size
        xDW = []
        for i in SUM:
            h_ary = SUM[i]
            hs_ary = self.subdiv_2dary(h_ary, div=div)
            lv, lu, _, _ = hs_ary.shape
            h_sub = np.zeros((lv,lu))-1
            for iv in range(lv):
                for iu in range(lu):
                    h_sub[iv,iu] = np.mean(hs_ary[iv,iu])
            xDW.append(h_sub)
        if x0!=None:
            xDW.insert(0, np.ones((lv,lu))*x0)
        ind_u, ind_v = np.meshgrid(np.arange(lu), np.arange(lv))
        iuu = ind_u.ravel()
        ivv = ind_v.ravel()
        # DW velocity
        a0 = prefactor(solid=solid, mode='a') # lattice constant
        vDW = cal_vel(xDW, v0=v0, a0=a0, dt=dt)
        # sub-DW
        xdw_ary = np.array([_.ravel() for _ in xDW])
        vdw_ary = np.array([_.ravel() for _ in vDW])
        nrow, ncol = vdw_ary.shape
        res = OrderedDict()
        for i in range(ncol):
            iu, iv = iuu[i], ivv[i]
            res[(iv,iu)] = [xdw_ary[:,i].tolist(), vdw_ary[:,i].tolist()]
        # plot vDW
        if display:
            ncols = 1 if len(res)<=6 else len(res)//6+1
            if not show_pos:
                fig, ax0 = plt.subplots(1, 1, figsize=(6,4), dpi=100)
                ax = [ax0, ax0]
            else:
                fig, ax = plt.subplots(2, 1, figsize=(6,6), dpi=100)
            nd_mean = 0 if Xd is None else np.mean([len(Xd[_]['xyz'[norm]]) for _ in Xd])
            for t in res:
                xdw, vdw = res[t]
                pof = ''
                ls = 's-'
                if show_pos:
                    nd = 0
                    if Xd!=None and isinstance(Xd, dict):
                        nd = len(Xd[t]['xyz'[norm]])
                        pof = ' [{}]'.format(nd)
                        ax[0].axhline(y=np.mean(Xd[t]['xyz'[norm]]), ls='-', color='k', lw=.8)
                    ls = ls if nd>=nd_mean else 's--'
                    ax[0].plot(np.arange(nrow)*dt, xdw, ls, label=str(t)+pof)
                ax[1].plot(np.arange(nrow)*dt, vdw, ls, label=str(t)+pof)
            for _ in [0,1]:
                if not show_pos and _==0:
                    ax[1].axhline(y=0, ls='-', color='k', lw=.8)
                    continue
                elif show_pos:
                    if Xd!=None and isinstance(Xd, int):
                        ax[0].axhline(y=Xd, ls='-', color='k', lw=.8)
                    ax[1].axhline(y=0, ls='-', color='k', lw=.8)
                    ax[0].set_xticklabels([])
                    ax[0].set_ylabel(r'$x_{DW}$ (u.c.)', fontsize=20)
                ax[_].tick_params(which='major', labelsize=18)
                ax[_].legend(loc='center left', ncol=ncols, columnspacing=1, 
                             bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
            ax[1].set_xlabel('t (ps)', fontsize=20)
            if solid in ['bto', 'bst']:
                ax[1].set_ylabel(r'$v_{DW}$ ($\AA$/ps)', fontsize=20)
            else:
                ax[1].set_ylabel(r'$v_{DW}$ (u.c./ps)', fontsize=20)
            plt.show()
        return res

    def plot_DW_surf(self, norm=0, ui=0, shift=None, kernel=None, key=None, mode='hist', 
                     solid=None, scale=None, width=None, fig_out=None):
        """ Finding domain walls

        Parameters
        ----------
        norm: int
            normal of DW ([0]:x, 1:y, 2:z)
        ui: int
            changing component of u array ([0]:ux, 1:uy, 2:uz), applies to color as well
        shift: list
            displacement for the shifted DW surface along `norm`, in unit of u.c. (default: None)
        kernel: np.array
            [None], 3-dimensional numpy array as convolution kernel
        key: dict
            [None], e.g. {'ux', lambda u: 0 if u<0 else u}
        mode: str
            ['hist'], 'snap'
        solid: str
            [None], 'bto', 'bst'
        scale: float
            for `mode='snap'` only, larger scale, shorter arrow (default: None)
        width: float
            for `mode='snap'` only, shaft width (default: None)

        Returns
        -------
        DW: dict
            DW[i]: Dataset of DW
        """
        # find DW
        SUM = self.find_DW_surf(norm=norm, ui=ui, kernel=kernel, key=key)
        normal = [0, 1, 2]
        iw = normal.pop(norm)
        iu, iv = normal
        DW = OrderedDict()
        for i in SUM:
            v = {_:[] for _ in range(3)}
            v[iw] = SUM[i]
            lv, lu = SUM[i].shape
            v[iu], v[iv] = np.meshgrid(np.arange(lu), np.arange(lv))
            dh = SUM[i]-np.mean(SUM[i])
            hlim = np.max(np.abs(dh))
            # dipoles@DW
            Naviset = self.navigate_data(x=v[0].astype(int), y=v[1].astype(int), z=v[2].astype(int))
            DW[i] = Naviset[i]
            # x/y/z limit
            L_i, L_j = [self.Size[i][_] for _ in normal]
            i_label, j_label = ['XYZ'[_] for _ in normal]
            xyz_min = [0 if j!=norm else np.min(SUM[i]) for j,_ in enumerate(self.Size[i])]
            xyz_max = [_ if j!=norm else np.max(SUM[i]) for j,_ in enumerate(self.Size[i])]
            x_min, x_max = xyz_min[0], xyz_max[0]
            y_min, y_max = xyz_min[1], xyz_max[1]
            z_min, z_max = xyz_min[2], xyz_max[2]
            shift = [0] if shift is None else shift
            if mode=='hist':
                umin, umax = -0.3, 0.3
                bin_size = 0.01
                bins = np.arange(umin-bin_size/2, umax+bin_size, bin_size)*prefactor(solid)
                fig, ax = plt.subplots(figsize=(6,4), dpi=100)
                ulim = 0
                for d in shift:
                    Naviset = self.navigate_data(x=v[0].astype(int)+d, y=v[1].astype(int), z=v[2].astype(int))
                    uc = Naviset[i]['u'+'xyz'[ui]].ravel()*prefactor(solid)
                    ax.hist(uc, bins=bins, alpha=0.6, label=d)
                    if np.max(np.abs(uc))>ulim:
                        ulim = np.max(np.abs(uc))
                ax.set_xlim([-ulim*1.1, ulim*1.1])
                ax.tick_params(which='major', labelsize=18)
                if solid in ['bto', 'bst']:
                    ax.set_xlabel(r'$P_'+'xyz'[ui]+'$'+r' ($\mu C/cm^2$)', fontsize=20)
                else:
                    ax.set_xlabel(r'$u_'+'xyz'[ui]+'$'+r' ($\AA$)', fontsize=20)
                ax.set_ylabel('counts', fontsize=20)
                ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
                # save figure
                if fig_out!=None:
                    rem, ext = fig_out.rsplit('.', maxsplit=1)
                    plt.savefig(f'{rem}_{i}.{ext}', dpi=fig.dpi, bbox_inches='tight')
                else:
                    plt.show()
            elif mode=='snap':
                scale = 4 if scale is None else scale
                width = 0.0025 if width is None else width
                for d in shift:
                    fig, ax = plt.subplots(figsize=(6,6), dpi=150)
                    Naviset = self.navigate_data(x=v[0].astype(int)+d, y=v[1].astype(int), z=v[2].astype(int))
                    uu = Naviset[i]['u'+'xyz'[iu]].ravel()
                    uv = Naviset[i]['u'+'xyz'[iv]].ravel()
                    c = np.where(Naviset[i]['u'+'xyz'[ui]]>=0, 'r', 'b').ravel()
                    # dipoles
                    ax.quiver(v[iu], v[iv], uu, uv, color=c, units='width', pivot='mid', width=width, scale=scale)
                    # heights
                    pos_h = ax.imshow(dh, plt.cm.gist_gray, alpha=0.5, vmin=-hlim, vmax=hlim, origin='lower')
                    plt.axis('scaled')
                    # x/ylim
                    ax.set_xlim(xyz_min[iu]-1.5, xyz_max[iu]+0.5)
                    ax.set_ylim(xyz_min[iv]-1.5, xyz_max[iv]+0.5)
                    ax.set_xlabel(i_label+' axis', fontsize=20, labelpad=2)
                    ax.set_ylabel(j_label+' axis', fontsize=20, labelpad=2)
                    ax.tick_params(which='major', labelsize=18)
                    x_major_locator = MultipleLocator(10)
                    ax.xaxis.set_major_locator(x_major_locator)
                    y_major_locator = MultipleLocator(10)
                    ax.yaxis.set_major_locator(y_major_locator)
                    # frame
                    bwith = 1.2
                    for _ in ['bottom', 'left', 'top', 'right']:
                        ax.spines[_].set_linewidth(bwith)
                    # colorbar
                    divider = make_axes_locatable(ax)
                    cax = divider.append_axes('right', size="5%", pad=0.05)
                    cbar = fig.colorbar(pos_h, cax=cax, fraction=0.045, pad=0.04)
                    cbar.ax.tick_params(length=4, width=1, labelsize=16)
                    cbar.ax.get_yaxis().labelpad = 20
                    cbar.ax.set_ylabel(r'$h-\langle h\rangle$ (u.c.)', fontsize=20, fontweight='normal', rotation=270)
                    # save figure
                    if fig_out!=None:
                        rem, ext = fig_out.rsplit('.', maxsplit=1)
                        plt.savefig(f'{rem}_{i}_{d}.{ext}', dpi=fig.dpi, bbox_inches='tight')
                    else:
                        plt.show()
        plt.close('all')
        return DW

    def count_phase(self, u_crit=0.04):
        """ Counting phases
        
        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >=u_crit, 0 if <u_crit
        
        Returns
        -------
        SUM: dict
            SUM[i] = {phase: #0, phase1: #1, ...}
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        # operators
        def f_comp(v):
            ans = None
            if abs(v)<abs(u_crit):
                ans = 0 # empirical criterion
            else:
                if v>0:
                    ans = 1
                elif v<0:
                    ans = -1
            return ans
        
        SUM = OrderedDict()
        for i in Dataset:
            ux = Dataset[i]['ux'].ravel()
            uy = Dataset[i]['uy'].ravel()
            uz = Dataset[i]['uz'].ravel()
            res = {}
            for _ux, _uy, _uz in zip(ux,uy,uz):
                if np.any([np.ma.is_masked(_) for _ in (_ux,_uy,_uz)]):
                    continue # skip masked values
                v_dipo = tuple(map(f_comp, [_ux, _uy, _uz]))
                if v_dipo not in res:
                    res[v_dipo] = 0
                res[v_dipo]+=1
            SUM[i] = res
        return SUM

    def catalog_phase(self, u_crit=0.04):
        """ Categorizing dipoles into groups of phases
        
        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        
        Returns
        -------
        res: dict
            res[i]: ([phase, phase1, ...], [#0, #1, ...])
        """
        # count phase
        SUM = self.count_phase(u_crit=u_crit)
        # catalog phases
        res = OrderedDict()
        for i in SUM:
            ph_sum = {_: 0 for _ in 'CTOR'}
            labels = []
            counts = []
            for vp in SUM[i]:
                ph = 'CTOR'[sum(np.abs(vp))]
                postfix = str(ph_sum[ph])
                labels.append( f'${ph}_{i}^{postfix}$' )
                counts.append( SUM[i][vp] )
                ph_sum[ph]+=1
            res[i] = (labels, counts)
        return res

    def check_dw_type(self, u_crit=0.04, key0=None, key1=None):
        """ Checking DW type

        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        key0: dict
            condition of domain0, e.g. {'uz': lambda u: u>0}
        key1: dict
            condition of domain1, e.g. {'uz': lambda u: u<0}

        Returns
        -------
        res: dict
            res[i]: (phase0, phase1, angle)
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        def domain_stat(Dataset, key):
            SUM = OrderedDict()
            for i in Dataset:
                DA = Dipo_Analysis(self.files[i])
                DA.load_data()
                DA.select_data(i_list=self.i_list, j_list=self.j_list, k_list=self.k_list, exclude=self.exclude, key=self.key)
                DA.select_data(key=key)
                res = DA.catalog_phase(u_crit=u_crit)[0]
                phase = sorted(res[0],key=lambda x: res[1][res[0].index(x)])[-1][1] # phase with the most counts
                ux_mean, ux_std = DA.cal_stat(ui=0)
                uy_mean, uy_std = DA.cal_stat(ui=1)
                uz_mean, uz_std = DA.cal_stat(ui=2)
                SUM[i] = [phase, (ux_mean[0], uy_mean[0], uz_mean[0])]
            return SUM
        f_norm = lambda v: np.sqrt(np.sum(np.array(v)**2))
        f_angle = lambda u,v: np.arccos(np.dot(u,v)/(f_norm(u)*f_norm(v)))*180/np.pi
        Domain0 = domain_stat(Dataset, key0)
        Domain1 = domain_stat(Dataset, key1)
        res = {}
        for i in Dataset:
            ph0, u_ary0 = Domain0[i]
            ph1, u_ary1 = Domain1[i]
            angle = f_angle(u_ary0, u_ary1)
            res[i] = (ph0, ph1, angle)
        return res

    def plot_corr2D(self, norm=0, ui0=0, ui1=0, dmax=0, spacing=1, figsize=(6,6), fig_out=None):
        """ Plotting correlation 2D map

        Parameters
        ----------
        norm: int
            normal of 2D plane ([0]:x, 1:y, 2:z)
        ui0: int
            component of reference u array0 ([0]:ux, 1:uy, 2:uz)
        ui1: int
            component of comparison u array1 ([0]:ux, 1:uy, 2:uz)
        dmax: int
            maximum shift for +/- directions in 2D plane
        spacing: int
            x/y-ticks spacing (default: 1)
        figsize: tuple(2)
            size of output figure (default: (6,6))
        fig_out: bool
            [None], False if not display figure

        Returns
        -------
        Corr_Map: dict
            dict of 2D correlation map
        """
        normal = [0, 1, 2]
        normal.pop(norm)
        d_i, d_j = dmax, dmax
        Corr_Map = {_: np.zeros((2*d_i+1, 2*d_j+1)) for _ in self.Dataset} # -dmax to +dmax
        for i in range(-d_i, d_i+1):
            for j in range(-d_j, d_j+1):
                vshift = [0, 0, 0]
                vshift[normal[0]] = i
                vshift[normal[1]] = j
                res = self.cal_corr(ui0=ui0, ui1=ui1, vshift=vshift, display=False)
                for _ in self.Dataset:
                    Corr_Map[_][d_j-j, i-(-d_i)] = res[_]
        if fig_out is not False:
            for _ in Corr_Map:
                fig,ax = plt.subplots(dpi=100, figsize=figsize)
                pos_cr = ax.imshow(Corr_Map[_], plt.cm.coolwarm, vmin=-1, vmax=1, origin='lower')
                ax.set_xticks(range(0, 2*d_i+1, spacing), range(-d_i, d_i+1, spacing))
                ax.set_yticks(range(0, 2*d_j+1, spacing), range(-d_j, d_j+1, spacing))
                ax.set_xlabel('XYZ'[normal[0]]+' axis', fontsize=20)
                ax.set_ylabel('XYZ'[normal[1]]+' axis', fontsize=20)
                plt.tick_params(which='major', labelsize=18, length=4, width=1)
                # colorbar
                cbar = fig.colorbar(pos_cr, fraction=0.045, pad=0.04)
                cbar.ax.tick_params(length=4, width=1, labelsize=16)
                cbar.ax.get_yaxis().labelpad = 18
                cbar.ax.set_ylabel(r'$C_{i,j}$', fontsize=20, fontweight='normal', rotation=270)
                # save figure
                if fig_out!=None:
                    rem, ext = fig_out.rsplit('.', maxsplit=1)
                    plt.savefig(f'{rem}_{i}.{ext}', bbox_inches='tight')
                else:
                    plt.show()
            plt.close('all')
        return Corr_Map

    def plot_hist(self, ui=0, umin=-0.2, umax=0.2, bin_size=0.01, solid=None, is_norm=False, is_sort=False,
                  is_urev_list=None, appendix=None, is_urev_ap=False, figsize=(8,6), fig_out=None):
        """ Plotting histogram of dipoles

        Parameters
        ----------
        ui: int
            component of reference u array ([0]:ux, 1:uy, 2:uz, 3: all)
        umin: float
            u maximum for the histogram
        umax: float
            u minimum for the histogram
        bin_size: float
            bin size of the histogram
        solid: str
            for prefactor of u. [None]:1, 'bto', 'bst'
        is_norm: bool
            [False], True if normalized data
        is_sort: bool
            [False], True if sorting u arrays based on np.abs(np.mean(u))
        is_urev_list: list or bool
            None, [True] if output data w.r.t the reversed u-axis
        appendix: str
            filename for reference histogram
        if_urev_ap: bool
            [False], True if output data w.r.t the reversed u-axis

        Returns
        -------
        None
            display/output histogram figures
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        uf = prefactor(solid=solid)
        # calculate histogram & assign styles
        ind = {0:'ux', 1:'uy', 2:'uz'}
        def _load_app(Cls, ui, umin, umax, bin_size, is_norm, is_urev_list):
            if ui in [0, 1, 2]:
                hist, bins_w0 = Cls.cal_hist(ui, umin, umax, bin_size, is_norm, is_urev_list)
                Hist = {ind[ui]: hist}
                u_mean, u_std = Cls.cal_stat(ui, is_urev_list)
                u_mean = {ind[ui]: u_mean}
                u_std = {ind[ui]: u_std}
                u_abs_mean, _ = Cls.cal_stat(ui, is_urev_list, True)
                u_abs_mean = {ind[ui]: u_abs_mean}
                uind = [ind[ui]]
                colors = ['rgb'[ui]]
            elif ui==3:
                hist_ux, bins_w0 = Cls.cal_hist(0, umin, umax, bin_size, is_norm, is_urev_list)
                hist_uy, bins_w0 = Cls.cal_hist(1, umin, umax, bin_size, is_norm, is_urev_list)
                hist_uz, bins_w0 = Cls.cal_hist(2, umin, umax, bin_size, is_norm, is_urev_list)
                Hist = {'ux': hist_ux, 'uy': hist_uy, 'uz': hist_uz}
                u_stat = [Cls.cal_stat(_, is_urev_list) for _ in range(3)]
                u_mean = {ind[_u]:_v[0] for _u,_v in zip(range(3), u_stat)}
                u_std = {ind[_u]:_v[1] for _u,_v in zip(range(3), u_stat)}
                u_abs_stat = [Cls.cal_stat(_, is_urev_list, True) for _ in range(3)]
                u_abs_mean = {ind[_u]:_v[0] for _u,_v in zip(range(3), u_abs_stat)}
                uind = ['ux', 'uy', 'uz']
                colors = ['r', 'g', 'b']
            bins_w0 = bins_w0 + bin_size/2 # shift to the center of the bin
            return Hist, bins_w0, u_mean, u_std, u_abs_mean, uind, colors

        # plotting histogram
        res = _load_app(self, ui, umin, umax, bin_size, is_norm, is_urev_list)
        Hist, bins_w0, u_mean, u_std, u_abs_mean, uind, colors = res
        for i in Dataset:
            if appendix==None:
                fig, ax = plt.subplots(figsize=figsize, dpi=100)
                ax = [ax]
            else:
                fig, ax = plt.subplots(2, 1, figsize=figsize, dpi=100, sharex=True,
                                       gridspec_kw={'height_ratios': [2, 5], 'hspace': 0.16})
                ax = ax[::-1]
            uind = sorted(uind, key=lambda x: np.abs(u_abs_mean[x][i])) if is_sort else uind
            for u, c in zip(uind, colors):
                ax[0].bar(bins_w0[:-1]*uf, Hist[u][i], bin_size*uf, color=c, alpha=0.6, label=u)
                ax[0].axvline(x=u_mean[u][i]*uf, color=c, linestyle='-', lw=.9)

            # appendix
            if appendix!=None:
                lls = ['--', ':', '-']
                Cls_ap = Dipo_Analysis(appendix)
                Cls_ap.load_data()
                res_ap = _load_app(Cls_ap, ui, umin, umax, bin_size, is_norm, is_urev_ap)
                Hist_ap, bins_w0_ap, u_mean_ap, u_std_ap, u_abs_mean_ap, uind_ap, colors_ap = res_ap
                bin_ap = [bins_w0_ap[0]-bin_size]+list(bins_w0_ap[:-1])+[bins_w0_ap[-1]]
                bin_ap = np.array(bin_ap)+bin_size/2
                f_add0_ap = lambda ary: [0]+list(ary)+[0]
                uind_ap = sorted(uind_ap, key=lambda x: np.abs(u_abs_mean_ap[x][0])) if is_sort else uind_ap
                for ui, (u, c) in enumerate(zip(uind_ap, colors_ap)):
                    _tag = 'u_{'+str(ui+1)+'}' if is_sort and len(uind)==3 else 'u_{'+u[-1]+'}'
                    _tag = _tag.replace('u','p') if uf!=1 else _tag
                    ax[0].plot(bin_ap*uf, f_add0_ap(Hist_ap[u][0]), drawstyle='steps', color=c, lw=1.1)
                    ax[1].axvline(x=u_mean_ap[u][0]*uf, color='grey', linestyle=lls[ui], label=r'$\langle$'+rf'${_tag}$'+r'$\rangle$')
                for u, c in zip(uind, colors):
                    ax[1].axvline(x=u_mean[u][i]*uf, color=c, linestyle='-', lw=.9)
                ax[1].set_ylim([0,1])
                # yticks
                ax[1].set_yticks([])
                # legend
                ax[1].legend(loc=0, frameon=False, labelspacing=0., prop={'size':18, 'weight': 'normal'})

            # xlim
            ax[0].set_xlim([(umin-bin_size)*uf, (umax+bin_size)*uf])

            # x/y labels
            star = '' if (is_urev_list==None and is_urev_ap==False) else '*'
            if uf==1:
                ax[0].set_xlabel('u'+star+r' ($\AA$)',fontsize=20, labelpad=None)
            else:
                ax[0].set_xlabel('P'+star+r' ($\mu C/cm^2$)',fontsize=20, labelpad=None)
            ylabel = 'counts' if not is_norm else 'density'
            ax[0].set_ylabel(ylabel, fontsize=20, labelpad=None)
            ax[0].tick_params(which='major', labelsize=18)
            # legend
            if is_sort and len(uind)==3:
                uind_leg = ['u1', 'u2', 'u3']
            else:
                uind_leg = uind
            if uf!=1:
                uind_leg = [_.replace('u','p') for _ in uind_leg]
            ax[0].legend(uind_leg, loc=0, frameon=False, prop={'size':18, 'weight': 'normal'})

            # frame
            bwith = 1
            for _ax in ax:
                _ax.spines['top'].set_visible(False)
                _ax.spines['right'].set_visible(False)
                _ax.spines['left'].set_visible(False)
                _ax.spines['bottom'].set_linewidth(bwith)
            ax[0].spines['left'].set_visible(True)
            ax[0].spines['left'].set_linewidth(bwith)

            # save figure
            if fig_out!=None:
                rem, ext = fig_out.rsplit('.', maxsplit=1)
                plt.savefig(f'{rem}_{i}.{ext}', bbox_inches='tight')
            else:
                plt.show()
        plt.close('all')

    def plot_vlhist(self, ui=0, tags=None, is_sort=False, figsize=None, fig_out=None):
        """ Plotting violinplot of dipoles

        Parameters
        ----------
        ui: int
            component of reference u array ([0]:ux, 1:uy, 2:uz, 3: all)
        tags: list
            tags for xticklabels
        is_sort: bool
            [False], True if sorting u arrays based on np.abs(np.mean(u))

        Returns
        -------
        None
            display/output violinplot figures
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        ind = {0:'ux', 1:'uy', 2:'uz'}
        if ui in [0,1,2]:
            uind = [ind[ui]]
            colors = ['rgb'[ui]]
        else:
            uind = ['ux', 'uy', 'uz']
            colors = ['r', 'g', 'b']

        figsize = (2*len(Dataset),5) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        for i in Dataset:
            xind = i-0.25 if len(uind)==3 else i
            if is_sort:
                uind = sorted(uind, key=lambda x: np.abs(np.ma.compressed(np.mean(Dataset[i][x]))) )
            for u, c in zip(uind, colors):
                vp = ax.violinplot(np.ma.compressed(Dataset[i][u]).ravel(), [xind], widths=0.25, showmeans=True, showextrema=True)
                xind += 0.25
                # customized violinplot
                for _ in ['cbars','cmins','cmaxes','cmeans']:
                    vp[_].set_edgecolor(c)
                    vp[_].set_linewidth(1)
                for _ in ['cmeans']:
                    vp[_].set_edgecolor(c)
                    vp[_].set_linewidth(0)
                for _ in vp['bodies']:
                    _.set_facecolor(c)

        # x/y limits
        ax.set_ylim([-0.2, 0.2])

        # x/y labels
        ax.set_ylabel(r'u ($\AA$)',fontsize=20, labelpad=None)
        plt.tick_params(which='major', labelsize=18, length=4, width=1)
        plt.tick_params(which='minor', labelsize=18, length=0)

        # ticks
        tags = np.arange(len(Dataset)) if tags==None else tags
        ax.set_xticks(np.arange(len(Dataset)),)
        ax.set_xticklabels(tags, rotation=90)

        # frame
        bwith = 1
        ax.spines['bottom'].set_linewidth(bwith)
        ax.spines['left'].set_linewidth(bwith)
        ax.spines['top'].set_linewidth(bwith)
        ax.spines['right'].set_linewidth(bwith)

        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], color=c, alpha=0.5, lw=6) for c in colors]
        if is_sort and len(uind)==3:
            uind = ['u1', 'u2', 'u3']
        ax.legend(custom_lines, uind, loc='center left', bbox_to_anchor=(1, 0.5),
                  frameon=False, prop={'size':18, 'weight': 'normal'})

        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_vhist(self, ui=0, tags=None, spacing=0, umin=-0.2, umax=0.2, bin_size=0.01, solid=None, is_norm=False, is_sort=False,
                   is_urev_list=None, colors=None, appendix=None, is_urev_ap=False, colors_ap=None, ulim=(-0.2,0.2), figsize=(6,4), fig_out=None):
        """ Plotting multiple vertical histogram plot of dipoles

        Parameters
        ----------
        ui: int
            component of reference u array ([0]:ux, 1:uy, 2:uz, 3: all)
        tags: list
            tags for xticklabels
        spacing: float
            spacing between each dataset (default: 0)
        umin: float
            u maximum for the histogram
        umax: float
            u minimum for the histogram
        bin_size: float
            bin size of the histogram
        solid: str
            for prefactor of u. [None]:1, 'bto', 'bst'
        is_norm: bool
            [False], True if normalized data
        is_sort: bool
            [False], True if sorting u arrays based on np.abs(np.mean(u))
        is_urev_list: list or bool
            None, [True] if output data w.r.t the reversed u-axis
        colors: list
            [None], colors for main u component(s)
        appendix: str or tuple/list
            filename(s) for reference histogram
        if_urev_ap: bool
            [False], True if output data w.r.t the reversed u-axis
        colors_ap: list
            [None], colors for reference u component(s)
        ulim: tuple/list
            [ulo, uhi] lower/upper limit of y(u)-range

        Returns
        -------
        None
            display/output violinplot figures
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        uf = prefactor(solid=solid)
        # calculate histogram & assign styles
        ind = {0:'ux', 1:'uy', 2:'uz'}
        def _load_app(Cls, ui, umin, umax, bin_size, is_norm, is_urev_list, colors):
            if ui in [0, 1, 2]:
                hist, bins_w0 = Cls.cal_hist(ui, umin, umax, bin_size, is_norm, is_urev_list)
                Hist = {ind[ui]: hist}
                u_mean, u_std = Cls.cal_stat(ui, is_urev_list)
                u_mean = {ind[ui]: u_mean}
                u_std = {ind[ui]: u_std}
                u_abs_mean, _ = Cls.cal_stat(ui, is_urev_list, True)
                u_abs_mean = {ind[ui]: u_abs_mean}
                uind = [ind[ui]]
                colors = ['rgb'[ui]] if colors is None else colors
            elif abs(ui)==3: # +3 or -3
                hist_ux, bins_w0 = Cls.cal_hist(0, umin, umax, bin_size, is_norm, is_urev_list)
                hist_uy, bins_w0 = Cls.cal_hist(1, umin, umax, bin_size, is_norm, is_urev_list)
                hist_uz, bins_w0 = Cls.cal_hist(2, umin, umax, bin_size, is_norm, is_urev_list)
                Hist = {'ux': hist_ux, 'uy': hist_uy, 'uz': hist_uz}
                u_stat = [Cls.cal_stat(_, is_urev_list) for _ in range(3)]
                u_mean = {ind[_u]:_v[0] for _u,_v in zip(range(3), u_stat)}
                u_std = {ind[_u]:_v[1] for _u,_v in zip(range(3), u_stat)}
                u_abs_stat = [Cls.cal_stat(_, is_urev_list, True) for _ in range(3)]
                u_abs_mean = {ind[_u]:_v[0] for _u,_v in zip(range(3), u_abs_stat)}
                uind = ['ux', 'uy', 'uz']
                colors = ['r', 'g', 'b'] if colors is None else colors
            bins_w0 = bins_w0 + bin_size/2 # shift to the center of the bin
            return Hist, bins_w0, u_mean, u_std, u_abs_mean, uind, colors

        # appendix
        if isinstance(appendix, (list,tuple)):
            App = appendix
        elif isinstance(appendix, str):
            App = [appendix]*len(Dataset)

        # plot histograms
        res = _load_app(self, ui, umin, umax, bin_size, is_norm, is_urev_list, colors)
        Hist, bins_w0, u_mean, u_std, u_abs_mean, uind, colors = res
        nrow = 3 if ui==-3 else 1
        ncol = len(Dataset)
        figsize = (2.2*ncol,5) if figsize==None else figsize
        fig, ax = plt.subplots(nrow, ncol, figsize=figsize, dpi=100)
        ax = [ax] if ncol==1 and nrow==1 else ax.ravel()
        hgap = 0.02
        for i in Dataset:
            uind = sorted(uind, key=lambda x: np.abs(u_abs_mean[x][i])) if is_sort else uind
            # appendix
            if appendix!=None:
                Cls_ap = Dipo_Analysis(App[i])
                Cls_ap.load_data()
                res_ap = _load_app(Cls_ap, ui, umin, umax, bin_size, is_norm, is_urev_ap, colors_ap)
                Hist_ap, bins_w0_ap, u_mean_ap, u_std_ap, u_abs_mean_ap, uind_ap, colors_ap = res_ap
                uind_ap = sorted(uind_ap, key=lambda x: np.abs(u_abs_mean_ap[x][0])) if is_sort else uind_ap
                dh = 0
                for j,(u, c) in enumerate(zip(uind_ap, colors_ap)):
                    hist = Hist_ap[u][0]/np.max(Hist_ap[u][0])*(0.4-hgap) # normalized
                    ih_nonzero = np.where(hist!=0)[0]
                    ib_lo, ib_hi = bins_w0_ap[ih_nonzero[0]]-bin_size/2, bins_w0[ih_nonzero[-1]]+bin_size/2
                    # barh
                    ax[i+j*ncol*(ui==-3)].vlines(x=dh-hgap, ymin=ib_lo*uf, ymax=ib_hi*uf, color=c, linestyle='-', lw=1.)
                    ax[i+j*ncol*(ui==-3)].hlines(y=u_mean_ap[u][0]*uf, xmin=dh-1/2, xmax=dh-hgap, color='k', linestyle='-', lw=2., zorder=0)
                    #ax[i+j*ncol*(ui==-3)].hlines(y=ib_lo*uf, xmin=dh-1/2, xmax=dh-hgap, color=c, linestyle='-', lw=1.)
                    #ax[i+j*ncol*(ui==-3)].hlines(y=ib_hi*uf, xmin=dh-1/2, xmax=dh-hgap, color=c, linestyle='-', lw=1.)
                    ax[i+j*ncol*(ui==-3)].barh(bins_w0[:-1]*uf, width=hist, height=bin_size*uf, left=dh-hist-hgap, color=c, alpha=.5, label=u)
                    dh+=1*(ui!=-3)
            h0 = -1*hgap if appendix!=None else 1/2
            dh = 0
            for j,(u, c) in enumerate(zip(uind, colors)):
                hist = Hist[u][i]/np.max(Hist[u][i])*(0.4-hgap+(0.4+hgap)*(appendix==None)) # normalized
                ih_nonzero = np.where(hist!=0)[0]
                ib_lo, ib_hi = bins_w0[ih_nonzero[0]]-bin_size/2, bins_w0[ih_nonzero[-1]]+bin_size/2
                # kde
                #import scipy.stats as sts
                #kde = sts.gaussian_kde(Hist[u][i])
                #_y = bins_w0[:-1]
                #_x = kde.pdf(_y)+dh
                #ax[i+j*ncol*(ui==-3)].plot(_x, _y, ls='-', color=c, lw=1)
                #hmax = np.max(kde.pdf(_y))
                # barh
                ax[i+j*ncol*(ui==-3)].vlines(x=dh-h0*(appendix!=None), ymin=ib_lo*uf, ymax=ib_hi*uf, color=c, linestyle='-', lw=1.)
                ax[i+j*ncol*(ui==-3)].hlines(y=u_mean[u][i]*uf, xmin=dh-h0, xmax=dh+1/2, color='k', linestyle='-', lw=2., zorder=0)
                #ax[i+j*ncol*(ui==-3)].hlines(y=ib_lo*uf, xmin=dh-h0, xmax=dh+1/2, color=c, linestyle='-', lw=1.)
                #ax[i+j*ncol*(ui==-3)].hlines(y=ib_hi*uf, xmin=dh-h0, xmax=dh+1/2, color=c, linestyle='-', lw=1.)
                ax[i+j*ncol*(ui==-3)].barh(bins_w0[:-1]*uf, width=hist, height=bin_size*uf, left=dh-hist/2*(appendix==None)-h0*(appendix!=None), color=c, alpha=.5, label=u)
                # u=0
                ax[i+j*ncol*(ui==-3)].axhline(y=0, color='k', linestyle=':', lw=1.5, zorder=0)
                dh+=1*(ui!=-3)

        # xlim/ylim
        xmax = 0.5 if ui!=3 else 2.5
        for _ax in ax:
            _ax.set_xlim([-0.5-spacing, xmax+spacing])
            if ulim is None:
                _ax.set_ylim([(umin-bin_size)*uf, (umax+bin_size)*uf])
            else:
                _ax.set_ylim(ulim)

        # x/y labels
        if is_sort and len(uind)==3:
            uind = ['u1', 'u2', 'u3']
        star = '' if (is_urev_list==None and is_urev_ap==False) else '*'
        for i,_ax in enumerate(ax):
            if i in list(range(0,nrow*ncol,ncol)):
                sub = r'$_{}$'.format(uind[i//ncol][-1]) if ui==-3 else ''
                if uf==1:
                    _ax.set_ylabel(r'u'+sub+star+r' ($\AA$)',fontsize=20, fontweight='normal', labelpad=None)
                else:
                    _ax.set_ylabel(r'P'+sub+star+r' ($\mu C/cm^2$)',fontsize=20, fontweight='normal', labelpad=None)
            _ax.tick_params(which='major', labelsize=20, length=4)
            _ax.tick_params(which='minor', labelsize=20, length=0)
        tags = list(range(1,ncol+1)) if tags==None else tags
        for i in range(ncol):
            xticks = [0] if ui!=3 else [1]
            ax[i+(nrow-1)*ncol*(ui==-3)].set_xticks(xticks)
            ax[i+(nrow-1)*ncol*(ui==-3)].set_xticklabels([f'{tags[i]}'], fontsize=20, fontweight='normal', rotation=0, va='top')

        # legend
        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], color=c, alpha=.7, lw=6) for c in colors]
        if uf==1:
            uleg = [r'u$_{}$'.format(_[-1]) for _ in uind]
        else:
            uleg = [r'P$_{}$'.format(_[-1]) for _ in uind]
        ax[-1-ncol*(ui==-3)].legend(custom_lines, uleg, loc='center left', bbox_to_anchor=(1, 0.5),
                      frameon=False, prop={'size':18, 'weight': 'normal'})

        # frame/ticks
        for i,_ax in enumerate(ax):
            _ax.xaxis.set_tick_params(width=2)
            _ax.yaxis.set_tick_params(width=2)
            _ax.spines['left'].set_linewidth(2)
            _ax.spines['bottom'].set_linewidth(2)
            _ax.spines['top'].set_visible(False)
            _ax.spines['right'].set_visible(False)
            if i not in list(range(0,nrow*ncol,ncol)):
                _ax.set_yticks([])
                _ax.spines['left'].set_visible(False)
            if i not in list(range((nrow-1)*ncol, nrow*ncol)):
                _ax.set_xticks([])
                _ax.spines['bottom'].set_visible(False)

        # save figure
        plt.tight_layout(w_pad=0)
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_hist2d(self, norm=2, bin_size=None, umax=None, vmax=None, solid=None, f_reduce=None,
                    show_u_ratio=None, figsize=(5,5), fig_out=None):
        """ Plotting 2D histogram of dipoles

        Parameters
        ----------
        norm: int
            0: x, 1: y, [2]: z as 2D plane normal
        bin_size: float
            [None], size of 2D bins in unit of `solid`
        umax: float
            [None], maximum of u in unit of `solid`
        vmax: int
            [None], maximum cap for histogram
        solid: str
            [None], 'bto', 'bst'
        f_reduce: function
            [None], e.g., lambda x: np.sum(x, axis=0, keepdims=True)
        show_u_ratio: bool/int
            [None], 0/1 for showing ratio of ui/uj dipoles for >= and < 0 (or bin_size/2)
        figsize: tuple(2)
            size of figure
        fig_out: str
            output file name of figure

        Returns
        -------
        None
        """
        ind = {0:'ux', 1:'uy', 2:'uz'}
        normal = [0, 1, 2]
        normal.pop(norm)
        ui, uj = normal
        # plot histogram
        hist, bins_w0 = self.cal_hist2d(norm=norm, bin_size=bin_size, umax=umax, solid=solid,
                                        f_reduce=f_reduce)
        bmin, bmax = np.min(bins_w0), np.max(bins_w0)
        for _h in hist:
            fig, ax = plt.subplots(figsize=figsize, dpi=100)
            hax = ax.imshow(_h, cmap=plt.cm.gist_heat_r, vmin=0, vmax=vmax, origin='lower',
                            interpolation='nearest', extent=[bmin,bmax,bmin,bmax])
            if show_u_ratio is not None:
                ur = show_u_ratio if show_u_ratio in (0,1) else 1
                ib = np.where(bins_w0>=0)[0][0]
                n_tot = np.sum(_h)
                if ur==0:
                    n_1 = np.sum(_h[:,ib:])
                    r_1 = n_1/n_tot
                    r_0 = 1-r_1
                    ax.axvline(x=bins_w0[ib], color='k', linestyle='--', lw=1)
                    ax.text(bmin*0.6, bmin*0.95, f'{r_0*100:4.1f}%', size=20)
                    ax.text(bin_size, bmin*0.95, f'{r_1*100:4.1f}%', size=20)
                else: # ur=1
                    n_1 = np.sum(_h[ib:,:])
                    r_1 = n_1/n_tot
                    r_0 = 1-r_1
                    ax.axhline(y=bins_w0[ib], color='k', linestyle='--', lw=1)
                    ax.text(bmax*0.5, -bin_size*2.5, f'{r_0*100:4.1f}%', size=20)
                    ax.text(bmax*0.5, bin_size, f'{r_1*100:4.1f}%', size=20)
            # tick params
            ax.tick_params(which='major', labelsize=18)
            locator = 0.1 if solid is None else 20
            ax.xaxis.set_major_locator(plt.MultipleLocator(locator))
            ax.yaxis.set_major_locator(plt.MultipleLocator(locator))
            # x/ylim
            if umax is not None:
                ax.set_xlim([-umax-bin_size, umax+bin_size])
                ax.set_ylim([-umax-bin_size, umax+bin_size])
            # x/y labels
            if solid is None:
                ax.set_xlabel(fr'$u_{ind[ui][-1]}$'+r' ($\AA)', fontsize=20, labelpad=None)
                ax.set_ylabel(fr'$u_{ind[uj][-1]}$'+r' ($\AA)', fontsize=20, labelpad=None)
            else:
                ax.set_xlabel(fr'$P_{ind[ui][-1]}$'+r' ($\mu C/cm^2$)',fontsize=20, labelpad=None)
                ax.set_ylabel(fr'$P_{ind[uj][-1]}$'+r' ($\mu C/cm^2$)',fontsize=20, labelpad=None)
            # colorbar
            cbar = fig.colorbar(hax, fraction=0.045, pad=0.04)
            cbar.ax.tick_params(labelsize=16)
            cbar.ax.get_yaxis().labelpad=20
            cbar.ax.set_ylabel('freq.', fontsize=20, fontweight='normal', rotation=270)
            cbar.ax.yaxis.set_major_locator(mpl.ticker.MaxNLocator(integer=True))
            ax.set_aspect('equal')
            if fig_out!=None:
                rem, ext = fig_out.rsplit('.', maxsplit=1)
                plt.savefig(f'{rem}_{i}.{ext}', bbox_inches='tight')
            else:
                plt.show()
        plt.close('all')

    def plot_u_evo(self, u_mode=0, solid=None, dt=1, figsize=(6,4), fig_out=None):
        """ Plotting averaged polarization evolution

        Parameters
        ----------
        u_mode: int
            [0]: ux, 1: uy, 2: uz
        solid: str
            [None], 'bto', 'bst'
        dt: float
            [1], timestep for snapshots (in ps)
        figsize: tuple(2)
            size of figure
        fig_out: str
            output file name of figure

        Returns
        -------
        None
        """
        uf = prefactor(solid=solid)
        ind = {0:'ux', 1:'uy', 2:'uz'}
        if u_mode in (0,1,2):
            ui = u_mode
        # calculate u_evo
        u_evo = self.cal_u_evo(ui=ui)
        # plot u_evo
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        t_ary = np.arange(len(u_evo))*dt
        u_ary = np.array(u_evo)*uf
        ax.plot(t_ary, u_ary, 'b-s')
        # x/y axes
        ax.tick_params(which='major', labelsize=18)
        ax.set_xlabel('t (ps)', fontsize=20)
        if solid is None:
            ax.set_ylabel(fr'$u_{ind[ui][-1]}$'+r' ($\AA)', fontsize=20)
        else:
            ax.set_ylabel(fr'$P_{ind[ui][-1]}$'+r' ($\mu C/cm^2$)', fontsize=20)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        # legend
        ax.legend(loc=0, frameon=False, prop={'size':18, 'weight': 'normal'})
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def g_plot_u_evo(self, u_mode=0, solid=None, dt=1, labels=None, figsize=(6,4), fig_out=None):
        """ Plotting averaged polarization evolution (for `assign_group`)

        Parameters
        ----------
        u_mode: int
            [0]: ux, 1: uy, 2: uz
        solid: str
            [None], 'bto', 'bst'
        dt: float
            [1], timestep for snapshots (in ps)
        labels: list
            labels for groups
        figsize: tuple(2)
            size of figure
        fig_out: str
            output file name of figure

        Returns
        -------
        None
        """
        uf = prefactor(solid=solid)
        ind = {0:'ux', 1:'uy', 2:'uz'}
        if u_mode in (0,1,2):
            ui = u_mode
        # calculate u_evo
        g_u_evo = self.g_cal_u_evo(ui=ui)
        # plot u_evo
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        colors = 'rgbmyck'
        for ig in g_u_evo:
            label = labels[ig] if labels is not None else ig
            u_evo = g_u_evo[ig]
            t_ary = np.arange(len(u_evo))*dt
            u_ary = np.array(u_evo)*uf
            ax.plot(t_ary, u_ary, '-s', color=colors[ig%7], label=label)
        # x/y limit
        ax.set_xlim([0, None])
        # x/y axes
        ax.tick_params(which='major', labelsize=18)
        ax.set_xlabel('t (ps)', fontsize=20)
        if solid is None:
            ax.set_ylabel(fr'$u_{ind[ui][-1]}$'+r' ($\AA)', fontsize=20)
        else:
            ax.set_ylabel(fr'$P_{ind[ui][-1]}$'+r' ($\mu C/cm^2$)', fontsize=20)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        # legend
        ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def rg_plot_u_evo(self, u_mode=0, n=None, solid=None, dt=1, labels=None, ylim=None, if_sfc=False, figsize=(6,4), fig_out=None):
        """ Plotting reduced averaged polarization evolution (for `assign_group`)

        Parameters
        ----------
        u_mode: int
            [0]: ux, 1: uy, 2: uz
        n: int
            [None], number of reduced groups
        solid: str
            [None], 'bto', 'bst'
        dt: float
            [1], timestep for snapshots (in ps)
        labels: list
            labels for groups
        ylim: tuple(2)
            limit of y-axis (in unit of `solid`)
        if_sfc: bool
            [False], True for legend(loc=0)
        figsize: tuple(2)
            size of figure
        fig_out: str
            output file name of figure

        Returns
        -------
        None
        """
        uf = prefactor(solid=solid)
        ind = {0:'ux', 1:'uy', 2:'uz'}
        if u_mode in (0,1,2):
            ui = u_mode
        # calculate u_evo
        rg_u_evo = self.rg_cal_u_evo(ui=ui, n=n)
        # plot u_evo
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        colors = 'rgbmyck'
        for rg in rg_u_evo:
            label = labels[rg] if labels is not None else rg
            u_mean = np.mean(rg_u_evo[rg], axis=0)*uf
            u_std = np.std(rg_u_evo[rg], axis=0)*uf
            t_ary = np.arange(len(u_mean))*dt
            ax.plot(t_ary, u_mean, '-s', color=colors[rg%7], label=label)
            ax.fill_between(t_ary, u_mean-u_std, u_mean+u_std, color=colors[rg%7], alpha=0.3)
        # y=0
        ax.axhline(y=0, ls='--', lw=1, color='k', zorder=0)
        # x/y limit
        xmax = np.max(t_ary) if if_sfc else None
        ax.set_xlim([0, xmax])
        ax.set_ylim(ylim)
        # x/y axes
        ax.tick_params(which='major', labelsize=18)
        ax.set_xlabel('t (ps)', fontsize=20)
        if solid is None:
            ax.set_ylabel(fr'$u_{ind[ui][-1]}$'+r' ($\AA)', fontsize=20)
        else:
            ax.set_ylabel(fr'$P_{ind[ui][-1]}$'+r' ($\mu C/cm^2$)', fontsize=20)
        # legend
        if if_sfc:
            ax.legend(frameon=False, prop={'size': 18})
        else:
            ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def rg_plot_ul_evo(self, ui=0, norm=0, n=None, layer=0, solid=None, dt=1, labels=None,
                       xlim=None, ylim=None, if_sfc=False, figsize=(6,4), fig_out=None):
        """ Plotting reduced layer-averaged (switched) polarization evolution (for `assign_group`)

        Parameters
        ----------
        ui: int
            [0]/1/2: ux/uy/uz, 10/11/12: ni+/ni_tot
        norm: int
            [0]/1/2: x/y/z as layer normal
        n: int
            [None], reduction per number of groups
        layer: int
            [0], layer index
        solid: str
            [None], 'bto', 'bst'
        dt: float
            [1], timestep for snapshots (in ps)
        labels: list
            [None], list of labels
        x/ylim: tuple/list
            [None], (x/ymin, x/ymax)
        if_sfc: bool
            [False], True if simple plotting format
        figsize: tuple(2)
            size of figure
        fig_out: str
            output file name of figure

        Returns
        -------
        None
        """
        ind = {0:'ux', 1:'uy', 2:'uz'}
        if ui in (0,1,2):
            uf = prefactor(solid=solid) # A or uC/cm^2
        elif ui in (10,11,12):
            uf = 100 # %
        else:
            return
        # calculate ul_evo
        rg_ul_evo = self.rg_cal_ul_evo(ui=ui, norm=norm, n=n)
        # plot ul_evo
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        colors = 'rgbmyck'
        for rg in rg_ul_evo:
            label = labels[rg] if labels is not None else rg
            u_mean = np.mean(rg_ul_evo[rg], axis=0)[:,layer]*uf
            u_std = np.std(rg_ul_evo[rg], axis=0)[:,layer]*uf
            t_ary = np.arange(len(u_mean))*dt
            # t(5%), t(95%)
            umin, umax = np.min(u_mean), np.max(u_mean)
            ulo = umin+(umax-umin)*0.05
            uhi = umin+(umax-umin)*0.95
            t0 = np.where(u_mean<ulo)[0][-1]*dt
            t1 = np.where(u_mean>=uhi)[0][0]*dt
            ax.fill_between(t_ary, u_mean-u_std, u_mean+u_std, color=colors[rg%7], alpha=0.3)
            ax.plot(t_ary, u_mean, '-s', color=colors[rg%7], label=label)
            ax.axvline(x=t0, ls=':', lw=1, color=colors[rg%7])
            ax.axvline(x=t1, ls=':', lw=1, color=colors[rg%7])
        # y-ref
        if ui in (0,1,2):
            ax.axhline(y=0, ls='--', lw=1, color='k', zorder=0)
        # x/y limit
        if if_sfc:
            ax.set_xlim([0, np.max(t_ary)])
        else:
            ax.set_xlim(xlim)
        ylim = (0, 100) if ui in (10,11,12) else ylim
        ax.set_ylim(ylim)
        # x/y axes
        ax.tick_params(which='major', labelsize=18)
        ax.set_xlabel('t (ps)', fontsize=20)
        if ui in (0,1,2):
            if solid is None:
                ax.set_ylabel(fr'$u_{ind[ui][-1]}$'+r' ($\AA)', fontsize=20)
            else:
                ax.set_ylabel(fr'$P_{ind[ui][-1]}$'+r' ($\mu C/cm^2$)', fontsize=20)
        elif ui in (10,11,12):
            ax.set_ylabel(r'$n_{layer}^{+}$ (%)', fontsize=20)
        # legend
        if if_sfc:
            ax.legend(frameon=False, prop={'size': 18})
        else:
            ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size': 18})
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_phase_catalog(self, mode='ratio', u_crit=0.04, tags=None, figsize=None, fig_out=None):
        """ Plotting phases catalog
        
        Parameters
        ----------
        mode: str
            ['ratio'], 'count'
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        tags: list
            tags for xticklabels
        
        Returns
        -------
        None
            display/output the figure of the phases catalog
        """
        res = self.catalog_phase(u_crit=u_crit)
        figsize = (len(res)*1.5,4) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)

        for i in res:
            labels, count = res[i]
            sort_zip = sorted(zip(labels, count), key=lambda x: list('ROTC').index(x[0][1]))
            labels = list(map(str, np.array(sort_zip)[:,0]))
            count = list(map(int, np.array(sort_zip)[:,1]))
            if mode=='ratio':
                ntot = sum(count)
                bottom = 0
                colors = ['r', 'g', 'b', 'm', 'c', 'k', 'y']
                c_dict = {}
                for lab, c in zip(labels, count):
                    if lab not in c_dict:
                        _c = colors.pop(0)
                        c_dict[lab] = _c
                        colors.append(_c)
                    ratio = 100*c/ntot
                    ax.bar(i, ratio, width=0.5, color=c_dict[lab], alpha=0.5, label=lab, bottom=bottom)
                    ax.text(i-0.1, ratio/2+bottom, lab, size=12, weight='normal')
                    bottom+=ratio
            elif mode=='count':
                c_dict = {'C': 'k', 'T': 'r', 'O': 'g', 'R': 'b'}
                CSUM = {}
                for lab in labels:
                    t = lab[1]
                    if t not in CSUM:
                        CSUM[t] = 0
                    CSUM[t]+=1
                w = 0.7/4
                s = 0 if len(CSUM)==1 else w/2
                for j,t in enumerate(CSUM):
                    ax.bar(i+j*w-s, CSUM[t], width=w, color=c_dict[t], alpha=0.5, label=t)

        # x/y limits
        ax.set_xlim([-1, len(res)])
        if mode=='ratio':
            ax.set_ylim([0, 100])
        elif mode=='count':
            ax.set_ylim([0, None])
            # legend
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], color=c_dict[t], alpha=0.5, lw=6) for t in list('CTOR')]
            ax.legend(custom_lines, list('CTOR'), loc='center left', bbox_to_anchor=(1, 0.5), 
                      frameon=False, prop={'size':12, 'weight': 'normal'})

        # x/y labels
        ax.set_ylabel(mode, fontsize=14, labelpad=None)
        plt.tick_params(which='major', labelsize=12, length=4, width=1)
        plt.tick_params(which='minor', labelsize=12, length=0)

        # ticks
        tags = np.arange(len(res)) if tags==None else tags
        ax.set_xticks(np.arange(len(res)),)
        ax.set_xticklabels(tags, rotation=0)
        if mode=='count':
            ax.set_yticks(np.arange(np.max(list(CSUM.values()))+1))

        # frame
        bwith = 1
        ax.spines['bottom'].set_linewidth(bwith)
        ax.spines['left'].set_linewidth(bwith)
        ax.spines['top'].set_linewidth(bwith)
        ax.spines['right'].set_linewidth(bwith)

        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def dec_plot_2d(f):
        """ Decorator for plot_2d

        Parameters
        ----------
        mode: str
            [None], 'defects'
        f_app: str
            [None], file name for appendix

        Returns
        -------
        None
        """
        def wrapper(self, mode=None, f_app=None, *args, **kwargs):
            norm = kwargs['norm']
            ind = kwargs['ind']
            color = None
            scale = None
            size = None
            if 'appendix' not in kwargs:
                pass
            elif kwargs['appendix']!=None:
                if 'color' in kwargs['appendix']:
                    color = kwargs['appendix']['color']
                if 'scale' in kwargs['appendix']:
                    scale = kwargs['appendix']['scale']
                if 'size' in kwargs['appendix']:
                    size = kwargs['appendix']['size']

            appendix = {}
            # modes
            if mode==None:
                return f(self, *args, **kwargs)
            elif mode=='defects':
                if f_app==None or not os.path.exists(f_app):
                    print(f'[Warning]: f_app = {f_app} not found!')
                    return

                # parse data
                raw = parse_data(f_app)
                x, y, z = [raw[_] for _ in 'xyz']
                ux, uy, uz = [raw[f'u{_}'] for _ in 'xyz']
                xyz = [x, y, z]
                uxyz = [ux, uy, uz]
                nm = xyz[norm]
                xyz.pop(norm)
                uxyz.pop(norm)

                # select data
                data = []
                _x, _y = xyz[0][nm==ind], xyz[1][nm==ind]
                _ux, _uy = uxyz[0][nm==ind], uxyz[1][nm==ind]
                for i,j, u,v in zip(_x,_y,_ux,_uy):
                    data.append( (i,j,u,v) )
                colors = color if color!=None else ['k' for _ in range(len(data))]
                scales = scale if scale!=None else [1 for _ in range(len(data))]
                sizes = size if size!=None else [0.01 for _ in range(len(data))]
                appendix['data'] = data
                appendix['color'] = colors
                appendix['scale'] = scales
                appendix['size'] = sizes
                kwargs['appendix'] = appendix
                return f(self, *args, **kwargs)
        return wrapper

    @dec_plot_2d
    def plot_2d(self, norm=2, ind=0, c_axis='uz', scale=None, width=None, spacing=1, appendix=None,
                xlim=None, ylim=None, inxy=[.7,.3], inlen=None, inlim=None, if_sfc=False, figsize=(10,10), fig_out=None):
        """ Ploting 2D profile of dipoles

        Parameters
        ----------
        norm: int
            plane normal (0:z, 1:y, [2]:x)
        ind: int
            layer index
        c_axis: str
            axis for color ['uz']
        scale: float or tuple/list
            larger scale, shorter arrow (default: None)
            tuple/list for (main, inset)
        width: float or tuple/list
            shaft width (default: None)
            tuple/list for (main, inset)
        spacing: float
            [1], spacing of x/y-tick-label
        appendix: dict
            [None], additional plotting
        xlim: list/tuple(2)
            [xlo, xhi]
        ylim: list/tuple(2)
            [ylo, yhi]
        inxy: list/tuple(2)
            [0.7, 0.3], (x, y) position of the inset
        inlen: float
            length of the longest side of the inset
        inlim: list/tuple(4)
            [xlo, xhi, ylo, yhi]
        if_sfc: bool
            [False], True if simple plotting format
        figsize: tuple(2)
            size of figure
        fig_out: str
            output figure name

        Returns
        -------
        None

        Examples
        --------
        >>> f=['T180_p.coord',]
        >>> DA = Dipo_Analysis(*f)
        >>> DA.load_data()
        >>> DA.plot_2d(norm=2, ind=48, c_axis='uy', scale=6, figsize=(18,18)) # plot 2D dipo arrangement
        """
        Dataset = self.Sliceset if self.Sliceset!=None else self.Dataset # check if selet_data()
        # define color
        f_color = lambda v: 'red' if v>0 else 'blue'

        brr = 1
        for i in Dataset:
            x=Dataset[i]['x']
            y=Dataset[i]['y']
            z=Dataset[i]['z']
            ux=Dataset[i]['ux']
            uy=Dataset[i]['uy']
            uz=Dataset[i]['uz']
            c=Dataset[i][c_axis]
            px = ux*brr
            py = uy*brr
            pz = uz*brr

            # slice data
            ijk_list = [self.i_list, self.j_list, self.k_list]
            ind_o = [np.min(_) if _ is not None else 0 for _ in ijk_list]
            ijk_list = [ary if ary is not None else np.arange(self.Size[i][_]) for _,ary in enumerate(ijk_list)]
            ijk_list.pop(norm)
            # select data
            xyz_min = [0 if j!=norm else ind-ind_o[j] for j,_ in enumerate(self.Size[i])]
            xyz_max = [_ if j!=norm else ind-ind_o[j]+1 for j,_ in enumerate(self.Size[i])]
            x_min, x_max = xyz_min[0], xyz_max[0]
            y_min, y_max = xyz_min[1], xyz_max[1]
            z_min, z_max = xyz_min[2], xyz_max[2]

            Ixyz = [x, y, z]
            Ixyz.pop(norm)
            Iij = [_I[z_min:z_max, y_min:y_max, x_min:x_max].ravel() for _I in Ixyz]
            Pxyz = [px, py, pz]
            Pxyz.pop(norm)
            Pij = [_P[z_min:z_max, y_min:y_max, x_min:x_max].ravel() for _P in Pxyz]
            C = [f_color(_c) for _c in c[z_min:z_max, y_min:y_max, x_min:x_max].ravel()]

            fig, ax = plt.subplots(figsize=figsize, dpi=100)
            def _check_type(v):
                if v is None:
                    return {'sc':(5.6,2), 'w':(0.0024,0.006)}
                elif isinstance(v, (int,float)):
                    return {'sc':(v,v), 'w':(v,v)}
                else:
                    return {'sc':(v[0],v[1]), 'w':(v[0],v[1])}

            scale0, scale1 = _check_type(scale)['sc']
            width0, width1 = _check_type(width)['w']

            def _plot_fun(ax, scale=scale0, width=width0, r_sc=1, r_w=1):
                # plot arrows
                ax.quiver(Iij[0]+0.5, Iij[1]+0.5, Pij[0], Pij[1], color=C, units='width',
                          width=width, scale=scale, pivot='mid')
                # appendix
                if appendix!=None:
                    _items = zip(appendix['data'],appendix['color'],appendix['scale'],appendix['size'])
                    for (_ii,_jj,_pi,_pj),_cc,_sc,_ss in _items:
                        if self.Sliceset!=None:
                            if _ii not in ijk_list[0]:
                                continue
                            if _jj not in ijk_list[1]:
                                continue
                        if _pi==_pj==0:
                            _ss = 9 if _ss==None else _ss*900
                            ax.plot(_ii+0.5, _jj+0.5, color=_cc, marker='s', ms=_ss)
                        else:
                            _sc = 1 if _sc==None else _sc
                            _ss = 0.01 if _ss==None else _ss
                            ax.quiver(_ii+0.5, _jj+0.5, _pi, _pj, color=_cc,
                                      scale=_sc*r_sc, width=_ss*r_w, pivot='mid')

            # main
            _plot_fun(ax)
            # zoom-in
            xyz_min = [x_min, y_min, z_min]
            xyz_max = [x_max, y_max, z_max]
            xyz_min.pop(norm)
            xyz_max.pop(norm)
            inxy = (.7,.3) if inxy==None else inxy
            inlen = 0.47 if inlen==None else inlen
            if inlim!=None:
                inxlo, inxhi, inylo, inyhi = inlim
                r_in = (inyhi-inylo)/(inxhi-inxlo)
                if inyhi-inylo >= inxhi-inxlo:
                    inleny = inlen
                    inlenx = inleny/r_in
                else:
                    inlenx = inlen
                    inleny = inlenx*r_in
                axins = ax.inset_axes([inxy[0], inxy[1], inlenx, inleny])
                _plot_fun(axins, scale=scale1, width=width1, r_sc=scale1/scale0, r_w=width1/width0)
                axins.set_xticklabels([])
                axins.set_yticklabels([])
                axins.set_xticks(np.arange(inxlo, inxhi, 1))
                axins.set_yticks(np.arange(inylo, inyhi, 1))
                axins.set_xlim(inxlo, inxhi)
                axins.set_ylim(inylo, inyhi)
                ax.indicate_inset_zoom(axins, edgecolor='k', alpha=0.5, ls='--', lw=1)

            # x/y labels
            xyzlab = ['X', 'Y', 'Z']
            xyzlab.pop(norm)
            ax.set_xlabel(f'{xyzlab[0]}',fontsize=20, labelpad=None)
            ax.set_ylabel(f'{xyzlab[1]}',fontsize=20, labelpad=None)
            if not if_sfc:
                plt.title('XYZ'[norm]+f' = {ind}',fontsize=20)
            plt.tick_params(which='major', labelsize=18, length=4, width=1)
            plt.tick_params(which='minor', labelsize=18, length=0)

            # ticks
            ax.set_xticklabels('')
            ax.set_xticks(np.arange(xyz_min[0], xyz_max[0], spacing)+0.5, minor=True)
            ax.set_xticklabels(np.arange(xyz_min[0], xyz_max[0], spacing), minor=True)
            ax.set_yticklabels('')
            ax.set_yticks(np.arange(xyz_min[1], xyz_max[1], spacing)+0.5, minor=True)
            ax.set_yticklabels(np.arange(xyz_min[1], xyz_max[1], spacing), minor=True)
            for tick in ax.get_xaxis().get_minor_ticks():
                tick.set_pad(8.)
            for tick in ax.get_yaxis().get_minor_ticks():
                tick.set_pad(8.)
            x_major_locator = MultipleLocator(1)
            ax.xaxis.set_major_locator(x_major_locator)
            y_major_locator = MultipleLocator(1)
            ax.yaxis.set_major_locator(y_major_locator)

            # x/y limits
            plt.axis('scaled')
            if xlim==None:
                ax.set_xlim(xyz_min[0]-0.5, xyz_max[0]+0.5)
            else:
                ax.set_xlim(xlim)
            if ylim==None:
                ax.set_ylim(xyz_min[1]-0.5, xyz_max[1]+0.5)
            else:
                ax.set_ylim(ylim)

            # frame
            bwith = 1
            ax.spines['bottom'].set_linewidth(bwith)
            ax.spines['left'].set_linewidth(bwith)
            ax.spines['top'].set_linewidth(bwith)
            ax.spines['right'].set_linewidth(bwith)

            # save figure
            if fig_out!=None:
                rem, ext = fig_out.rsplit('.', maxsplit=1)
                plt.savefig(f'{rem}_{i}.{ext}', bbox_inches='tight')
            else:
                plt.show()
            plt.close('all')



class DWPass_Collector():
    def __init__(self):
        self.Cls = None
        self.load_data_param = dict(is_dump=False, is_raw=False, iframe=':')
        self.select_data_param = dict(i_list=None, j_list=None, k_list=None, exclude=None, key=None)
        self.find_DW_surf_param = dict(norm=0, ui=0, kernel=None, key=None, display=False)
        self.assign_group_param = dict(nframe=None)
        self.g_cal_DW_pass_param = dict(ind=0, dt=1, labels=None, fig_out=False)
        self.SUM = None

    def load_class(self, *args):
        if self.Cls is None:
            self.Cls = []
        self.Cls.append(Dipo_Analysis(*args))

    def load_data(self, is_dump=False, is_raw=False, iframe=':'):
        self.load_data_param = dict(is_dump=is_dump, is_raw=is_raw, iframe=iframe)

    def select_data(self, i_list=None, j_list=None, k_list=None, exclude=None, key=None):
        self.select_data_param = dict(i_list=i_list, j_list=j_list, k_list=k_list, exclude=exclude, key=key)

    def find_DW_surf(self, norm=0, ui=0, kernel=None, key=None, display=False):
        self.find_DW_surf_param = dict(norm=norm, ui=ui, kernel=kernel, key=key, display=display)

    def assign_group(self, nframe=None):
        self.assign_group_param = dict(nframe=nframe)

    def g_cal_DW_pass(self, ind=0, dt=1, labels=None, fig_out=False):
        self.g_cal_DW_pass_param = dict(ind=ind, dt=dt, labels=labels, fig_out=fig_out)

    def collect_dw(self):
        SUM = []
        for Cls in self.Cls:
            if Cls.Dataset is None:
                Cls.load_data(**self.load_data_param)
            Cls.select_data(**self.select_data_param)
            DW = Cls.find_DW_surf(**self.find_DW_surf_param)
            Cls.assign_group(**self.assign_group_param)
            g_res = Cls.g_cal_DW_pass(DW, **self.g_cal_DW_pass_param)
            SUM.append(g_res)
        self.SUM = SUM
        return SUM

    def sg_plot_DW_pass(self, dt=1, labels=None, line_styles=None, colors=None, legend_title='',
                        show_all=False, if_sfc=False, fig_out=None):
        if self.SUM is None:
            print('[Error]: No data!')
            return
        SUM = self.SUM
        labels = np.arange(len(SUM)) if labels is None else labels
        lss = ['-']*len(SUM) if line_styles is None else line_styles
        colors = ['rgbmyck'[_%7] for _ in range(len(SUM))] if colors is None else colors
        ind = self.g_cal_DW_pass_param['ind']
        fig, ax = plt.subplots(dpi=100)
        for i,g_res in enumerate(SUM):
            psum = []
            for ig in g_res:
                res = g_res[ig]
                psum.append(res)
            t = np.arange(np.shape(psum)[-1])*dt
            pmean = np.mean(psum, axis=0)
            pstd = np.std(psum, axis=0)
            tp_min = t[np.where(pmean<1)][-1]
            tp_max = t[pmean>99][0] if len(t[pmean>99])>0 else t[-1]
            tpin = tp_max - tp_min
            ax.fill_between(t, pmean-pstd, pmean+pstd, color=colors[i], alpha=0.2)
            if show_all:
                for res in psum:
                    t = np.arange(len(res))*dt
                    ax.plot(t, res, '.'+lss[i], color=colors[i])
                    ax.text(t[-1]+1, res[-1], labels[ig])
            else:
                ax.plot(t, pmean, 's', ls=lss[i], color=colors[i], label=f'{labels[i]}'+f' [{tpin}ps]')

        # reference lines
        ax.axhline(y=0, ls='--', color='k', lw=.8)
        ax.axhline(y=100, ls='--', color='k', lw=.8)
        ax.set_xlim([0, t[-1]])
        ax.tick_params(which='major', labelsize=20)
        ax.set_xlabel('t (ps)', fontsize=20)
        ax.set_ylabel('passed DW area (%)', fontsize=20)
        if if_sfc:
            CLG = OrderedDict()
            for _i, lab in enumerate(labels):
                if lab in CLG:
                    continue
                CLG[lab] = (lss[_i], colors[_i])
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle=CLG[_][0], marker='s', color=CLG[_][1]) for _ in CLG]
            ax.legend(custom_lines, list(CLG), loc=0, frameon=False, labelspacing=0.1, handletextpad=0.2, prop={'size': 18})
        else:
            ax.set_title(f'index = {ind}', fontsize=20)
            leg = ax.legend(loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, 
                            labelspacing=0.1, handletextpad=0.2, prop={'size': 18})
            leg.set_title(legend_title, prop={'size': 18})
        # save figure
        if fig_out!=None:
            plt.savefig(f'{fig_out}', bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')



class Dipo_Analysis_Evo(Dipo_Analysis):
    def __init__(self, f_dipo=None, *args):
        super().__init__(f_dipo, *args)
        self.Times = None
        self.Sites = None
        self.SiteDataset = None

    def set_times(self, times=None):
        """ Setting time stamps for frames

        Parameters
        ----------
        times: list
            list of times

        Returns
        -------
        None
        """
        f_isnumbers = lambda x: np.all(list(map(lambda x: isinstance(x, numbers.Number), x)))
        if times==None or not f_isnumbers(times):
            times = [_ for _ in range(len(self.files))]
        self.Times = times

    def select_sites(self, sites=None):
        """ Selecting certain sites for tracking

        Parameters
        ----------
        sites: list
            list of site index (x,y,z)

        Returns
        -------
        None
        """
        # check size consistence
        tmp = []
        for i in self.Dataset:
            tmp.append(self.Size[i])
        if len(set(tmp))!=1:
            stat_tmp = {_: tmp.count(_) for _ in set(tmp)}
            print(f'[Warning]: Inconsistent sizes for snapshots! {stat_tmp}')

        # select sits
        coordinates = sites
        if sites==None:
            Lx, Ly, Lz = self.Size[0]
            coordinates = np.vstack( tuple([_.ravel() for _ in np.mgrid[0:Lz,0:Ly,0:Lx]]) ).T
            print(f'[Warning]: Using all sites may cause running out of memory!')
        SiteDataset = OrderedDict()
        for item in ['ux','uy','uz']:
            if item not in SiteDataset:
                SiteDataset[item] = {}
            k=0
            for x,y,z in coordinates:
                if (x,y,z) not in SiteDataset[item]:
                    SiteDataset[item][(x,y,z)] = []
                for i in self.Dataset:
                    SiteDataset[item][(x,y,z)].append(self.Dataset[i][item][z,y,x])
                k+=1
        self.Sites = coordinates
        self.SiteDataset = SiteDataset

    def add_snapshots(self, snapshots=None, index=None, times=None):
        """ Adding snapshots at specified times

        Parameters
        ----------
        snapshots: list
            list of files
        index: list
            list of 'new' index where snapshots should be inserted
        times: list
            list of times

        Returns
        -------
        None
        """
        Files = self.files.copy()
        Times = self.Times.copy()
        if snapshots==None:
            print({'Times': Times, 'Files': Files})
            return

        # add snapshots
        if index!=None:
            for i,sp in zip(index,snapshots):
                Files.insert(i, sp)
        else:
            Files += list(snapshots)

        # add times
        tmin, tmax = min(Times), max(Times)
        if index!=None:
            if times!=None:
                for i,t in zip(index,times):
                    if not isinstance(t, numbers.Number):
                        print('[Error]: Non-numeric values for time found!')
                        return
                    if (i<=0 and t>=tmin) or (i>=len(Times) and t<=tmax):
                        print(f'[Error]: Incompatible time added. {Times}')
                        return
                    Times.insert(i, t)
            else:
                for i in index:
                    if i<=0:
                        Times.insert(0, Times[0]-1.05)
                    elif i>=len(Times):
                        Times.append(Times[-1]+1.05)
                    else:
                        Times.insert(i, (Times[i-1]+Times[i])/2+0.05)
        else:
            if times!=None:
                for t in times:
                    if t<=tmax:
                        print(f'[Error]: Incompatible time added. {Times}')
                        return
                    Times.append(t)
            else:
                Times += [tmax+1.05*(_) for _ in range(len(Times),len(Files))]
        if len(set(Times))<len(Times):
            print(f'[Error]: Duplicate times found! {Times}')
            return
        self.files = Files
        self.Times = Times
        self.load_data()

    def cal_p_prob(self, ui=0, umin=-0.25, umax=0.25, bin_size=0.01, display=False):
        """ Calculating probability from polarization trajectories
        # Todo: either sum up more sites or use plot_(v)hist for better statistics

        Parameters
        ----------
        ui: int
            [0]:ux, 1:uy, 2:uz, 3:ux+uy+uz
        umin: float
            low-bound of polarization [-0.25]
        umax: float
            high-bound of polarization [0.25]
        bin_size: float
            bin size of polarization [0.01]
        display: bool
            display probability of polarization

        Returns
        -------
        Prob: dict
            dict contains each site's probability distribution
        """
        if self.SiteDataset is None: # check if selec_sites()
            print('[Error]: Run select_sites before plot_p_evo!')
            return
        # select u
        ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        if ui in [0, 1, 2]:
            uind = [ind[ui]]
        elif ui==3:
            uind = ['ux', 'uy', 'uz']

        # make sure that [-bin_size/2, bin_size/2] is always included if starting from a negative value
        _x, _y = np.array([umin, bin_size])*10**max([len(str(float(_)).split('.')[-1]) for _ in [umin, bin_size]])
        if _x%_y!=0:
            print('[Warning] 0 is not at the middle of the bin!'+f' [{_x}, {_y}]')
        bins = np.arange(umin - bin_size/2, umax + bin_size, bin_size)
        bins_w0 = np.where(np.abs(bins)<np.finfo(float).eps, 0, bins) # 0 if close to zero

        # load data
        Sites = self.Sites
        SiteDataset = self.SiteDataset
        nsites = len(Sites)
        Prob = {}
        for u in uind:
            for coord in SiteDataset[u]:
                if coord not in Prob:
                    Prob[coord] = {}
                u_ary = SiteDataset[u][coord]
                h, _ = np.histogram(u_ary, bins=bins_w0)
                Prob[coord][u] = h.tolist()
        if display:
            fig, ax = plt.subplots(len(Prob), 1, sharex=True, dpi=100)
            for i, coord in enumerate(Prob):
                if len(uind)==1:
                    ax[i].bar(bins_w0[:-1], Prob[coord][ind[ui]], bin_size, color='rgb'[ui], alpha=0.6, label=ind[ui])
                elif len(uind)==3:
                    ax[i].bar(bins_w0[:-1], Prob[coord]['ux'], bin_size, color='r', alpha=0.6, hatch='\\\\', label=ind[0])
                    ax[i].bar(bins_w0[:-1], Prob[coord]['uy'], bin_size, color='g', alpha=0.6, hatch='', label=ind[1])
                    ax[i].bar(bins_w0[:-1], Prob[coord]['uz'], bin_size, color='b', alpha=0.6, hatch='//', label=ind[2])
                # ticks
                ax[i].tick_params(which='major', labelsize=12)
                # frame
                ax[i].spines['top'].set_visible(False)
                ax[i].spines['right'].set_visible(False)
                bwith = 1
                ax[i].spines['bottom'].set_linewidth(bwith)
                ax[i].spines['left'].set_linewidth(bwith)
            # x/y labels
            if len(uind)==1:
                ax[-1].set_xlabel(f'{ind[ui]}',fontsize=14, labelpad=None)
            elif len(uind)==3:
                ax[-1].set_xlabel('u',fontsize=14, labelpad=None)
            ax[len(ax)//2].set_ylabel('counts', fontsize=14, labelpad=None)
            # legend
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], color=c, alpha=0.5, lw=6) for c in 'rgb']
            ax[len(ax)//2].legend(custom_lines, uind, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False, prop={'size':12, 'weight': 'normal'})
            plt.show()
        return Prob, bins_w0

    def cal_free_energy(self, temperature=0, ui=0, umin=-0.25, umax=0.25, bin_size=0.01, f_eps=0):
        """ Plotting free energy from probabilities
        # Todo: carefully think about the log(0) case

        Parameters
        ----------
        temperature: float
            temperature (K)
        ui: int
            [0]:ux, 1:uy, 2:uz, 3:ux+uy+uz
        umin: float
            low-bound of polarization [-0.25]
        umax: float
            high-bound of polarization [0.25]
        bin_size: float
            bin size of polarization [0.01]
        f_eps: int
            factor to be applied to eps when calculating log(eps*factor), [0] to bypass inf

        Returns
        -------
        F: dict
            dict contains each site's free energies
        """
        def f_free_eng(s):
            if s>0:
                return -1*kb*temperature*np.log(s) # eV
            elif s==0:
                return -1*kb*temperature*np.log(np.finfo(float).eps*f_eps) if f_eps>0 else 0
            else:
                print('[Error]: Negative probability found!')
                return
        Prob, bins_w0 = self.cal_p_prob(ui=ui, umin=umin, umax=umax, bin_size=bin_size)
        F = {}
        for coord in Prob:
            if coord not in F:
                F[coord] = {}
            for u in Prob[coord]:
                s_ary = Prob[coord][u]
                F[coord][u] = list(map(f_free_eng, s_ary))
        return F, bins_w0

    def plot_p_evo(self, ui=0, ylim=None, vsplit=False, figsize=(6,4), fig_out=None):
        """ Plotting evolution of polarization

        Parameters
        ----------
        ui: int
            [0]:ux, 1:uy, 2:uz, 3:ux+uy+uz
        ylim: tuple/list
            (ylo, yhi), y range of plotting
        vsplit: bool
            split plot vertically into subplots for each site
        figsize: tuple
            size of the figure
        fig_out: str
            Output filename of the figure
        """
        if self.SiteDataset is None: # check if selec_sites()
            print('[Error]: Run select_sites before plot_p_evo!')
            return
        # load data
        Sites = self.Sites
        SiteDataset = self.SiteDataset
        nsites = len(Sites)
        ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        colors = 'rbgcymk'
        if ui in [0, 1, 2]:
            uind = [ind[ui]]
            linestyles = [['--', ':', '-'][ui]]
            markers = [['', '', '.'][ui]]
            clabels = [[r'$\it{i:x}$', r'$\it{i:y}$', r'$\it{i:z}$'][ui]]
        elif ui==3:
            uind = ['ux', 'uy', 'uz']
            linestyles = ['--', ':', '-']
            markers = ['', '', '.']
            clabels = [r'$\it{i:x}$', r'$\it{i:y}$', r'$\it{i:z}$']

        # plot data
        if vsplit:
            figsize = (6,1.5*nsites) if figsize is None else figsize
            fig, ax = plt.subplots(nsites, 1, figsize=figsize, dpi=100, sharex=True, gridspec_kw={'hspace': 0.1})
        else:
            fig, ax = plt.subplots(1, 1, figsize=figsize, dpi=100)
            ax = [ax]*nsites

        for _u,item in enumerate(uind):
            for i,coord in enumerate(SiteDataset[item]):
                c = colors[i%7]
                ax[i].plot(self.Times, SiteDataset[item][coord], c+markers[_u]+linestyles[_u])

                # limits
                if ylim!=None:
                    ax[i].set_ylim(ylim)

                # y labels
                ax[i].set_ylabel(r'$u_i$ ($\AA$)', fontsize=14, labelpad=None)
                ax[i].tick_params(which='major', labelsize=12, length=4)
                ax[i].tick_params(which='minor', labelsize=12, length=0)

                # frame/ticks
                ax[i].spines['top'].set_visible(False)
                ax[i].spines['right'].set_visible(False)

        # x labels
        ax[-1].set_xlabel('Time index', fontsize=14, labelpad=None)

        # custom legend
        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_%7], lw=2) for _ in range(nsites)]
        leg=ax[len(ax)//3].legend(custom_lines, list(map(tuple,Sites)), loc='center left', prop={'size':12, 'weight': 'normal'}, bbox_to_anchor=(1,0.64))
        custom_lines_1 = [Line2D([0], [0], linestyle=lsty, marker=mk, color='k', lw=2) for lsty, mk in zip(linestyles, markers)]
        ax[len(ax)*2//3].legend(custom_lines_1, clabels, loc='center left', frameon=False,
                      prop={'size':12, 'weight': 'normal'}, bbox_to_anchor=(1,0.15))
        if not vsplit:
            plt.gca().add_artist(leg)

        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')
