import os
import pytest
# user-defined modules
from utilities_feram import parse_data, parse_hl
from feram_converter import read_dump

test_dir = 'tests/'
f_td = lambda x: os.path.join(test_dir, x)

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('example_sd.coord'), (1000, )),
                          (f_td('example_sd.dipoRavg'), (1000, )),
                          (f_td('example.modulation'), (1000, )),
                         ])
def test_parse_data_shape(inputs, outputs):
    ary = parse_data(filename=inputs)
    assert ary.shape == outputs

@pytest.mark.parametrize('inputs, fmt, outputs',
                         [(f_td('example_sd.coord'), None, 18),
                          (f_td('example_sd.dipoRavg'), None, 6),
                          (f_td('example.modulation'), None, 4),
                          (f_td('example_sd.coord'), 'coord', 18),
                          (f_td('example_sd.coord'), 'dipoRavg', 6),
                         ])
def test_parse_data_namefields(inputs, fmt, outputs):
    ary = parse_data(filename=inputs, fmt=fmt)
    assert len(ary.dtype) == outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('example.hl'), (10, )),
                          (f_td('example_5.hl'), (10, )),
                         ])
def test_parse_hl_shape(inputs, outputs):
    ary = parse_hl(fname=inputs)
    assert ary.shape == outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('example.hl'), 14),
                          (f_td('example_5.hl'), 35),
                         ])
def test_parse_hl_ncol(inputs, outputs):
    ary = parse_hl(fname=inputs)
    assert len(ary[0]) == outputs

@pytest.mark.parametrize('inputs, outputs',
                         [(f_td('dump_example_0'), 3),
                          (f_td('dump_example_1'), 6)
                         ])
def test_read_dump_len(inputs, outputs):
    data = read_dump(inputs)
    assert len(data)==outputs
