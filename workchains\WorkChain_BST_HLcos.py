import os
import glob, re
import numpy as np
import shutil
import subprocess as spb
from collections import OrderedDict
# user-defined modules
from utilities_feram import Modulation, write_feram, run_feram
from generate_defects import Defects
from create_domains import DomainWall
from analyze_feram import Dipo_Analysis
from feram_converter import feram2dump
from domain_rectangle import DomainWalls

# write feram input
input_feram = lambda verbose = 4, method = 'md', Q_Nose = 0.1, GPa =0, kelvin = 300, \
                     bulk_or_film = 'bulk', L0 = 12, L1 = 12, L2 = 12, \
                     dt = 0.002, n_thermalize = 40000, n_average = 20000, n_hl_freq = 5000, n_coord_freq = 60000, \
                     coord_directory = 'never', slice_directory = 'never', distribution_directory = 'never', \
                     external_E_field0 = 0.0, external_E_field1 = 0.0, external_E_field2 = 0.0, \
                     init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0, \
                     init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03: \
"""# Parameters for (Ba,Sr)TiO3 effective Hamiltonian
# from https://journals.jps.jp/doi/10.7566/JPSJ.85.114714
#--- Method, Temperature, and mass ---------------
verbose = {verbose}
method = '{method}'
Q_Nose = {Q_Nose}
GPa = {GPa}
kelvin = {kelvin}
mass_amu = 40.9285
# acoustic_mass_amu = 41.67

#--- System geometry -----------------------------
bulk_or_film = '{bulk_or_film}'
L = {L0} {L1} {L2}

#--- Elastic Constants ---------------------------
modulation_constant = -0.279
B11 = 129.0286059
B12 = 39.00720516
B44 = 45.26949109

#--- From perovskite-optcell2-p.gp ---------------
B1xx = -143.7185938 [eV/Angstrom^2]
B1yy = -1.375464746 [eV/Angstrom^2]
B4yz = -15.02208695 [eV/Angstrom^2]
P_k1 = -166.56247 [eV/Angstrom^6]
P_k2 = 157.2518592 [eV/Angstrom^6]
P_k3 = 515.9414896 [eV/Angstrom^6]
P_k4 = 390.6570497 [eV/Angstrom^8]
P_alpha = 50.68630712 [eV/Angstrom^4]
P_gamma = -72.18357441 [eV/Angstrom^4]

#--- Time step -----------------------------------
dt = {dt} [pico second]
n_thermalize = {n_thermalize}
n_average = {n_average}
n_hl_freq = {n_hl_freq}
n_coord_freq = {n_coord_freq}
coord_directory = '{coord_directory}'
slice_directory = '{slice_directory}'
distribution_directory = '{distribution_directory}'

#--- External electric field ---------------------
external_E_field = {external_E_field0} {external_E_field1} {external_E_field2}

#--- From eigenvalues2j --------------------------
P_kappa2 = 9.4250031 [eV/Angstrom^2]
j = -2.048250285  -1.472144446  0.6396521198  -0.5891190367  0.0 0.2576732039  0.0  [eV/Angstrom^2]
a0 = 3.9435 [Angstrom]
Z_star = 9.807238756
epsilon_inf = 6.663371926

#--- Initial dipole configrations ----------------
init_dipo_avg = {init_dipo_avg0} {init_dipo_avg1} {init_dipo_avg2} [Angstrom]
init_dipo_dev = {init_dipo_dev0} {init_dipo_dev1} {init_dipo_dev2} [Angstrom]
""".format(verbose=verbose, method=method, Q_Nose=Q_Nose, GPa=GPa, kelvin=kelvin, bulk_or_film=bulk_or_film, 
           L0=L0, L1=L1, L2=L2, 
           dt=dt, n_thermalize=n_thermalize, n_average=n_average, n_hl_freq=n_hl_freq, n_coord_freq=n_coord_freq, 
           coord_directory=coord_directory, slice_directory=slice_directory,
           distribution_directory=distribution_directory, external_E_field0=external_E_field0,
           external_E_field1=external_E_field1, external_E_field2=external_E_field2,
           init_dipo_avg0=init_dipo_avg0, init_dipo_avg1=init_dipo_avg1, init_dipo_avg2=init_dipo_avg2,
           init_dipo_dev0=init_dipo_dev0, init_dipo_dev1=init_dipo_dev1, init_dipo_dev2=init_dipo_dev2)

# workflow
def cdec_methods(decorator, pattern):
    def dec(cls):
        for attr in cls.__dict__:
            if callable(getattr(cls, attr)) and re.compile(pattern).match(attr):
               setattr(cls, attr, decorator(getattr(cls, attr)))
        return cls
    return dec

def dec_add_call(f):
    def wrapper(self, *args, **kwargs):
        self.call_index += 1
        return f(self, *args, **kwargs)
    return wrapper

@cdec_methods(dec_add_call, r'cf_run_.*')
class WorkChain_BST_HLcos():
    """ Protocols from https://www.sciencedirect.com/science/article/pii/S1359645424005743 """
    call_index = 0

    def __init__(self, PATH='./', FILE='bst', method='vs', xsize=96, ysize=96, zsize=96, bulk_or_film='bulk',
                 r_ba=0.7, seed=123456789, ncore=8, tag=0, worklog='worklog', show_call_index=False):
        self.PATH = PATH
        self.method = method
        self.FILE = FILE
        self.xsize = xsize
        self.ysize = ysize
        self.zsize = zsize
        self.bulk_or_film = bulk_or_film
        self.r_ba = r_ba
        self.seed = seed
        self.ncore = ncore
        self.tag = tag
        self.worklog = worklog
        self.epi_strain = [0, 0]
        self.external_E_field = [0, 0, 0]
        self.exe = 'feram'
        self.restart = None
        self.show_call_index = show_call_index

    # calculation functions
    def cf_set_method(self, method='vs'):
        self.method = method

    def cf_set_exe(self, exe='feram'):
        self.exe = exe

    def cf_reset_call_index(self):
        self.call_index = 0

    def cf_set_seed(self, seed=123456789):
        self.seed = seed

    def cf_set_tag(self, tag=0):
        self.tag = tag

    def cf_set_bulk_or_film(self, tag='bulk'):
        self.bulk_or_film = tag

    def cf_set_epi_strain(self, exx=0, eyy=0):
        self.epi_strain = [exx, eyy]

    def cf_set_external_E_field(self, efx=0, efy=0, efz=0):
        self.external_E_field = [efx, efy, efz]

    def cf_set_restart(self, f_coord, f_opt=None, *args):
        files = []
        if os.path.exists(f_coord):
            files.append(f_coord)
        else:
            print(f'[Error]: {f_coord} not found!')
            return
        if f_opt is not None:
            if os.path.exists(f_opt):
                files.append(f_opt)
            else:
                print(f'[Error]: {f_opt} not found!')
                return
        for f in args:
            if os.path.exists(f):
                files.append(f)
            else:
                print(f'[Error]: {f} not found!')
                return
        self.restart = files

    def cf_set_Ba_ratio(self, r_ba=0.7):
        self.r_ba = r_ba

    def cf_write_modulation(self, seed=0):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        r_ba = self.r_ba
        M = Modulation(Lbox=(xsize,ysize,zsize), r_ba=r_ba, seed=seed)
        M.set_rand()
        M.write_modulation(PATH=PATH, FILE=FILE)

    def cf_run_therm_Ecos(self, temp=250, dt=0.001, n_thermalize=40000, n_average=400000, n_hl_freq=200):
        PATH, FILE = self.PATH, self.FILE
        method = self.method
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        bulk_or_film = self.bulk_or_film
        ba_percent = self.r_ba*100
        exx, eyy = self.epi_strain
        efx, efy, efz = self.external_E_field
        seed = self.seed
        ncore = self.ncore
        exe = self.exe
        # pressure correction
        GPa = (50-ba_percent)*6.0/100
        # thermalization w/o localfield
        n_total = n_thermalize + n_average
        text = input_feram(verbose=2, method=method, Q_Nose=14.4, dt=dt,
                           GPa=GPa, bulk_or_film=bulk_or_film,
                           kelvin=temp,
                           L0=xsize, L1=ysize, L2=zsize,
                           n_thermalize=n_thermalize,
                           n_average=n_average,
                           n_hl_freq=n_hl_freq,
                           n_coord_freq=n_total,
                           coord_directory = './',
                           external_E_field0=efx, external_E_field1=efy, external_E_field2=efz,
                           init_dipo_avg0 = 0.0, init_dipo_avg1 = 0.0, init_dipo_avg2 = 0.0,
                           init_dipo_dev0 = 0.03, init_dipo_dev1 = 0.03, init_dipo_dev2 = 0.03)
        options = [('seed', '{} 987654321'.format(seed), 10),
                   ('n_E_wave_period', n_average, 37)]
        options += [('E_wave_type', "'triangular_cos'", 38)]
        if bulk_or_film!='bulk':
            options+= [('epi_strain', '{} {}'.format(exx, eyy), 15)]
        write_feram(PATH=PATH, FILE=FILE, text=text, add_options=options)
        run_feram(PATH=PATH, FILE=FILE, tag='therm_Ecos', header='', f_out='data.avg', cores=ncore, exe=exe)
    
        # backup
        os.system('mv {}.feram data.avg logfile'.format(FILE))
        os.system(f'mv {FILE}.defects {FILE}.modulation logfile')
        # tidy
        os.system('rm -f {}.*'.format(FILE))
        print('>>> stage: therm + E_wave finished!')

    def cf_generate_defects(self, percent=0, dd_strength=0.1, seed=0):
        PATH, FILE = self.PATH, self.FILE
        xsize, ysize, zsize = self.xsize, self.ysize, self.zsize
        # generate defects
        dd = dd_strength
        D = Defects(fname='{}.defects'.format(FILE), size=(xsize,ysize,zsize))
        rnd1 = D.random_df(percent=percent, defects=(0.0,0.0,dd), seed=seed, xlim=None, ylim=None, zlim=None)
        D.write(defects=[rnd1,]) 

# helper functions
def multi_run(cls, outline, exe='feram', epi_strain=None, n=10):
    for i in range(n):
        cls.cf_set_exe(exe=exe)
        cls.cf_set_seed(seed=123456789+i)
        cls.cf_set_tag(tag=i)
        cls.cf_set_bulk_or_film(tag='bulk')
        if epi_strain!=None:
            cls.cf_set_epi_strain(exx=epi_strain[0], eyy=epi_strain[1])
        for func, param in outline:
            print('# function ({}):'.format(cls.call_index), func.__name__)
            print('# parameters:', param)
            func(**param)

################
# main program
################
xsize = 48
ysize = 48
zsize = 48
WC = WorkChain_BST_HLcos(PATH='./', FILE='bst',
                         xsize=xsize, ysize=ysize, zsize=zsize,
                         ncore=8, show_call_index=True)

efm = 200 # kV/cm
dt = 0.001 # ps
n_thm = 40000
n_avg = 400000
n_hl = 200
for pt in [0, 0.3, 0.6, 0.7, 0.8, 0.9, 1]:
    for temp in [240, 260, 280]: # K
        for ax in (0,2): # x,z
            tag = '{}_{}'.format(temp, 'xyz'[ax])
            efield = [0, 0, 0]
            efield[ax] = 0.00001*(efm) # (kV/cm)
            efx, efy, efz = efield
            outline=[(WC.cf_set_Ba_ratio, dict(r_ba=0.7)), # Ba70%Sr30%
                     (WC.cf_write_modulation, dict(seed=0)),
                     (WC.cf_generate_defects, dict(percent=pt, dd_strength=0.1, seed=0)), # ud_z
                     (WC.cf_set_external_E_field, dict(efx=efx, efy=efy, efz=efz)),
                     (WC.cf_run_therm_Ecos, dict(temp=temp, dt=0.001, n_thermalize=40000, n_average=400000, n_hl_freq=200)),]
            multi_run(WC, outline, exe='feram', n=1)
            os.system(f'mv logfile  hl_{tag}')
    os.system(f'mkdir {pt}%')
    os.system(f'mv hl_* {pt}%')
os.system(f'mkdir efm{efm}; mv *% efm{efm};')
