import numpy as np
from collections import OrderedDict
import cc3d
import matplotlib.pyplot as plt
import seaborn as sns
import matplotlib as mpl
import matplotlib.mlab as mlab
import matplotlib.gridspec as gridspec
from matplotlib.pyplot import MultipleLocator
from matplotlib.ticker import Scalar<PERSON>ormatter
from matplotlib.colors import Normalize
from utilities_feram import prefactor, parse_avg, parse_data
from analyze_feram import Dipo_Analysis, HL_Analyzer

class Plot_Avg():
    """ Plotting Feram *.avg files 
    
    Parameters
    ----------
    f_avg: str
        str file(s)
    
    Attributes
    ----------
    Files: list
        list of filename(s) of avg file(s)
    """
    def __init__(self, f_avg=None, *args):
        self.Files = [f_avg]
        for arg in args:
            self.Files.append(arg)
        self.range = None
        self.clegend = None

    def set_range(self, xlim=None, ylim=None):
        """ Setting x/y-axis plot-range """
        self.range = {}
        if xlim is not None:
            self.range['xlim'] = xlim
        if ylim is not None:
            self.range['ylim'] = ylim
        if xlim is None and ylim is None:
            self.range = None

    def set_clegend(self, labels=None):
        """ Setting customized legend 

        Parameters
        ----------
        labels: list
            [None], use one label instead of using px/y/z for each file, e.g. ['heating', 'cooling']

        Returns
        -------
        None
        """
        self.clegend = labels
    
    def plot_PT_mavg(self, solid='bto', fig_out=None):
        """ Plotting magnitude and mean of polarization w.r.t. temperature 
        
        Parameters
        ----------
        fig_out: str
            [None], output filename
        
        Returns
        -------
        None
        """
        for favg in self.Files:
            data = parse_avg(favg)
            brr = prefactor(solid)

            fig,ax = plt.subplots(figsize=(10,8), dpi=400)
            plt.plot(data['T'], np.abs(data['ux']*brr), '-bo', label=r'$\widebar{p}_x$', linewidth=2,markersize=12)
            plt.plot(data['T'], np.abs(data['uy']*brr), ':b^', label=r'$\widebar{p}_y$', linewidth=2,markersize=12)
            plt.plot(data['T'], np.abs(data['uz']*brr), '--b*', label=r'$\widebar{p}_z$', linewidth=2,markersize=12)
            plt.plot(data['T'], data['amx']*brr, '-ro', label=r'$|p_x|$', linewidth=2,markersize=12)
            plt.plot(data['T'], data['amy']*brr, ':r^', label=r'$|p_y|$', linewidth=2,markersize=12)
            plt.plot(data['T'], data['amz']*brr, '--r*', label=r'$|p_z|$', linewidth=2,markersize=12)

            # the scale of y axis(same in x axis)
            y_major_locator=MultipleLocator(10)  
            ax.yaxis.set_major_locator(y_major_locator)
            x_major_locator=MultipleLocator(50)  
            ax.xaxis.set_major_locator(x_major_locator)

            # setting x, y , legend,My,Mfor j in My: ticks parameters
            ax.set_ylabel("P ($\mu C/cm^{2}$)",fontsize=24, labelpad=12)
            ax.set_xlabel('T(K)',fontsize=24, labelpad=15)
            plt.tick_params(labelsize=24,length=12,width=3)
            plt.ylim(-1,36)
            plt.xlim(140,410)
            plt.legend(fontsize=24 , loc = 'best') #bbox_to_anchor=(0.50, 0.49))

            bwith = 3 
            #ax = plt.gca()
            ax.spines['bottom'].set_linewidth(bwith)
            ax.spines['left'].set_linewidth(bwith)
            ax.spines['top'].set_linewidth(bwith)
            ax.spines['right'].set_linewidth(bwith)
            #ax.grid(which ='major')
            #ax.grid(which ='minor')

            plt.show()
            if fig_out!=None:
                fig.savefig(f'{i:02d}_'+fig_out, dpi=fig.dpi, bbox_inches='tight')

    def plot_PT_avg(self, solid='bto', is_magn=True, overlap=False, show_Tc=False, fig_out=None):
        """ Plotting polarizations w.r.t. temperature 
        
        Parameters
        ----------
        solid: str
            ['bto'], 'bst'
        is_magn: bool
            [True] if plotting average of <|P|>; |<P>| otherwise
        overlap: bool
            [False], True if overlapping figures
        show_Tc: bool
            [False], True if showing Tc and overriding legend with Tc
        fig_out: str
            [None], output filename
        
        Returns
        -------
        SUM: dict
            dict of Tc if `show_Tc=True`
        """
        colors = 'rbgcymk'
        fig,ax = plt.subplots(figsize=(10,6), dpi=100)
        SUM = OrderedDict()
        for i, favg in enumerate(self.Files):
            data = parse_avg(favg)
            brr = prefactor(solid)

            color = colors[i%7]
            i_hide = '_' if show_Tc else ''
            if is_magn:
                Px, Py, Pz = [data[_]*brr for _ in ('amx', 'amy', 'amz')]
            else:
                Px, Py, Pz = [np.abs(data[_]*brr) for _ in ('ux', 'uy', 'uz')]
            ax.plot(data['T'], Px, color+'-o', label=i_hide+r'$p_x$', linewidth=2, markersize=8)
            ax.plot(data['T'], Py, color+'-^', label=i_hide+r'$p_y$', linewidth=2, markersize=8)
            ax.plot(data['T'], Pz, color+'-*', label=i_hide+r'$p_z$', linewidth=2, markersize=8)
            # Tc
            if show_Tc:
                uu = np.sort(np.abs([Px, Py, Pz]), axis=0)
                dx = data['T'][1]-data['T'][0]
                T0 = data['T'][np.argmin(np.gradient(uu[0],dx))]
                T1 = data['T'][np.argmin(np.gradient(uu[1],dx))]
                T2 = data['T'][np.argmin(np.gradient(uu[2],dx))]
                ax.axvline(x=T0, color=color, ls='-', label=r'$T_C^{RO}$'+f'={T0}K')
                ax.axvline(x=T1, color=color, ls='--', label=r'$T_C^{OT}$'+f'={T1}K')
                ax.axvline(x=T2, color=color, ls=':', lw=4, label=r'$T_C^{TC}$'+f'={T2}K')
                SUM[i] = [T0, T1, T2]

            y_major_locator=MultipleLocator(10)  
            ax.yaxis.set_major_locator(y_major_locator)
            x_major_locator=MultipleLocator(50)  
            ax.xaxis.set_major_locator(x_major_locator)
            plt.tick_params(labelsize=20, length=10, width=2)
            if self.range is not None:
                if 'xlim' in self.range:
                    ax.set_xlim(self.range['xlim'])
                if 'ylim' in self.range:
                    ax.set_ylim(self.range['ylim'])
            if self.clegend is None:
                ax.legend(fontsize=20, loc='lower left', frameon=False)

            ax.set_xlabel('Temperature(K)', fontsize=24, fontweight='normal', labelpad=15)
            ylabel = r'$\langle |P|\rangle$ ($\mu C/cm^{2}$)' if is_magn else r'$|\langle P\rangle|$ ($\mu C/cm^{2}$)'
            ax.set_ylabel(ylabel, fontsize=24, fontweight='normal', labelpad=12)
            
            plt.tight_layout()
            if not overlap:
                plt.show()
                if fig_out!=None:
                    fig.savefig(f'{i:02d}_'+fig_out, dpi=fig.dpi)

        # custom legend
        if self.clegend is not None:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_], lw=2) for _ in range(len(self.Files))]
            leg = ax.legend(custom_lines, self.clegend, loc=0, frameon=False, prop={'size':20, 'weight': 'normal'})

        if overlap and fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')
        return SUM

    def plot_StrT_avg(self, solid=None, overlap=False, fig_out=None):
        """ Plotting strains w.r.t. temperature 
        
        Parameters
        ----------
        solid: str
            [None], 'bto', 'bst'
        fig_out: str
            [None], output filename
        
        Returns
        -------
        None
        """
        colors = 'rbgcymk'
        fig,ax = plt.subplots(figsize=(10,6), dpi=100)
        for i,favg in enumerate(self.Files):
            data = parse_avg(favg)
            a0 = prefactor(solid=solid, mode='a')
            color = colors[i%7]
            if solid!=None:
                ax.plot(data['T'], (1+data['exx'])*a0, color+'-o', label=r'$a$', linewidth=2, markersize=8)
                ax.plot(data['T'], (1+data['eyy'])*a0, color+'-^', label=r'$b$', linewidth=2, markersize=8)
                ax.plot(data['T'], (1+data['ezz'])*a0, color+'-*', label=r'$c$', linewidth=2, markersize=8)
            else:
                ax.plot(data['T'], data['exx'], color+'-o', label=r'${e}_{xx}$', linewidth=2, markersize=8)
                ax.plot(data['T'], data['eyy'], color+'-^', label=r'${e}_{yy}$', linewidth=2, markersize=8)
                ax.plot(data['T'], data['ezz'], color+'-*', label=r'${e}_{zz}$', linewidth=2, markersize=8)

            y_intv = 0.01 if solid!=None else 0.004
            y_major_locator=MultipleLocator(y_intv)  
            ax.yaxis.set_major_locator(y_major_locator)
            x_major_locator=MultipleLocator(50)  
            ax.xaxis.set_major_locator(x_major_locator)
            plt.tick_params(labelsize=18, length=10, width=2)
            if self.range is not None:
                if 'xlim' in self.range:
                    ax.set_xlim(self.range['xlim'])
                if 'ylim' in self.range:
                    ax.set_ylim(self.range['ylim'])
            if self.clegend is None:
                ax.legend(fontsize=14 , loc='lower left')

            ax.set_xlabel('Temperature(K)', fontsize=24, fontweight='bold', labelpad=15)
            ylabel = 'Lattice const. ($\AA$)' if solid!=None else 'Strain'
            ax.set_ylabel(ylabel, fontsize=24, fontweight='bold', labelpad=12)

            plt.tight_layout()
            if not overlap:
                plt.show()
                if fig_out!=None:
                    fig.savefig(f'{i:02d}_'+fig_out, dpi=fig.dpi)

        # custom legend
        if self.clegend is not None:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_], lw=2) for _ in range(len(self.Files))]
            leg = ax.legend(custom_lines, self.clegend, loc='lower left', prop={'size':14, 'weight': 'normal'})

        if overlap and fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_Ediff_avg(self, i0=0, xlim=None, fig_out=None):
        """ Plotting total energy differences w.r.t. temperature 
        
        Parameters
        ----------
        i0: int
            the index of the reference dataset
        xlim: tuple/list
            spcify the range of x-data
        fig_out: str
            [None], output filename
        
        Returns
        -------
        None
        """
        colors = 'rbgcymk'
        fig,ax = plt.subplots(figsize=(10,6), dpi=100)
        # reference data
        data_ref = parse_avg(self.Files[i0])
        T_ref = data_ref['T']
        if xlim is None:
            xlim = [np.min(T_ref), np.max(T_ref)]
        ind = np.where((T_ref>=xlim[0]) & (T_ref<=xlim[1]))[0]
        ind_s = np.argsort(T_ref[ind])
        E_ref = data_ref['etot'][ind][ind_s]
        for i, favg in enumerate(self.Files):
            data = parse_avg(favg)
            T = data['T']
            ind = np.where((T>=xlim[0]) & (T<=xlim[1]))[0]
            ind_s = np.argsort(T[ind])
            Ediff = data['etot'][ind][ind_s] - E_ref # eV/u.c.
            if i==i0:
                ax.axhline(y=0, color=colors[i], lw=2, label=f'E[{i}]-E[{i}]')
            else:
                ax.plot(T[ind][ind_s], Ediff*1000, colors[i]+'-s', label=r'E[{}]-E[{}]'.format(i,i0), linewidth=2, markersize=8)

        x_major_locator=MultipleLocator(50)  
        ax.xaxis.set_major_locator(x_major_locator)
        plt.tick_params(labelsize=20, length=10, width=2)
        if self.range is not None:
            if 'xlim' in self.range:
                ax.set_xlim(self.range['xlim'])
            if 'ylim' in self.range:
                ax.set_ylim(self.range['ylim'])
        if self.clegend is None:
            ax.legend(fontsize=18, loc='lower left', frameon=False)

        ax.set_xlabel('Temperature(K)', fontsize=24, fontweight='normal', labelpad=15)
        ax.set_ylabel(r'$\Delta E$ (meV/u.c.)', fontsize=24, fontweight='normal', labelpad=12)
        plt.tight_layout()

        # custom legend
        if self.clegend is not None:
            from matplotlib.lines import Line2D
            custom_lines = [Line2D([0], [0], linestyle='-', color=colors[_], lw=2) for _ in range(len(self.Files))]
            leg = ax.legend(custom_lines, self.clegend, loc=0, frameon=False, prop={'size':18, 'weight': 'normal'})

        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_hmap(self, solid=None, is_abs=False, xind=None, xlim=None, ylim=[100,400], y_interval=10,
                  clim=None, cbar=True, xlabel='x_index', ylabel='Temp. (K)', plabel=None, figsize=None, fig_out=None):
        """ Plotting heatmap using Px+Py+Pz 
        
        Parameters
        ----------
        solid: str
            [None], 'bto', 'bst'
        is_abs: bool
            [False], True if using <|u|> (for customized output)
        xind: list/tuple
            index for x-axis
        xlim: list/tuple
            limit of x-range
        ylim: list/tuple
            limit of y-range, this also applys for selecting data
        y_interval: float
            interval for contour level
        clim: list/tuple
            limit for c-range, i.e. Px+Py+Pz
        cbar: bool
            [True], False if not showing color bar
        x/y/plabel: str
            x-/y-axis label or colorbar label
        figsize: list/tuple
            size of figure
        fig_out: str
            [None], output filename
        
        Returns
        -------
        None
        """
        xind = list(np.arange(len(self.Files))) if xind is None else xind
        Dataset = {'xind': [], 'temp': [], 'ux': [], 'uy': [], 'uz': []}
        for i, favg in enumerate(self.Files):
            data = parse_avg(favg)
            brr = prefactor(solid)
            # unique temp-index
            T = data['T']
            _, uT = np.unique(T, return_index=True)
            # ylim for temp-range
            _T = T[uT]
            iT = np.where((_T>=ylim[0]) & (_T<=ylim[1]))[0]
            Dataset['temp'].append(T[uT][iT])
            Dataset['xind'].append(i)
            if not is_abs:
                Dataset['ux'].append(data['ux'][uT][iT]*brr)
                Dataset['uy'].append(data['uy'][uT][iT]*brr)
                Dataset['uz'].append(data['uz'][uT][iT]*brr)
            else:
                Dataset['ux'].append(data['amx'][uT][iT]*brr)
                Dataset['uy'].append(data['amy'][uT][iT]*brr)
                Dataset['uz'].append(data['amz'][uT][iT]*brr)
        # abs(p)
        Dataset = {key: np.abs(val) for key, val in Dataset.items()}
        Plotset = {key: val.T for key, val in Dataset.items()}

        # plot heatmap
        xind_d = Dataset['xind']
        xind_p = Plotset['xind']
        temp = Dataset['temp'][0]
        cdata = Plotset['ux']+Plotset['uy']+Plotset['uz']
        figsize = (6,6) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)

        # colormap
        norm = Normalize()
        norm.autoscale(cdata)
        colormap = mpl.cm.jet_r
        xm, ym = np.meshgrid(xind_p, temp)
        im0 = ax.contour(xm, ym, cdata, alpha=0) # for calculating levels
        levels = int(max(im0.levels)//y_interval)+1
        im1 = ax.contourf(xm, ym, cdata, cmap=colormap, norm=norm, levels=levels)

        if clim!=None:
            im1.set_clim(vmin=clim[0], vmax=clim[1])
        # ticks
        xlo, xhi = float(xind_p[0]), float(xind_p[-1])
        inp = np.linspace(xlo, xhi, len(xind_d))
        ax.set_xticks(inp)
        ax.set_xticklabels(xind)
        ax.tick_params(which='major', labelsize=18, length=4, width=1)
        ax.set_xlim(xlim)
        ax.set_ylim(ylim)

        # labels
        ax.set_xlabel(xlabel, fontsize=20)
        ax.set_ylabel(ylabel, fontsize=20)
        
        # colorbar
        if plabel==None:
            if is_abs:
                plabel=r'$\langle |P_x|\rangle+\langle |P_y|\rangle+\langle |P_z|\rangle$'
            else:
                plabel=r'$\langle P_x\rangle+\langle P_y\rangle+\langle P_z\rangle$'
            plabel+=r' $(\mu C/cm^{2})$'
        if cbar:
            clb = fig.colorbar(im1, ax=ax)
            clb.ax.tick_params(length=4, width=1, labelsize=18)
            clb.ax.invert_yaxis()
            clb.ax.get_yaxis().labelpad = 28
            clb.ax.set_ylabel(plabel, rotation=270, fontsize=20)

        plt.tight_layout()
        if fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')

    def plot_cgrid(self, solid=None, is_abs=False, add_Tc=None, xind=None, xlim=None, ylim=[100,400], 
                   clim=None, cbar=True, xlabel='x_index', ylabel='Temp. (K)', plabel=None, figsize=None, fig_out=None):
        """ Plotting heatmap using Px+Py+Pz 
        
        Parameters
        ----------
        solid: str
            [None], 'bto', 'bst'
        is_abs: bool
            [False], True if using <|u|> (for customized output)
        add_Tc: list
            [(x_index, y_Tcs), ...]
        xind: list/tuple
            index for x-axis
        xlim: list/tuple
            limit of x-range
        ylim: list/tuple
            limit of y-range, this also applys for selecting data
        clim: list/tuple
            limit for c-range, i.e. Px+Py+Pz
        cbar: bool
            [True], False if not showing color bar
        x/y/plabel: str
            x-/y-axis label or colorbar label
        figsize: list/tuple
            size of figure
        fig_out: str
            [None], output filename
        
        Returns
        -------
        None
        """
        xind = list(np.arange(len(self.Files))) if xind is None else xind
        figsize = (6.5,6) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        for i, favg in enumerate(self.Files):
            data = parse_avg(favg)
            brr = prefactor(solid)
            T = data['T']
            if not is_abs:
                px = data['ux']*brr
                py = data['uy']*brr
                pz = data['uz']*brr
            else:
                px = data['amx']*brr
                py = data['amy']*brr
                pz = data['amz']*brr
            c = np.abs(px)+np.abs(py)+np.abs(pz)
            # clim
            clim = [np.min(c), np.max(c)] if clim is None else clim
            # colormap
            norm = Normalize(vmin=clim[0], vmax=clim[1])
            colormap = mpl.cm.jet_r
            sc = ax.scatter([i]*len(T), T, c=c, cmap=colormap, norm=norm, marker='s', s=100)
        # Tc
        if add_Tc is not None:
            for (_x, _y) in add_Tc:
                ax.plot(_x, _y, 's-k')

        # ticks
        xlo, xhi = 0, len(self.Files)
        inp = np.linspace(xlo, xhi, len(self.Files)+1)[:-1]
        ax.set_xticks(inp)
        ax.set_xticklabels(xind)
        ax.tick_params(which='major', labelsize=18, length=4, width=1)
        ax.set_xlim(xlim)
        ax.set_ylim(ylim)

        # labels
        ax.set_xlabel(xlabel, fontsize=20)
        ax.set_ylabel(ylabel, fontsize=20)
        
        # colorbar
        if plabel==None:
            if is_abs:
                plabel=r'$\langle |P_x|\rangle+\langle |P_y|\rangle+\langle |P_z|\rangle$'
            else:
                plabel=r'$\langle P_x\rangle+\langle P_y\rangle+\langle P_z\rangle$'
            plabel+=r' $(\mu C/cm^{2})$'
        if cbar:
            clb = fig.colorbar(sc, ax=ax)
            clb.ax.tick_params(length=4, width=1, labelsize=18)
            clb.ax.invert_yaxis()
            clb.ax.get_yaxis().labelpad = 28
            clb.ax.set_ylabel(plabel, rotation=270, fontsize=20)

        plt.tight_layout()
        if fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')

class Dipo_Analysis_STI(Dipo_Analysis):
    def __init__(self, f_dipo=None, *args):
        super().__init__(f_dipo, *args)
        self.Phase = None
        self.DW = None
        self.LSTI = None

    def load_dataset(self, key=0, add_set=None):
        """ Loading dataset for `subplot_phase_catalog`

        Parameters
        ----------
        key: hashable
            key of dataset

        Returns
        -------
        None
        """
        LSTI = {_: OrderedDict() for _ in ['main', 'add']}
        if key not in LSTI['main']:
            _STI_main = Dipo_Analysis_STI(*self.files)
            _STI_main.load_data()
            LSTI['main'][key] = _STI_main
        if add_set is not None:
            _STI_add = Dipo_Analysis_STI(*add_set)
            _STI_add.load_data()
            LSTI['add'][key] = _STI_add
        self.LSTI = LSTI

    def add_subdataset(self, main_set=None, key=1, add_set=None):
        """ Adding sub-dataset for `subplot_phase_catalog`
        
        Parameters
        ----------
        main_set: list
            list of files for main subdataset
        key: hashable
            key of subdataset
        add_set: list
            list of files for additional subdataset

        Returns
        -------
        None
        """
        LSTI = self.LSTI
        if LSTI is None:
            print("[Error]: Please use `load_dataset` before using `add_subdataset`.")
            return
        else:
            LSTI_main = LSTI['main']
            LSTI_add = LSTI['add']
        if key in LSTI_main:
            print(f"[Warning]: key={key} already exists! Replacing the data!")
        _STI_main = Dipo_Analysis_STI(*main_set)
        _STI_main.load_data()
        LSTI_main[key] = _STI_main
        if add_set is not None:
            _STI_add = Dipo_Analysis_STI(*add_set)
            _STI_add.load_data()
            LSTI_add[key] = _STI_add
        else:
            self.LSTI['add'] = None # reset

    def plot_dipo_hist(self, solid='bto', xlim=None, u_crit=None, fig_out=None):
        """ Plotting histogram of dipoles

        Parameters
        ----------
        solid: str
            ['bto'], 'bst', None
        xlim: list/tuple
            x-axis range, e.g. [-0.2, 0.2]
        u_crit: float
            if not [None], then showing criterion of u (refer to `assign_phase`)

        Returns
        -------
        None
        """
        for i,_f in enumerate(self.files):
            ux=self.Dataset[i]['ux'].ravel()
            uy=self.Dataset[i]['uy'].ravel()
            uz=self.Dataset[i]['uz'].ravel()
            brr = prefactor(solid)
            px = ux*brr
            py = uy*brr
            pz = uz*brr

            fig,ax=plt.subplots(figsize=(6,4), dpi=300)

            sns.histplot(px, ax=ax, color='b', label =r'$\widebar{p}_x$')
            sns.histplot(py, ax=ax, color='r',label =r'$\widebar{p}_y$')
            sns.histplot(pz, ax=ax, color='k',label =r'$\widebar{p}_z$')
            # u_crit
            if u_crit is not None and u_crit!=0:
                ax.axvline(x=u_crit*brr, color='k', linestyle='--', lw=1)
                ax.axvline(x=-u_crit*brr, color='k', linestyle='--', lw=1)

            # xlim
            ulim = xlim if xlim is not None else [-0.2*brr, 0.2*brr]
            ax.set_xlim(ulim)

            bwith = 2
            ax.spines['bottom'].set_linewidth(bwith)
            ax.spines['left'].set_linewidth(bwith)
            ax.spines['top'].set_linewidth(bwith)
            ax.spines['right'].set_linewidth(bwith)

            plt.legend(fontsize=16, loc='best')
            plt.tick_params(labelsize=16,length=8,width=2)
            ax.set_ylabel("Frequency",fontsize=16, labelpad=12)
            ax.set_xlabel("P ($\mu C/cm^{2}$)",fontsize=16, labelpad=12)

            # save figure
            if fig_out!=None:
                rem, ext = fig_out.rsplit('.', maxsplit=1)
                plt.savefig(f'{rem}_{i}.{ext}', dpi=fig.dpi, bbox_inches='tight')
            plt.show()

    def assign_phase(self, u_crit=0.015, divide=False):
        """ Assigning phase for each dipole based on a criterion of u
        
        Parameters
        ----------
        u_crit: float
            criterion for the strength of u, above means order, below means disorder
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        
        Returns
        -------
        Phase: dict
            dict of np.array, where 0: cubic, 1: tetragonal, 2: orthorhombic, 3: rhombohedral
        """
        f_phlab = lambda u,u_crit: (abs(u)>=u_crit)*(u>0)*1 + (abs(u)>=u_crit)*(u<0)*(-1)
        f_phvar = lambda vx,vy,vz: (vx+1)*3**2 + (vy+1)*3 + (vz+1)
        def map_phase(phvar):
            # C:0, T:100-105, O:200-211, R:300-307
            phase_ind = [13]+[-1]*99+\
                        [4,10,12,14,16,22]+[-1]*94+\
                        [1,3,5,7,9,11,15,17,19,21,23,25]+[-1]*88+\
                        [0,2,6,8,18,20,24,26]
            return phase_ind.index(phvar)
        # assign phases
        Phase = OrderedDict()
        for i,_f in enumerate(self.files):
            x=self.Dataset[i]['x']
            y=self.Dataset[i]['y']
            z=self.Dataset[i]['z']
            ux=self.Dataset[i]['ux']
            uy=self.Dataset[i]['uy']
            uz=self.Dataset[i]['uz']
            # phase
            if not divide:
                phase = np.count_nonzero(np.abs([ux, uy, uz])>=u_crit, axis=0)
            else:
                vx, vy, vz = [f_phlab(_,u_crit) for _ in (ux, uy, uz)]
                phvar = np.array(f_phvar(vx, vy, vz)) # converted masked array to avoid overflow error
                phase = np.vectorize(map_phase)(phvar)
            Phase[i] = phase
        self.Phase = Phase
        return Phase

    @staticmethod
    def _stat_domain(Phase, divide=False, size_th=1, connectivity=6, pbc=True, f_mod=None):
        """ Statistics of domains and walls (single data)
        
        Parameters
        ----------
        Phase: dict
            output dict from `assign_phase`
        divide: bool
            [False], True if using divided identical phases for different orientations
        size_th: int
            [1], threshold size of domain, bypass size < size_th
        connectivity: int
            for 3D: [6], 18, 26; for 2D: 4, 8
        pbc: bool
            [True] if periodic boundary condition
        f_mod: str
            read modulation file to exclude STO inclusion (-8) while counting domains
        
        Returns
        -------
        Domain: dict
            statistics of domains
        Wall: dict
            statistics of walls
        """
        phase = Phase+1 # C,T,O,R = (0,1,2,3) --> (1,2,3,4)
        i_chk = 0
        if divide:
            if np.all(np.unique(phase)<100):
                print('[Warning]: Please make sure Phase is generated by `assign_phase(divide=True)`.')
                i_chk = 1
        else:
            if np.any(np.unique(phase)>=100):
                print('[Error]: Unexpected Phase detected, remember to use `assign_phase(divide=False)`.')
                return
        if f_mod is not None:
            mod = parse_data(f_mod, fmt='modulation')
            sto = mod[mod['mod']==-8]
            x_sto, y_sto, z_sto = sto['x'], sto['y'], sto['z']
            for _x,_y,_z in zip(x_sto, y_sto, z_sto):
                phase[_z,_y,_x] = 0
        # domains
        labels_out, n_domains = cc3d.connected_components(phase, connectivity=connectivity, 
                                                          return_N=True, periodic_boundary=pbc)
        Domain = {}
        dw_dict = {}
        for i in np.unique(labels_out):
            condi = (labels_out==i)
            size = np.sum(condi)
            phvar = np.unique(phase*condi)[-1]
            dw_dict[i] = (phvar-1)//100 if i_chk else phvar-1
            if size<size_th: # bypass size below threshold
                continue
            if phvar==0:
                Domain['S'+f'{phvar-1}_{i}'] = size
            else:
                ph = (phvar-1)//100 if divide else phvar-1
                Domain['CTOR'[ph]+f'{phvar-1}_{i}'] = size
        # walls
        f_kph = lambda k: 'CTOR'[k//100] if divide else 'CTOR'[k]
        edges_out = cc3d.contacts(labels_out, connectivity=connectivity, surface_area=True)
        Wall = {}
        for key in edges_out:
            key_new = tuple(sorted([f'{f_kph(dw_dict[k])}{dw_dict[k]}_{k}' for k in key]))
            if key_new not in Wall:
                Wall[key_new] = 0
            Wall[key_new] += edges_out[key]
        return Domain, Wall

    def stat_domain(self, divide=False, size_th=1, connectivity=6, pbc=True, f_mods=None):
        """ Statistics of domains and walls (multiple data)
        
        Parameters
        ----------
        divide: bool
            [False], True if using divided identical phases for different orientations 
        size_th: int
            [1], threshold size of domain, bypass size < size_th
        connectivity: int
            for 3D: [6], 18, 26; for 2D: 4, 8
        pbc: bool
            [True] if periodic boundary condition
        f_mods: str
            list of modulation files to exclude STO inclusion (-8) while counting domains
        
        Returns
        -------
        Domain: dict
            statistics of domains
        Wall: dict
            statistics of walls
        """
        Phase = self.Phase
        DW = OrderedDict()
        for i,_f in enumerate(self.files):
            if f_mods is not None:
                f_mod = f_mods[i]
            else:
                f_mod = None
            Domain, Wall = self._stat_domain(Phase[i], divide, size_th, connectivity, pbc, f_mod)
            DW[i] = (Domain, Wall)
        self.DW = DW
        return DW

    @staticmethod
    def _print_wall_type(DW):
        """ Determining wall type from the output of `_stat_domain` (single data)

        Parameters
        ----------
        DW: tuple(2)
            output from `_stat_domain`
        
        Returns
        -------
        res: dict
            {Wall: (Domain0, Domain1)}
        """
        f_phvar = lambda vx,vy,vz: (vx+1)*3**2 + (vy+1)*3 + (vz+1)
        def map_phase(phvar):
            # C:0, T:100-105, O:200-211, R:300-307
            phase_ind = [13]+[-1]*99+\
                        [4,10,12,14,16,22]+[-1]*94+\
                        [1,3,5,7,9,11,15,17,19,21,23,25]+[-1]*88+\
                        [0,2,6,8,18,20,24,26]
            return phase_ind.index(phvar)
        Orient_Dict = {}
        for x in [-1,0,1]:
            for y in [-1,0,1]:
                for z in [-1,0,1]:
                    v = map_phase(f_phvar(x,y,z))
                    Orient_Dict[v] = (x,y,z)
        # Wall type
        f_norm = lambda v: np.sqrt(np.sum(np.array(v)**2))
        f_angle = lambda u,v: np.arccos(np.clip(np.dot(u,v)/f_norm(u)/f_norm(v), -1.0, 1.0))/np.pi*180
        Domain, Wall = DW
        phases = list(Domain.keys())
        res = {}
        for key in Wall:
            # only for identical phase
            if key[0][0]==key[1][0] and key[0] in phases and key[1] in phases:
                u, v = [Orient_Dict[int(k.split('_')[0][1:])] for k in key]
                wall_type = f'{key[0][0]}{f_angle(u,v):.0f}'
                if wall_type not in res:
                    res[wall_type] = []
                res[wall_type].append(key)
        return res

    def print_wall_type(self):
        """ Determining wall type (multiple data)

        Parameters
        ----------
        None
            Use this function after using `stat_domain`

        Returns
        -------
        W: dict
        """
        DW = self.DW
        W = OrderedDict()
        for i in DW:
            res = self._print_wall_type(DW[i])
            W[i] = res
        return W

    def plot_2d_incl(self, norm=2, ind=0, cax=3, u_crit=0.015, incl_size=(0,0,0), width=0.0025, scale=4, 
                     show_periodic=False, size_th=1, arr_dict=None, f_mod=None, 
                     figsize=(3,3), fig_out=None):
        """ Plotting 2D snapshots for STO inclusion in BTO system
        
        Parameters
        ----------
        norm: int
            normal along 0:x, 1:y, 2:z (default: 2)
        ind: int
            layer index (default: 0)
        cax: int
            0: ux, 1: uy, 2: uz, [3]: phase
        u_crit: float
            criterion for the strength of u, above means order, below means disorder
        incl_size: tuple(3)
            (x,y,z) size of inclusion
        width: float
            width of arrow
        scale: float
            scale for arrow length (the larger, the shorter)
        show_periodic: bool
            show periodic images (3x3x3)
        size_th: int
            threshold size of domain, bypass size < size_th (default: 1)
        arr_dict: dict
            additional arrows by giving {'i': [ipos], 'j': [jpos], 'ui': [ui], 'uj': uj}
        f_mod: str
            read modulation file to exclude STO inclusion (-8) while counting domains

        Returns
        -------
        None
        """
        # assign phase & color
        colors_ph = {0: 'black', 1: 'red', 2: 'cyan', 3: 'blue'} # C,T,O,R
        Phase = self.assign_phase(u_crit=u_crit)
        _Phase = self.assign_phase(u_crit=u_crit, divide=True)
        normal = [0,1,2]
        normal.pop(norm)
        incl_i, incl_j = [incl_size[_] for _ in normal]
        incl_norm = incl_size[norm]
        i_label, j_label = ['XYZ'[_] for _ in normal]
        brr = 1
        for i,_f in enumerate(self.files):
            x=self.Dataset[i]['x']
            y=self.Dataset[i]['y']
            z=self.Dataset[i]['z']
            ux=self.Dataset[i]['ux']
            uy=self.Dataset[i]['uy']
            uz=self.Dataset[i]['uz']
            px = ux*brr
            py = uy*brr
            pz = uz*brr
            if cax==3:
                c = Phase[i]
            elif cax in [0, 1, 2]:
                _p = [px, py, pz]
                c = np.where(_p[cax]>=0, 1, 3)
            L_i, L_j = [self.Size[i][_] for _ in normal]

            # select data
            xyz_min = [0 if j!=norm else ind for j,_ in enumerate(self.Size[i])]
            xyz_max = [_ if j!=norm else ind+1 for j,_ in enumerate(self.Size[i])]
            x_min, x_max = xyz_min[0], xyz_max[0]
            y_min, y_max = xyz_min[1], xyz_max[1]
            z_min, z_max = xyz_min[2], xyz_max[2]
            Ixyz = [x, y, z]
            Ixyz.pop(norm)
            Iij = [_I[z_min:z_max, y_min:y_max, x_min:x_max].ravel() for _I in Ixyz]
            Pxyz = [px, py, pz]
            Pxyz.pop(norm)
            Pij = [_P[z_min:z_max, y_min:y_max, x_min:x_max].ravel() for _P in Pxyz]
            C = [colors_ph[_c] for _c in c[z_min:z_max, y_min:y_max, x_min:x_max].ravel()]
            # domains
            phase = _Phase[i]
            # modulation
            if f_mod is not None:
                mod = parse_data(f_mod, fmt='modulation')
                sto = mod[mod['mod']==-8]
                x_sto, y_sto, z_sto = sto['x'], sto['y'], sto['z']
                for _x,_y,_z in zip(x_sto, y_sto, z_sto):
                    phase[_z,_y,_x] = 0
            Ph = phase[z_min:z_max, y_min:y_max, x_min:x_max]
            labels_out = cc3d.connected_components(Ph, connectivity=6, periodic_boundary=False)
            stat = cc3d.statistics(labels_out)
            Phi = []
            for _s in range(len(stat['centroids'])):
                _ind = np.where(labels_out.ravel()==_s)
                _n = len(_ind[0])
                if _n!=0:
                    _psum = np.sum(np.abs(Pij), axis=0)
                    _i = np.argsort([_psum[_] for _ in _ind[0]])[_n//2]
                    Phi.append(_ind[0][_i])
                else:
                    Phi.append(None)

            # overlap-arrows
            def overlap_arrows(ax, i_chk=False):
                clo, chi = (box/2.-incl_norm/2.), (box/2.+incl_norm/2.)
                for v_i, v_count, v_cent in zip(Phi, stat['voxel_counts'], stat['centroids']):
                    if v_count < size_th:
                        continue
                    ctz, cty, ctx = v_cent
                    ctij = [ctx, cty, ctz]
                    ctij.pop(norm)
                    if i_chk and clo<=ctij[0]<=chi and clo<=ctij[1]<=chi:
                        continue
                    cpij = Pij[0][v_i], Pij[1][v_i]
                    if np.all(np.abs(cpij) < u_crit*2): # u_crit*2
                        continue
                    ax.quiver(ctij[0], ctij[1], cpij[0], cpij[1], color='k', units='width', pivot='mid', 
                              headlength=2, headaxislength=2, headwidth=3.236, width=0.03, scale=1)
                # additional arrows
                if arr_dict is not None:
                    i_add, j_add = arr_dict['i'], arr_dict['j']
                    ui_add, uj_add = arr_dict['ui'], arr_dict['uj']
                    for _i,_j, aui,auj in zip(i_add, j_add, ui_add, uj_add):
                        ax.quiver(_i, _j, aui, auj, color='k', units='width', pivot='mid', 
                                  headlength=2, headaxislength=2, headwidth=3.236, width=0.03, scale=1)

            # dipole arrows
            fig, ax = plt.subplots(figsize=figsize, dpi=300)
            if not show_periodic:
                ax.quiver(Iij[0], Iij[1], Pij[0], Pij[1], color=C, units='width', pivot='mid',
                          width=width, scale=scale)
            if show_periodic:
                for _i in (-1,0,1):
                    for _j in (-1,0,1):
                        ax.quiver(Iij[0]+_i*L_i, Iij[1]+_j*L_j, Pij[0], Pij[1], color=C, units='width', pivot='mid',
                                  width=width/3, scale=scale*3)

            # inclusion box
            i_chk = 0
            box = self.Size[i][norm]
            if ind<(box/2.-incl_norm/2.) or ind>(box/2.+incl_norm/2.): #layer in the middle between the two structure(m)
                ls="dotted"
            elif ind==(box/2.-incl_norm/2.) or ind==(box/2.+incl_norm/2.): #layer at the interface of the two structure (i)
                ls="dashed"
            elif ind>(box/2.-incl_norm/2.) and ind<(box/2.+incl_norm/2.): #layer at the center of the two structure (c)
                ls="solid"
                i_chk = 1
            if incl_i!=0 and incl_j!=0:
                if not show_periodic:
                    rec_o = (box/2.-incl_i/2., box/2.-incl_j/2.)
                    rectangle = plt.Rectangle(rec_o, incl_i-1, incl_j-1, fc = "None", ec= "k", lw = 1.2, ls=ls)
                    ax.add_patch(rectangle)
                if show_periodic:
                    for _i in (-1,0,1):
                        for _j in (-1,0,1):
                            rec_o = (box/2.-incl_i/2.+_i*L_i, box/2.-incl_j/2.+_j*L_j)
                            rectangle = plt.Rectangle(rec_o, incl_i-1, incl_j-1, fc = "None", ec= "k", lw = 1.2, ls=ls)
                            ax.add_patch(rectangle)

            plt.axis('scaled')
            if show_periodic:
                # inset
                axins = ax.inset_axes([1.04, 0.1, .8, .8])
                axins.quiver(Iij[0], Iij[1], Pij[0], Pij[1], color=C, units='width', pivot='mid',
                      width=width, scale=scale)
                overlap_arrows(axins, i_chk)
                rec_o = (box/2.-incl_i/2., box/2.-incl_j/2.)
                rectangle = plt.Rectangle(rec_o, incl_i-1, incl_j-1, fc = "None", ec= "k", lw = 1.2, ls=ls)
                axins.add_patch(rectangle)
                axins.set_xticks([])
                axins.set_yticks([])
                axins.set_xticklabels([])
                axins.set_yticklabels([])
                axins.set_xlim(xyz_min[normal[0]], xyz_max[normal[0]])
                axins.set_ylim(xyz_min[normal[1]], xyz_max[normal[1]])
                axins.set_aspect('equal', adjustable='box', anchor='C')
                ax.indicate_inset_zoom(axins, edgecolor='k', alpha=0.5, ls='--', lw=0.8)
                # ticks/ticklabels
                ax.set_xticks([])
                ax.set_yticks([])
                ax.set_xticklabels([])
                ax.set_yticklabels([])
                # frame
                for _ in ['bottom','left','top','right']:
                    ax.spines[_].set_visible(False)
            else:
                overlap_arrows(ax, i_chk)
                ax.set_xlim(xyz_min[normal[0]]-1.5, xyz_max[normal[0]]+0.5)
                ax.set_ylim(xyz_min[normal[1]]-1.5, xyz_max[normal[1]]+0.5)
                ax.set_xlabel(i_label+' axis',fontsize=8, labelpad=2)
                ax.set_ylabel(j_label+' axis',fontsize=8, labelpad=2)
                plt.tick_params(labelsize=8,length=2,width=1)
                x_major_locator=MultipleLocator(10)  
                ax.xaxis.set_major_locator(x_major_locator)
                y_major_locator=MultipleLocator(10)  
                ax.yaxis.set_major_locator(y_major_locator)
                # frame
                bwith = 1.2
                ax.spines['bottom'].set_linewidth(bwith)
                ax.spines['left'].set_linewidth(bwith)
                ax.spines['top'].set_linewidth(bwith)
                ax.spines['right'].set_linewidth(bwith)
            
            # save figure
            if fig_out!=None:
                rem, ext = fig_out.rsplit('.', maxsplit=1)
                plt.savefig(f'{rem}_{i}.{ext}', dpi=fig.dpi, bbox_inches='tight')
            plt.show()

    def plot_phase_catalog(self, mode='ratio', u_crit=0.015, divide=False, tags=None, add_data=None, figsize=None, fig_out=None):
        """ Plotting phases catalog

        Parameters
        ----------
        mode: str
            ['ratio'], 'count'
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        tags: list
            tags for xticklabels
        add_data: list
            list of files for additional data
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the figure of the phases catalog
        """
        def _plot(res, ix=0, width=0.5, alpha=0.5, hatch=''):
            di = ix*width/2
            for i in res:
                labels, count = res[i]
                sort_zip = sorted(zip(labels, count), key=lambda x: list('ROTC').index(x[0][1]))
                labels = list(map(str, np.array(sort_zip)[:,0]))
                count = list(map(int, np.array(sort_zip)[:,1]))
                if mode=='ratio':
                    ntot = sum(count)
                    bottom = 0
                    colors = ['r', 'g', 'b', 'm', 'c', 'k', 'y']
                    c_dict = {}
                    if divide:
                        for lab, c in zip(labels, count):
                            if lab not in c_dict:
                                _c = colors.pop(0)
                                c_dict[lab] = _c
                                colors.append(_c)
                            ratio = 100*c/ntot
                            ax.bar(i+di, ratio, width=width, color=c_dict[lab], alpha=alpha, hatch=hatch,
                                   label=lab, bottom=bottom)
                            ax.text(i-0.1+di, ratio/2+bottom, lab, size=12, weight='normal')
                            bottom+=ratio
                    else:
                        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
                        CSUM = {_:0 for _ in 'ROTC'}
                        PSUM = {_:0 for _ in 'ROTC'}
                        for lab, c in zip(labels, count):
                            t = lab[1]
                            CSUM[t]+=c
                            PSUM[t]+=1
                        for t in ('R', 'O', 'T', 'C'):
                            ratio = 100*(CSUM[t]/ntot)
                            ax.bar(i+di, ratio, width=width, color=c_dict[t], alpha=alpha, hatch=hatch,
                                   label=t, bottom=bottom)
                            if ratio!=0:
                                n = PSUM[t]
                                ax.text(i-0.15+di, ratio*0.4+bottom, f'{t}'+'$_{('+f'{n}'+')}$', size=12, weight='normal')
                            bottom+=ratio
                elif mode=='count':
                    c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
                    CSUM = {}
                    for lab in labels:
                        t = lab[1]
                        if t not in CSUM:
                            CSUM[t] = 0
                        CSUM[t]+=1
                    w = 0.7/4
                    s = 0 if len(CSUM)==1 else w*len(CSUM)/2-w/2
                    for j,t in enumerate(CSUM):
                        ax.bar(i+j*w-s, CSUM[t], width=w, color=c_dict[t], alpha=alpha, hatch=hatch, label=t)

        # plot
        if add_data is not None:
            _STI = Dipo_Analysis_STI(*add_data)
            _STI.load_data()
            res_add = _STI.catalog_phase(u_crit=u_crit)
        res = self.catalog_phase(u_crit=u_crit)
        figsize = (len(res)*1.5,4) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        if add_data is None:
            _plot(res, ix=0, width=0.6, alpha=0.5)
        else:
            _plot(res, ix=-1, width=0.3, alpha=0.5)
            _plot(res_add, ix=1, width=0.3, alpha=0.9, hatch='')
        
        # x/y limits
        ax.set_xlim([-1, len(res)])
        if mode=='ratio':
            ax.set_ylim([0, 100])
        elif mode=='count':
            ax.set_ylim([0, None])
        if mode=='count' or (mode=='ratio' and divide==False):
            c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
            # legend
            from matplotlib.lines import Line2D
            from matplotlib.patches import Rectangle
            ha0 = lambda m: [Line2D([0],[0], marker=m, ms=14, mfc='k', mec='none', lw=0)]
            ha1 = lambda a,h: [Rectangle((0,0), 1,1, color=c_dict[t],alpha=a, hatch=h,ec='k', lw=0) for t in 'CTOR']
            if add_data is None:
                handles = ha1(0.5,'')
                ha_titles = list('CTOR')
                ncol = 1
            else:
                handles = ha0(r'$\leftarrow$')+ha1(0.5,'')+ha0(r'$\rightarrow$')+ha1(0.8,'')
                ha_titles = ['']+['']*4+['']+list('CTOR')
                ncol = 2
            ax.legend(handles, ha_titles, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False,
                      ncol=ncol, columnspacing=-0.5, prop={'size':12, 'weight': 'normal'})

        # x/y labels
        ax.set_ylabel(mode, fontsize=14, labelpad=None)
        plt.tick_params(which='major', labelsize=12, length=4, width=1)
        plt.tick_params(which='minor', labelsize=12, length=0)

        # ticks
        tags = np.arange(len(res)) if tags==None else tags
        ax.set_xticks(np.arange(len(res)),)
        ax.set_xticklabels(tags, rotation=0)

        # frame
        bwith = 1
        ax.spines['bottom'].set_linewidth(bwith)
        ax.spines['left'].set_linewidth(bwith)
        ax.spines['top'].set_linewidth(bwith)
        ax.spines['right'].set_linewidth(bwith)

        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        plt.show()

    def subplot_phase_catalog(self, mode='ratio', u_crit=0.015, divide=False, tags=None, show_annotation=False, figsize=None, fig_out=None):
        """ Sub-plotting phases catalog

        Parameters
        ----------
        mode: str
            ['ratio'], 'count'
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        tags: list
            tags for xticklabels
        show_annotation: bool
            [False], showing annotations of phases
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the figure of the phases catalog
        """
        def _plot(ha, res, ix=0, width=0.5, alpha=0.5, hatch=''):
            di = ix*width/2
            for i in res:
                labels, count = res[i]
                sort_zip = sorted(zip(labels, count), key=lambda x: list('ROTC').index(x[0][1]))
                labels = list(map(str, np.array(sort_zip)[:,0]))
                count = list(map(int, np.array(sort_zip)[:,1]))
                if mode=='ratio':
                    ntot = sum(count)
                    bottom = 0
                    colors = ['r', 'g', 'b', 'm', 'c', 'k', 'y']
                    c_dict = {}
                    if divide:
                        for lab, c in zip(labels, count):
                            if lab not in c_dict:
                                _c = colors.pop(0)
                                c_dict[lab] = _c
                                colors.append(_c)
                            ratio = 100*c/ntot
                            ax[ha].barh(i+di, ratio, height=width, left=bottom, color=c_dict[lab], 
                                        alpha=alpha, hatch=hatch, label=lab)
                            if show_annotation and ratio!=0:
                                ax[ha].text(ratio/3+bottom, i-0.05+di, lab, size=12, weight='normal')
                            bottom+=ratio
                    else:
                        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
                        CSUM = {_:0 for _ in 'ROTC'}
                        PSUM = {_:0 for _ in 'ROTC'}
                        for lab, c in zip(labels, count):
                            t = lab[1]
                            CSUM[t]+=c
                            PSUM[t]+=1
                        for t in ('R', 'O', 'T', 'C'):
                            ratio = 100*(CSUM[t]/ntot)
                            ax[ha].barh(i+di, ratio, height=width, left=bottom, color=c_dict[t], 
                                        alpha=alpha, hatch=hatch, label=lab)
                            if ratio!=0:
                                n = PSUM[t]
                            if show_annotation and ratio!=0:
                                ax[ha].text(ratio/3+bottom, i-0.05+di, f'{t}'+'$_{('+f'{n}'+')}$', size=12, weight='normal')
                            bottom+=ratio
                elif mode=='count':
                    c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
                    CSUM = {}
                    for lab in labels:
                        t = lab[1]
                        if t not in CSUM:
                            CSUM[t] = 0
                        CSUM[t]+=1
                    i_dict = {'C': 8+12+6, 'T': 8+12, 'O': 8, 'R': 0}
                    for j,t in enumerate(CSUM):
                        ax[ha].barh(i+di, CSUM[t], height=width, left=i_dict[t], color=c_dict[t], 
                                    alpha=alpha, hatch=hatch, label=t)
                        if show_annotation and CSUM[t]!=0:
                            ax[ha].text(i_dict[t]+CSUM[t]//2, i+di, f'{CSUM[t]}', size=12, weight='normal')
                if di!=0:
                    ax[ha].axhline(y=i, ls='-', lw=0.7, color='k')

        LSTI = self.LSTI
        if LSTI is None:
            print("[Error]: Please use `load_dataset` before using `subplot_phase_catalog`.")
            return
        else:
            LSTI_main = LSTI['main']
            LSTI_add = LSTI['add']
        # plot
        n_key = len(LSTI_main)
        n_files = len(self.files)
        figsize = (n_key*1.5, n_files*0.5) if figsize==None else figsize
        fig, ax = plt.subplots(1, n_key, sharex=True, gridspec_kw={'wspace': 0.0},
                               figsize=figsize, dpi=100)
        for ha, key in enumerate(LSTI_main):
            _STI_main = LSTI_main[key]
            res = _STI_main.catalog_phase(u_crit=u_crit)
            if LSTI_add is None:
                _plot(ha, res, ix=0, width=0.6, alpha=0.5)
            else:
                _STI_add = LSTI_add[key]
                res_add = _STI_add.catalog_phase(u_crit=u_crit)
                _plot(ha, res, ix=-1, width=0.3, alpha=0.5)
                _plot(ha, res_add, ix=1, width=0.3, alpha=0.9, hatch='')
            # ylim
            ax[ha].set_ylim([-0.5, len(res)-0.5])
            # x/yticks
            if ha!=0:
                ax[ha].set_yticks(np.arange(n_files),)
                ax[ha].set_yticklabels([])
            ax[ha].tick_params(which='major', labelsize=12, length=4, width=1)
            ax[ha].tick_params(which='both', axis='x', labelrotation=-90)
            ax[ha].tick_params(which='minor', labelsize=12, length=0)
            # xlabels
            unit = '%' if mode=='ratio' else ''
            ax[ha].set_xlabel(f'{mode} {unit}', fontsize=14, labelpad=6)
            # grid
            if mode=='count':
                ax[ha].grid(axis='x', linestyle='--')
            # frame
            bwith = 1
            ax[ha].spines['bottom'].set_linewidth(bwith)
            ax[ha].spines['left'].set_linewidth(bwith)
            ax[ha].spines['top'].set_linewidth(bwith)
            ax[ha].spines['right'].set_linewidth(bwith)
            # title
            ax[ha].set_title(key, fontsize=14, fontweight='normal')
        # xlim
        if mode=='ratio':
            ax[0].set_xlim([0-7, 100+7])
        elif mode=='count':
            ax[0].set_xlim([0, None])
        # legend
        # legend
        if mode=='count' or (mode=='ratio' and divide==False):
            c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
            from matplotlib.lines import Line2D
            from matplotlib.patches import Rectangle
            ha0 = lambda m: [Line2D([0],[0], marker=m, ms=14, mfc='k', mec='none', lw=0)]
            ha1 = lambda a,h: [Rectangle((0,0), 1,1, color=c_dict[t],alpha=a, hatch=h,ec='k', lw=0) for t in 'CTOR']
            if LSTI_add is None:
                handles = ha1(0.5,'')
                ha_titles = list('CTOR')
                ncol = 1
            else:
                handles = ha0(r'$\downarrow$')+ha1(0.5,'')+ha0(r'$\uparrow$')+ha1(0.8,'')
                ha_titles = ['']+['']*4+['']+list('CTOR')
                ncol = 2
            ax[-1].legend(handles, ha_titles, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False,
                      ncol=ncol, columnspacing=-0.5, prop={'size':12, 'weight': 'normal'})
        # yticks
        tags = np.arange(len(n_files)) if tags==None else tags
        ax[0].set_yticks(np.arange(n_files),)
        ax[0].set_yticklabels(tags)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        plt.show()

    def plot_domain_size(self, bin_size=1000, xlim=None, ylim=None, figsize=None, fig_out=None):
        """ Plotting histogram of domain sizes

        Parameters
        ----------
        bin_size: int
            size of bins for domain sizes
        xlim: list/tuple
            x-range of plotting
        ylim: list/tuple
            y-range of plotting
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the histogram of the domain sizes
        """
        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b', 'S': 'g'}
        if self.DW is None:
            print('[Error]: Please run `stat_domain` before using `plot_domain_size`!')
            return
        DW = self.DW
        for i in DW:
            Domain, Wall = DW[i]
            bins = np.arange(0, np.max(list(Domain.values())) + bin_size, bin_size)
            DSize = {}
            for di in Domain:
                ph = di[0]
                if ph not in DSize:
                    DSize[ph] = []
                size = Domain[di]
                DSize[ph].append(size)
            # plot
            fig, ax = plt.subplots(2,1, sharex=True, figsize=figsize, dpi=100)
            fig.subplots_adjust(hspace=0.05)
            figsize = plt.gcf().get_size_inches()
            bottom = 0
            hmax = 0
            for ph in 'SCTOR':
                if ph not in DSize:
                    continue
                h, _ = np.histogram(DSize[ph], bins=bins)
                for _ax in ax:
                    _ax.bar(bins[:-1]+bin_size/2, h, width=bin_size, bottom=bottom, 
                           color=c_dict[ph], alpha=0.5, label=ph)
                bottom += h
                if np.max(h)>hmax:
                    hmax = np.max(h)
            # text
            fig.text(0.9, 0.9, f'bin_size: {bin_size}', ha='right', color='k', fontsize=12)
            # x/ylim
            ax[0].set_xlim(xlim)
            ax[1].set_ylim(ylim)
            if ylim is not None:
                if hmax<ylim[-1]:
                    ax[0].set_ylim([ylim[-1], ylim[-1]*2])
                else:
                    ax[0].set_ylim([ylim[-1],None])
            # x/yticks+grid
            for _ax in ax:
                _ax.tick_params(which='major', labelsize=12, length=4, width=1)
                _ax.grid(axis='y', linestyle='--')
                _ax.yaxis.set_major_locator(mpl.ticker.MaxNLocator(nbins='auto', integer=True))
            ax[0].tick_params(labeltop=False, bottom=False)
            # frame
            for _ax in ax:
                _ax.spines['bottom'].set_linewidth(0)
                _ax.spines['top'].set_linewidth(0)
                _ax.spines['right'].set_linewidth(0)
            bwith = 1
            ax[1].spines['bottom'].set_linewidth(bwith)
            ax[1].spines['left'].set_linewidth(bwith)
            # x/ylabel
            ax[1].set_xlabel(r'domain size ($u.c.^3$)', fontsize=14, fontweight='normal')
            ax[0].set_ylabel('counts'+' '*int(figsize[-1])*6, fontsize=14, fontweight='normal')
            ax[1].legend(loc='center left', bbox_to_anchor=(1,1), frameon=False, 
                         prop={'size': 12, 'weight': 'normal'})
            # broken symbol from matplotlib example
            kwargs = dict(marker=[(-1, -0.5), (1, 0.5)], markersize=12, color='k', clip_on=False)
            ax[0].plot([0], [0], transform=ax[0].transAxes, **kwargs)
            ax[1].plot([0], [1], transform=ax[1].transAxes, **kwargs)
            if fig_out!=None:
                fig.savefig(f'{i}_'+fig_out, dpi=fig.dpi)
            else:
                plt.show()
        plt.close('all')

    def plot_domain_counts(self, u_crit=0.015, divide=False, size_th=1, tags=None, f_mods=None,
                           figsize=None, fig_out=None):
        """ Plotting domain counts (line)

        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        size_th: int
            [0], threshold size of domain
        tags: list
            tags for xticklabels
        f_mods: list
            list of modulation file(s) to exclude STO inclusion (-8) while counting domains
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the figure of the domain counts
        """
        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
        def count_domain(Domain):
            DCount = {_: 0 for _ in 'ROTCS'}
            for phd in Domain:
                DCount[phd[0]] += 1
            return DCount

        def _collect(DW):
            DCS = {}
            for i in DW:
                Domain, Wall = DW[i]
                DCount = count_domain(Domain)
                for ph in 'ROTC':
                    if ph not in DCS:
                        DCS[ph] = []
                    DCS[ph].append(DCount[ph])
            return DCS

        # load data
        Phase = self.assign_phase(u_crit=u_crit, divide=divide)
        DW = self.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
        DCS = _collect(DW)
        # plot
        figsize = (len(DCS)*1.5,4) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        for ph in 'CTOR':
            ax.plot(DCS[ph], marker='s', color=c_dict[ph], label=ph)

        # x/y limits
        ax.set_xlim([-1, len(DW)])
        ax.set_ylim([0, None])
        # legend
        from matplotlib.lines import Line2D
        handles = [Line2D([0],[0], marker='s', color=c_dict[ph]) for ph in 'CTOR']
        ha_titles = list('CTOR')
        ax.legend(handles, ha_titles, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False,
                  prop={'size':12, 'weight': 'normal'})

        # x/y labels
        ax.set_ylabel('Domain counts', fontsize=14, labelpad=None)
        plt.tick_params(which='major', labelsize=12, length=4, width=1)
        plt.tick_params(which='minor', labelsize=12, length=0)

        # ticks
        tags = np.arange(len(DW)) if tags==None else tags
        ax.set_xticks(np.arange(len(DW)),)
        ax.set_xticklabels(tags, rotation=0)

        # frame
        bwith = 1
        ax.spines['bottom'].set_linewidth(bwith)
        ax.spines['left'].set_linewidth(bwith)
        ax.spines['top'].set_linewidth(bwith)
        ax.spines['right'].set_linewidth(bwith)

        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        plt.show()

    def subplot_domain_counts(self, u_crit=0.015, divide=False, size_th=1, tags=None, f_mods=None,
                              figsize=None, fig_out=None):
        """ Sub-plotting domain counts (line)

        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        size_th: int
            [0], threshold size of domain
        tags: list
            tags for xticklabels
        f_mods: list
            list of modulation file(s) to exclude STO inclusion (-8) while counting domains
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the figure of the domain counts
        """
        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
        def count_domain(Domain):
            DCount = {_: 0 for _ in 'ROTCS'}
            for phd in Domain:
                DCount[phd[0]] += 1
            return DCount

        def _collect(DW):
            DCS = {}
            for i in DW:
                Domain, Wall = DW[i]
                DCount = count_domain(Domain)
                for ph in 'ROTC':
                    if ph not in DCS:
                        DCS[ph] = []
                    DCS[ph].append(DCount[ph])
            return DCS

        def _plot(ha, DCS, m='s', ls='-'):
            hmax = 0
            for ph in 'CTOR':
                count = DCS[ph]
                ax[ha].plot(count, np.arange(len(count)), linestyle=ls, marker=m, color=c_dict[ph])
                if np.max(count)>hmax:
                    hmax = np.max(count)
            return hmax

        LSTI = self.LSTI
        if LSTI is None:
            print("[Error]: Please use `load_dataset` before using `subplot_domain_density`.")
            return
        else:
            LSTI_main = LSTI['main']
            LSTI_add = LSTI['add']
        # plot
        n_key = len(LSTI_main)
        n_files = len(self.files)
        figsize = (n_key*1.5, n_files*0.5) if figsize==None else figsize
        fig, ax = plt.subplots(1, n_key, sharex=True, gridspec_kw={'wspace': 0.0}, figsize=figsize, dpi=100)
        max_all = 0
        for ha, key in enumerate(LSTI_main):
            _STI_main = LSTI_main[key]
            Phase = _STI_main.assign_phase(u_crit=u_crit, divide=divide)
            DW = _STI_main.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
            DCS = _collect(DW)
            if LSTI_add is None:
                hmax = hmax_add = _plot(ha, DCS, m='s', ls='-')
            else:
                _STI_add = LSTI_add[key]
                Phase_add = _STI_add.assign_phase(u_crit=u_crit, divide=divide)
                DW_add = _STI_add.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
                DCS_add = _collect(DW_add)
                hmax = _plot(ha, DCS, m='v', ls='-')
                hmax_add = _plot(ha, DCS_add, m='^', ls='-')
            # ylim
            xmax = max([hmax, hmax_add])
            if xmax>max_all:
                max_all = xmax
            ax[ha].set_ylim([-0.5, len(DW)-0.5])
            # x/yticks
            if ha!=0:
                ax[ha].set_yticks(np.arange(n_files),)
                ax[ha].set_yticklabels([])
            ax[ha].tick_params(which='major', labelsize=12, length=4, width=1)
            ax[ha].tick_params(which='both', axis='x', labelrotation=-90)
            ax[ha].tick_params(which='minor', labelsize=12, length=0)
            # xlabel
            if ha==n_key//2:
                ax[ha].set_xlabel('Domain counts'+' '*16*(n_key%2==0), fontsize=14, labelpad=12)
            # frame
            bwith = 1
            ax[ha].spines['bottom'].set_linewidth(bwith)
            ax[ha].spines['left'].set_linewidth(bwith)
            ax[ha].spines['top'].set_linewidth(bwith)
            ax[ha].spines['right'].set_linewidth(bwith)
            # title
            ax[ha].set_title(key, fontsize=14, fontweight='normal')
        # xlim
        dx = max_all*0.07
        ax[0].set_xlim([0-dx, max_all+dx])
        # legend
        from matplotlib.lines import Line2D
        ha0 = lambda m: [Line2D([0],[0], marker=m, ms=14, mfc='k', mec='none', lw=0)]
        ha1 = lambda m: [Line2D([0],[0], marker=m, color=c_dict[t]) for t in 'CTOR']
        if LSTI_add is None:
            handles = ha1('s')
            ha_titles = list('CTOR')
            ncol = 1
        else:
            handles = ha0(r'$\downarrow$')+ha1('v')+ha0(r'$\uparrow$')+ha1('^')
            ha_titles = ['']+['']*4+['']+list('CTOR')
            ncol = 2
        ax[-1].legend(handles, ha_titles, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False,
                      ncol=ncol, columnspacing=-0.5, prop={'size':12, 'weight': 'normal'})
        # yticks
        tags = np.arange(len(n_files)) if tags==None else tags
        ax[0].set_yticks(np.arange(n_files),)
        ax[0].set_yticklabels(tags)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        plt.show()

    def plot_domain_density(self, u_crit=0.0015, divide=False, size_th=1, volumes=None, tags=None, add_data=None, 
                            f_mods=None, figsize=None, fig_out=None):
        """ Plotting domain density/counts

        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        size_th: int
            [1], threshold size of domain, bypass size < size_th
        volumes: list
            [None], list of volumes for normalization
        tags: list
            tags for xticklabels
        add_data: list
            list of files for additional data
        f_mods: list
            list of modulation file(s) to exclude STO inclusion (-8) while counting domains
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the figure of domain density/count
        """
        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
        def count_domain(Domain):
            DCount = {_: 0 for _ in 'ROTCS'}
            for phd in Domain:
                DCount[phd[0]] += 1
            return DCount

        def _plot(DW, volumes, ix=0, width=0.5, alpha=0.5, hatch=''):
            volumes = [1]*len(DW) if volumes is None else volumes
            di = ix*width/2
            for i in DW:
                Domain, Wall = DW[i]
                DCount = count_domain(Domain)
                bottom = 0
                for t in 'ROTC':
                    density = DCount[t]/volumes[i]
                    ax.bar(i+di, density, width=width, color=c_dict[t], alpha=alpha, hatch=hatch, 
                           label=t, bottom=bottom)
                    if density!=0:
                        ax.text(i-0.07+di, density*0.3+bottom, f'{t}', size=12, weight='normal')
                    bottom+=density
        # load data
        if add_data is not None:
            _STI = Dipo_Analysis_STI(*add_data)
            _STI.load_data()
            Phase_add = _STI.assign_phase(u_crit=u_crit, divide=divide)
            DW_add = _STI.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
        Phase = self.assign_phase(u_crit=u_crit, divide=divide)
        DW = self.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
        # plot
        figsize = (len(DW)*1.5,4) if figsize==None else figsize
        fig, ax = plt.subplots(figsize=figsize, dpi=100)
        if add_data is None:
            _plot(DW, volumes, ix=0, width=0.6, alpha=0.5)
        else:
            _plot(DW, volumes, ix=-1, width=0.3, alpha=0.5)
            _plot(DW_add, volumes, ix=1, width=0.3, alpha=0.9, hatch='')

        # x/y limits
        ax.set_xlim([-1, len(DW)])
        ax.set_ylim([0, None])
        # legend
        from matplotlib.lines import Line2D
        from matplotlib.patches import Rectangle
        ha0 = lambda m: [Line2D([0],[0], marker=m, ms=14, mfc='k', mec='none', lw=0)]
        ha1 = lambda a,h: [Rectangle((0,0), 1,1, color=c_dict[t],alpha=a, hatch=h,ec='k', lw=0) for t in 'CTOR']
        if add_data is None:
            handles = ha1(0.5,'')
            ha_titles = list('CTOR')
            ncol = 1
        else:
            handles = ha0(r'$\leftarrow$')+ha1(0.5,'')+ha0(r'$\rightarrow$')+ha1(0.8,'')
            ha_titles = ['']+['']*4+['']+list('CTOR')
            ncol = 2
        ax.legend(handles, ha_titles, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False,
                  ncol=ncol, columnspacing=-0.5, prop={'size':12, 'weight': 'normal'})

        # x/y labels
        ylab = 'density ($a.u.$)' if volumes is not None else 'counts'
        ax.set_ylabel(f'Domain {ylab}', fontsize=14, labelpad=None)
        plt.tick_params(which='major', labelsize=12, length=4, width=1)
        plt.tick_params(which='minor', labelsize=12, length=0)

        # ticks
        tags = np.arange(len(DW)) if tags==None else tags
        ax.set_xticks(np.arange(len(DW)),)
        ax.set_xticklabels(tags, rotation=0)

        # frame
        bwith = 1
        ax.spines['bottom'].set_linewidth(bwith)
        ax.spines['left'].set_linewidth(bwith)
        ax.spines['top'].set_linewidth(bwith)
        ax.spines['right'].set_linewidth(bwith)

        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        plt.show()

    def subplot_domain_density(self, u_crit=0.015, divide=False, size_th=1, volumes=None, tags=None, show_annotation=False,
                               f_mods=None, figsize=None, fig_out=None):
        """ Sub-plotting domain density/counts

        Parameters
        ----------
        u_crit: float
            criterion for dipole displacement; 1 if >u_crit, 0 if <u_crit
        divide: bool
            [False], divided identical phases for different orientations (only for mode='ratio')
        size_th: int
            [1], threshold size of domain, bypass size < size_th
        volumes: list
            [None], list of volumes for normalization
        tags: list
            tags for xticklabels
        show_annotation: bool
            [False], showing annotations of phases
        f_mods: list
            list of modulation file(s) to exclude STO inclusion (-8) while counting domains
        figsize: tuple
            size of figure
        fig_out: str
            output filename

        Returns
        -------
        None
            display/output the figure of domain density/count
        """
        c_dict = {'C': 'k', 'T': 'r', 'O': 'cyan', 'R': 'b'}
        def count_domain(Domain):
            DCount = {_: 0 for _ in 'ROTCS'}
            for phd in Domain:
                DCount[phd[0]] += 1
            return DCount

        def _plot(ha, DW, volumes, ix=0, width=0.5, alpha=0.5, hatch=''):
            volumes = [1]*len(DW) if volumes is None else volumes
            di = ix*width/2
            hmax = 0
            for i in DW:
                Domain, Wall = DW[i]
                DCount = count_domain(Domain)
                bottom = 0
                for t in 'ROTC':
                    density = DCount[t]/volumes[i]
                    ax[ha].barh(i+di, density, height=width, left=bottom, color=c_dict[t], 
                                alpha=alpha, hatch=hatch, label=t)
                    if show_annotation and density!=0:
                        ax[ha].text(density*0.3+bottom, i-0.07+di, t, size=12, weight='normal')
                    bottom+=density
                if di!=0:
                    ax[ha].axhline(y=i, ls='-', lw=0.7, color='k')
                if bottom>hmax:
                    hmax = bottom
            return hmax

        LSTI = self.LSTI
        if LSTI is None:
            print("[Error]: Please use `load_dataset` before using `subplot_domain_density`.")
            return
        else:
            LSTI_main = LSTI['main']
            LSTI_add = LSTI['add']
        # plot
        n_key = len(LSTI_main)
        n_files = len(self.files)
        figsize = (n_key*1.5, n_files*0.5) if figsize==None else figsize
        fig, ax = plt.subplots(1, n_key, sharex=True, gridspec_kw={'wspace': 0.0}, figsize=figsize, dpi=100)
        max_all = 0
        for ha, key in enumerate(LSTI_main):
            _STI_main = LSTI_main[key]
            Phase = _STI_main.assign_phase(u_crit=u_crit, divide=divide)
            DW = _STI_main.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
            if LSTI_add is None:
                hmax = hmax_add = _plot(ha, DW, volumes, ix=0, width=0.6, alpha=0.5)
            else:
                _STI_add = LSTI_add[key]
                Phase_add = _STI_add.assign_phase(u_crit=u_crit, divide=divide)
                DW_add = _STI_add.stat_domain(divide=divide, size_th=size_th, connectivity=6, pbc=True, f_mods=f_mods)
                hmax = _plot(ha, DW, volumes, ix=-1, width=0.3, alpha=0.5)
                hmax_add = _plot(ha, DW_add, volumes, ix=1, width=0.3, alpha=0.9, hatch='')
            # ylim
            xmax = max([hmax, hmax_add])
            if xmax>max_all:
                max_all = xmax
            ax[ha].set_ylim([-0.5, len(DW)-0.5])
            # x/yticks
            if ha!=0:
                ax[ha].set_yticks(np.arange(n_files),)
                ax[ha].set_yticklabels([])
            ax[ha].tick_params(which='major', labelsize=12, length=4, width=1)
            ax[ha].tick_params(which='both', axis='x', labelrotation=-90)
            ax[ha].tick_params(which='minor', labelsize=12, length=0)
            # xlabel
            if ha==n_key//2:
                xlab = 'density (a.u.)' if volumes is not None else 'counts'
                ax[ha].set_xlabel(f'Domain {xlab}'+' '*16*(n_key%2==0), fontsize=14, labelpad=12)
            # grid
            if volumes is None:
                ax[ha].grid(axis='x', linestyle='--')
            # frame
            bwith = 1
            ax[ha].spines['bottom'].set_linewidth(bwith)
            ax[ha].spines['left'].set_linewidth(bwith)
            ax[ha].spines['top'].set_linewidth(bwith)
            ax[ha].spines['right'].set_linewidth(bwith)
            # title
            ax[ha].set_title(key, fontsize=14, fontweight='normal')
        # xlim
        dx = max_all*0.07
        ax[0].set_xlim([0-dx, max_all+dx])
        # legend
        from matplotlib.lines import Line2D
        from matplotlib.patches import Rectangle
        ha0 = lambda m: [Line2D([0],[0], marker=m, ms=14, mfc='k', mec='none', lw=0)]
        ha1 = lambda a,h: [Rectangle((0,0), 1,1, color=c_dict[t],alpha=a, hatch=h,ec='k', lw=0) for t in 'CTOR']
        if LSTI_add is None:
            handles = ha1(0.5,'')
            ha_titles = list('CTOR')
            ncol = 1
        else:
            handles = ha0(r'$\downarrow$')+ha1(0.5,'')+ha0(r'$\uparrow$')+ha1(0.8,'')
            ha_titles = ['']+['']*4+['']+list('CTOR')
            ncol = 2
        ax[-1].legend(handles, ha_titles, loc='center left', bbox_to_anchor=(1, 0.5), frameon=False,
                      ncol=ncol, columnspacing=-0.5, prop={'size':12, 'weight': 'normal'})
        # yticks
        tags = np.arange(len(n_files)) if tags==None else tags
        ax[0].set_yticks(np.arange(n_files),)
        ax[0].set_yticklabels(tags)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, bbox_inches='tight')
        plt.show()

class HL_Analyzer_STI(HL_Analyzer):
    def plot_PE(self, xaxis=0, yaxis=0, solid=None, xlim=None, ylim=None, fill_mode=0, add_P0=None, fig_out=None):
        """ Plotting P-E loops
        
        Parameters
        ----------
        xaxis: int
            x-axis ([0]: Ex, 1: Ey, 2: Ez)
        yaxis: int
            y-axis ([0]: ux, 1: uy, 2: uz)
        solid: str
            prefactor for u (None, 'bto', 'bst')
        xlim: list
            [xlo, xhi]
        ylim: list
            [ylo, yhi] 
        fill_mode: int
            [0]: no fill, 1: fill P+, -1: fill P-
        add_P0: tuple/list
            list of P0s
        fig_out: str
            output figure

        Returns
        -------
        None
        """
        f_slope = lambda x: 1*( np.max(np.abs(np.gradient(x)))>np.finfo(float).eps )
        factor = prefactor(solid=solid) if solid!=None else 1
        colors = 'krgbcmy'
        # xdata
        ind = {0: 'Ex', 1: 'Ey', 2: 'Ez'}
        if xaxis in [0,1,2]:
            xind = ind[xaxis]
            XIND = [xind]*len(self.Dataset)
        elif isinstance(xaxis, list):
            XIND = [ind[_] for _ in xaxis]
        # ydata
        ind = {0: 'ux', 1: 'uy', 2: 'uz'}
        if yaxis in [0,1,2]:
            yind = [ind[yaxis]]
            ylabels = ['P'+'xyz'[yaxis]]
            ystyles = ['-']
            YIND = [yind]*len(self.Dataset)
        elif yaxis==3:
            yind = ['ux', 'uy', 'uz']
            ylabels = ['Px', 'Py', 'Pz']
            ystyles = ['--', ':', '-']
            YIND = [yind]*len(self.Dataset)
        elif isinstance(yaxis, list):
            YIND = [[ind[_]] for _ in yaxis]
            ylabels = ['P'+'xyz'[yaxis[0]]]
            ystyles = ['-']

        fig,ax = plt.subplots(figsize=(7,4), dpi=150)
        fw = 'normal'
        for i,key in enumerate(self.Dataset):
            hl_data = self.Dataset[key]
            for hl in hl_data:
                efxyz_ary = [hl[_]*1e5 for _ in ('Ex','Ey','Ez')] # kV/cm
                ef_ary = hl[XIND[i]]*1e5 # kV/cm
                u_ary = [hl[_]*factor for _ in YIND[i]] # Ang. or uC/cm^2
                #efdir = ' // [{}]'.format(''.join([str(f_slope(_ef)) for _ef in efxyz_ary]))
                efdir = 'xyz'[xaxis]
                for j,u in enumerate(u_ary):
                    ax.plot(ef_ary, u, linestyle=ystyles[j], color=colors[i%7], label=ylabels[j], lw=2)
            # P0
            if add_P0 is not None:
                ax.plot([0], add_P0[i], marker='s', mfc='none', mew=1.4, color=colors[i%7])

            # limits
            if xlim!=None:
                plt.xlim(xlim)
            if ylim!=None:
                plt.ylim(ylim)

            # labels
            plt.xlabel(fr"$E_{efdir}$ (kV/cm)", fontsize=20, fontweight=fw, labelpad=10)
            if factor==1:
                plt.ylabel("Dipole displacement ($\AA$)",fontsize=20, fontweight=fw, labelpad=10)
            else:
                ylabel = r"$P$" if yaxis==3 else fr"$P_{'xyz'[yaxis]}$"
                plt.ylabel(ylabel+r" (${\mu C/cm^{2}}$)", fontsize=20, fontweight=fw, labelpad=10)

            # legend
            if self.labels==None:
                ax.legend(loc=0, frameon=False, labelspacing=0.2, prop={'size':18, 'weight': fw})

            # ticks
            if xlim!=None:
                xmxa = np.max(np.abs(xlim))//50*50
                xticks = np.linspace(-xmxa, xmxa, 7)
                xticklabels = [int(xt) if _%2==1 else '' for _,xt in enumerate(xticks)]
                ax.set_xticks(xticks)
                ax.set_xticklabels(xticklabels)
            if ylim!=None:
                ax.set_yticks(np.linspace(ylim[0], ylim[1], 5))
            plt.xticks(fontsize=18, fontweight=fw)
            plt.yticks(fontsize=18, fontweight=fw)

        # fill PE
        hatchs = ['//','\\\\']
        if fill_mode!=0:
            rev = True if fill_mode==-1 else False
            _,EPm = self.cal_PE_integral(E_axis=xaxis, P_axis=yaxis, E_scale=1e5, P_scale='bst', E_ref=0, reverse=rev)
            for i,key in enumerate(EPm):
                EPmin, EPmax = EPm[key]
                E = np.array(list(EPmax.keys()))
                ind = np.argsort(E)
                E = E[ind]
                Pmin = np.array(list(EPmin.values()))[ind]
                Pmax = np.array(list(EPmax.values()))[ind]
                if fill_mode==-1:
                    ax.fill_between(E, Pmax, Pmin, color='k', alpha=0.2)
                    ax.fill_between(E, np.ones(len(E))*np.min(Pmax), Pmax, color=colors[i%7], alpha=0.3, hatch=hatchs[i//2%2])
                else:
                    ax.fill_between(E, Pmin, Pmax, color='k', alpha=0.2)
                    ax.fill_between(E, Pmax, np.ones(len(E))*np.max(Pmax), color=colors[i%7], alpha=0.3, hatch=hatchs[i//2%2])

        # custom legends
        if self.labels!=None:
            from matplotlib.lines import Line2D
            m = 's' if add_P0 is not None else ''
            custom_lines = [Line2D([0], [0], marker=m, mfc='none', mew=1.4, linestyle='-', color=colors[i%7], lw=2) for i in range(len(self.Dataset.keys()))]
            leg=ax.legend(custom_lines, self.Dataset.keys(), frameon=False, labelspacing=0.25, 
                          loc='center left', bbox_to_anchor=(1, 0.5), prop={'size':18, 'weight': fw})

        # zero-axes
        plt.axhline(y=0, color='k', linestyle='-', lw=0.5, zorder=0.5)
        plt.axvline(x=0, color='k', linestyle='-', lw=0.5, zorder=0.5)
        
        plt.tight_layout()
        if fig_out!=None:
            fig.savefig(fig_out, dpi=fig.dpi)
        else:
            plt.show()
        plt.close('all')
