import os
import pytest
# user-defined modules
from analyze_feram import HL_Analyzer

test_dir = 'tests/'
f_td = lambda x: os.path.join(test_dir, x)

@pytest.mark.parametrize('inputs, labels, repeat, outputs',
                         [([f_td('example.hl')], None, 1, [0]),
                          ([f_td('example.hl')], [1], 1, [1]),
                          ([f_td('example.hl')]*2, None, 1, [0]*2),
                          ([f_td('example.hl')]*2, [1,2], 1, [1,2]),
                          ([f_td('example.hl')]*4, None, 1, [0]*4),
                          ([f_td('example.hl')]*4, [1,2,3,4], 1, [1,2,3,4]),
                          ([f_td('example.hl')]*4, [1,2], 2, [1,1,2,2]),
                          ([f_td('example.hl')]*6, [1,2], 3, [1,1,1,2,2,2]),
                         ])
def test_load_data_labels(inputs, labels, repeat, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=labels, repeat=repeat)
    assert HA.labels==outputs

@pytest.mark.parametrize('inputs, labels, xaxis, yaxis, outputs',
                         [([f_td('example_1.hl')], None, 'Ex', 'ux', {0:[[0,0,0,0,1,2,1,0,0]]}),
                          ([f_td('example_1.hl'),f_td('example_2.hl')], None, 'Ex', 'ux', {0:[[0,0,0,0,1,2,1,0,0],[0,0,1,2,1,0,0,0,0]]}),
                          ([f_td('example_1.hl'),f_td('example_2.hl')], [0,1], 'Ex', 'ux', {0:[[0,0,0,0,1,2,1,0,0]], 1:[[0,0,1,2,1,0,0,0,0]]}),
                         ])
def test_cal_derivative(inputs, labels, xaxis, yaxis, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=labels, repeat=1)
    D,_ = HA.cal_derivative(xaxis=xaxis, yaxis=yaxis, display=False)
    assert (D[_]==outputs[_] for _ in D)

@pytest.mark.parametrize('inputs, labels, outputs',
                         [([f_td('example_1.hl'), f_td('example_2.hl')], None, {0: [-1.0,1.0]}),
                          ([f_td('example_1.hl'), f_td('example_2.hl')], [0,1], {0: [1.0], 1: [-1.0]}),
                         ])
def test_find_Ec(inputs, labels, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=labels, repeat=1)
    Ec = HA.find_Ec(E_scale=1, P_scale=1)
    assert Ec==outputs

@pytest.mark.parametrize('inputs, labels, outputs',
                         [([f_td('example_1.hl'), f_td('example_2.hl')], None, {0: [0]}),
                          ([f_td('example_1.hl'), f_td('example_2.hl')], [0,1], {0: [1.0], 1: [-1.0]}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], None, {0: [1]}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], [0,1], {0: [2.0], 1: [0.0]}),
                         ])
def test_find_Ebias(inputs, labels, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=labels, repeat=1)
    Ebias = HA.find_Ebias(E_scale=1, P_scale=1)
    assert Ebias==outputs

@pytest.mark.parametrize('inputs, labels, outputs',
                         [([f_td('example_1.hl'), f_td('example_2.hl')], None, {0: [-2.0, 2.0]}),
                          ([f_td('example_1.hl'), f_td('example_2.hl')], [0,1], {0: [0.0], 1: [0.0]}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], None, {0: [-2.0, 2.0]}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], [0,1], {0: [0.0], 1: [0.0]}),
                         ])
def test_find_Pr(inputs, labels, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=labels, repeat=1)
    Ebias = HA.find_Ebias(E_scale=1, P_scale=1)
    for key in Ebias:
        Pr = HA.find_Pr(E_axis=0, P_axis=0, Ebias=Ebias[key], E_scale=1, P_scale=1)
        assert Pr[key]==outputs[key]

@pytest.mark.parametrize('inputs, eref, outputs',
                         [([f_td('example_1.hl'), f_td('example_2.hl')], 0, {0.0: -2.0, 1.0: 0.0, 2.0: 2.0, 3.0: 2.0, 4.0: 2.0}),
                          ([f_td('example_1.hl'), f_td('example_2.hl')], -2, {-2.0: -2.0, -1.0: -2.0, 0.0: -2.0, 1.0: 0.0, 2.0: 2.0, 3.0: 2.0, 4.0: 2.0}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], 0, {0.0: -2.0, 1.0: -2.0, 2.0: 0.0, 3.0: 2.0, 4.0: 2.0}),
                         ])
def test_cal_PE_integral_EPm(inputs, eref, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=None, repeat=1)
    res,EPm = HA.cal_PE_integral(E_axis=0, P_axis=0, E_scale=1, P_scale=1, E_ref=eref, reverse=False, display=False)
    assert EPm[0][0]==outputs

@pytest.mark.parametrize('inputs, labels, rev, outputs',
                         [([f_td('example.hl')], None, 0, {0: (12.0, 0)}),
                          ([f_td('example_1.hl'), f_td('example_2.hl')], None, 0, {0: (0.0, 4.0)}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], None, 0, {0: (1.0, 7.0)}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], [0,1], 0, {0: (8.0, 0.0), 1: (1.0, 0.0)}),
                          ([f_td('example_3.hl'), f_td('example_4.hl')], None, 1, {0: (0.0, 1.0)}),
                         ])
def test_cal_PE_integral_res(inputs, labels, rev, outputs):
    HA = HL_Analyzer(*inputs)
    HA.load_data(labels=labels, repeat=1)
    res,EPm = HA.cal_PE_integral(E_axis=0, P_axis=0, E_scale=1, P_scale=1, E_ref=0, reverse=rev, display=False)
    assert res==outputs
