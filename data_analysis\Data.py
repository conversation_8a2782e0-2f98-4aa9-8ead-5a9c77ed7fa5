import os, glob

# Data
fdata = {}
storage = '/temp_storage/j1-DW_defects/sim_data/'
for temp in [150, 200, 240, 260, 280, 290]:
    fdata[temp] = {}

case = '1-T180DW/pristine/'
for temp in [150, 200, 240, 260, 280, 290]: # K
    for field in [0, 75, 100, 150]:
        path = case+f'{temp}K/'
        for i in range(10):
            fdata[temp][f'pt_f{field}_{i}'] = os.path.join(storage, path, f'dir_ana_pt_r0.00_s0_{field:d}kVocm/logfile_ramp_{i}/')

case = '1-T180DW/1-one_site/'
temp = 260 # K
field = 100 # kV/cm

path = case+f'{temp}K/'
for size in [2, 4, 8, 12, 16]:
    for i in range(5):
        fdata[temp][f'0D_a{size}_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_a{size:d}_d0_{field:d}kVocm/logfile_ramp_{i}/')

case = '1-T180DW/4-layers/'
temp = 260 # K
field = 100 # kV/cm

path = case+f'1-1L_1_2_4_8_10%/{temp}K/'
for ratio in [1, 2, 4, 8, 10]:
    for i in range(5):
        fdata[temp][f'1L_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
        fdata[temp][f'1L_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

path = case+f'2-1L_2.5_3.0_3.5%/{temp}K/'
for ratio in [2.5, 3, 3.5]:
    for i in range(5):
        fdata[temp][f'1L_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
        fdata[temp][f'1L_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

# half-L
path = case+f'3-hLz_1_2_4_6_8%/{temp}K/'
for ratio in [1, 2, 4, 6, 8]:
    for i in range(5):
        fdata[temp][f'hLz_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
        fdata[temp][f'hLz_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

path = case+f'4-hLy_1_2_4_6_8%/{temp}K/'
for ratio in [1, 2, 4, 6, 8]:
    for i in range(5):
        fdata[temp][f'hLy_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
        fdata[temp][f'hLy_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

# T=150, 200, 290K
for temp in [150, 200, 240, 280, 290]:
    path = case+f'5-1L_1_2_2.5_3_3.5_4/{temp}K'
    for ratio in [1, 2, 2.5, 3, 3.5, 4]:
        for i in range(5):
            fdata[temp][f'1L_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
            fdata[temp][f'1L_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

# 5,6,7% (T=240,260,280K)
for temp in [240, 260, 280]:
    path = case+f'7-1L_5_6_7%/{temp}K'
    for ratio in [5, 6, 7]:
        for i in range(5):
            fdata[temp][f'1L_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
            fdata[temp][f'1L_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

# slit_z
for temp in [260]:
    path = case+f'8-1L_slit_zd1_2_3_4_5/{temp}K'
    for dz in [3,4]:
        for i in range(10):
            fdata[temp][f'1L_sl_dz{dz}_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_d{dz}_{field:d}kVocm/logfile_ramp_{i}/')
# slit_y
ratio = 3.5
for temp in [260]:
    path = case+f'9-1L_slit_yd38_39_40_41/{temp}K'
    for dy in [38,39,40,41]:
        for i in range(10):
            fdata[temp][f'1L_sl_dy{dy}_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_d{dy}_{field:d}kVocm/logfile_ramp_{i}/')

# field removal
case = '1-T180DW/4-layers/'
temp = 260 # K
field = 0 # kV/cm
for t in [90, 100]:
    path = case+f's1-1L_3.5%_nofield/{temp}K_{t}ps/'
    for ratio in [3.5,]:
        for i in range(1):
            fdata[temp][f'1L_{ratio}%_s0_{i}_f0_{t}ps'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
#
case = '1-T180DW/3-rand3d/'
temp = 260 # K
field = 0 # kV/cm
path = case+f's1-1%_nofield/{temp}K/'
for ratio in [1,]:
    for i in range(1):
        fdata[temp][f'3D_{ratio}%_s0_{i}_f0_100ps'] = os.path.join(storage, path, f'dir_ana_dd0.09_{ratio:.1f}pt_s0_{field:d}kVocm/logfile_ramp_{i}/')



case = '4-T90DW/4-layers/'
temp = 260 # K
field = 10 # kV/cm
# 1L
path = case+f'1-1L_0.5_1_2_3_4/{temp}K/'
for ratio in [0.5, 1, 2, 3, 4]:
    for i in range(5):
        fdata[temp][f'T901L_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s0_{field:d}kVocm/logfile_ramp_{i}/')
        fdata[temp][f'T901L_{ratio}%_s1_{i}'] = os.path.join(storage, path, f'dir_ana_dd-0.09_r{ratio:.2f}_s1_{field:d}kVocm/logfile_ramp_{i}/')

case = '1-T180DW/3-rand3d/'
field = 100 # kV/cm
# rand.3D
for temp in [240, 260, 280]: # K
    path = case+f'1-PuNd_0.5_1_2%/{temp}K/'
    for ratio in [0.5, 1, 2]:
        for i in range(10):
            fdata[temp][f'3D_{ratio}%_s0_{i}'] = os.path.join(storage, path, f'dir_ana_dd0.09_{ratio:.1f}pt_s0_{field:d}kVocm/logfile_ramp_{i}/')
## dd: 0.11, 0.15
for temp in [240, 260, 280]: # K
    path = case+f'3-PuNd_1%_dd/{temp}K/'
    for ratio in [1]:
        for i in range(10):
            fdata[temp][f'3D_{ratio}%_s3_{i}'] = os.path.join(storage, path, f'dir_ana_dd0.11_{ratio:.1f}pt_s0_{field:d}kVocm/logfile_ramp_{i}/')
            fdata[temp][f'3D_{ratio}%_s4_{i}'] = os.path.join(storage, path, f'dir_ana_dd0.15_{ratio:.1f}pt_s0_{field:d}kVocm/logfile_ramp_{i}/')
