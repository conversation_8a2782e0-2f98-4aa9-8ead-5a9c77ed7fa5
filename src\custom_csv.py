import os, sys, re, glob
import numpy as np
import pandas as pd
import matplotlib as mpl
import matplotlib.pyplot as plt
#mpl.use('Agg')
from analyze_feram import Dipo_Analysis

class DWCSV_Helper():
    def __init__(self):
        self.dt = 1
        self.solid = 'bto'
        self.norm = 0
        self.ui = 0
        self.divs = [1]
        self.kernel = None
        self.key = None
        self.x0 = None
        self.v0 = None
        self.f_defects = None
        self.disp_r = False
        self.disp_v = False
        self.DWL_SUM = None
        self.DWR_SUM = None

    def set_sim_params(self, dt=1, solid='bto'):
        self.dt = dt
        self.solid = solid

    def set_dw_params(self, norm=0, ui=0, divs=[1], kernel=None, key=None, x0=None, v0=0):
        self.norm = norm
        self.ui = ui
        self.divs = divs
        self.kernel = kernel
        self.key = key
        self.x0 = x0
        self.v0 = v0

    def set_defect_params(self, f_defects=None):
        self.f_defects = f_defects

    def set_disp(self, disp_r=False, disp_v=False):
        self.disp_r = disp_r
        self.disp_v = disp_v

    def initialize_dw_sum(self, DWL_SUM=None, DWR_SUM=None):
        self.DWL_SUM = DWL_SUM if DWL_SUM!=None else {}
        self.DWR_SUM = DWR_SUM if DWR_SUM!=None else {}

    def analyze_dw(self, Cls, Dict, tag='system:LR:temp', if_print=False):
        dt, solid = self.dt, self.solid
        norm, ui, divs, kernel, key = self.norm, self.ui, self.divs, self.kernel, self.key
        x0, v0 = self.x0, self.v0
        f_defects = self.f_defects
        disp_r, disp_v = self.disp_r, self.disp_v
        DW = Cls.find_DW_surf(norm=norm, ui=ui, kernel=kernel, key=key, display=False)
        for div in divs:
            rgh = Cls.cal_DW_roughness(norm=norm, div=div, show_sub=True, display=disp_r)
            if os.path.exists(f_defects):
                Xd = dft = Cls.alloc_defects_2d(f_defects, norm=norm, size=Cls.Size[0], div=div, is_dump=True)
            else:
                Xd = None
                dft = {_: {x: [] for x in 'xyz'} for _ in rgh}
            vdw = Cls.cal_DW_velocity(norm=norm, div=div, dt=dt, solid=solid, x0=x0, v0=v0, Xd=Xd, show_pos=True, display=disp_v)
            for ind in vdw:
                Dict[f'{tag}:{div}:{ind}'] = [dft[ind], vdw[ind][0], vdw[ind][1], rgh[ind],]
                system, LR, temp = tag.split(':')
                if if_print:
                    print(f'{system}; {temp}; {LR}; {div}; {ind}; {dft[ind]}; {vdw[ind][0]}; {vdw[ind][1]}; {rgh[ind]}')
        return Dict

    def run_dw_summary(self, f_dump, temp, system, if_print=False):
        if self.DWL_SUM!=None and self.DWR_SUM!=None:
            DWL_SUM = self.DWL_SUM # left DW
            DWR_SUM = self.DWR_SUM # right DW
        # load file
        DA = Dipo_Analysis(f_dump)
        DA.load_data(is_dump=True, iframe=':')
        # analyze DW_L
        tag = f'{system}:{temp}:L'; print(tag)
        DA.select_data(i_list=list(range(82)))
        DWL_SUM = self.analyze_dw(DA, DWL_SUM, tag=tag, if_print=if_print)
        # analyze DW_R
        tag = f'{system}:{temp}:R'; print(tag)
        DA.unselect_data()
        DA.select_data(i_list=list(range(82,164)))
        DWR_SUM = self.analyze_dw(DA, DWR_SUM, tag=tag, if_print=if_print)
        # write summary
        self.DWL_SUM = DWL_SUM
        self.DWR_SUM = DWR_SUM

    def output(self, to_csv=False, f_out=None, if_replace=True):
        DWL_SUM = self.DWL_SUM
        DWR_SUM = self.DWR_SUM
        headers = ['system', 'temp', 'lr', 'dv', 'ind', 'defects', 'xdw', 'vdw', 'rgh']
        if to_csv:
            # write csv
            k_z, k_y, k_x = np.shape(self.kernel)
            csv_out = f'DW_summary_{k_x}x{k_y}x{k_z}.csv' if f_out is None else f_out
            tr = 'w' if if_replace else 'a+'
            with open(csv_out, tr) as fw:
                if if_replace:
                    fw.write('; '.join(headers)+'\n')
                for tag in DWL_SUM:
                    line = tag.split(':')+[str(_) for _ in DWL_SUM[tag]]
                    fw.write('; '.join(line)+'\n')
                    sys.stdout.flush()
                for tag in DWR_SUM:
                    line = tag.split(':')+[str(_) for _ in DWR_SUM[tag]]
                    fw.write('; '.join(line)+'\n')
                    sys.stdout.flush()
        else:
            # return DataFrame
            SUM = {_: [] for _ in headers}
            for tag in DWL_SUM:
                line = tag.split(':')+[str(_) for _ in DWL_SUM[tag]]
                for k,v in zip(headers,line):
                    SUM[k].append(v)
            for tag in DWR_SUM:
                line = tag.split(':')+[str(_) for _ in DWR_SUM[tag]]
                for k,v in zip(headers,line):
                    SUM[k].append(v)
            df = pd.DataFrame(data = SUM)
            df = df[headers].astype({'temp': 'int64', 'dv': 'int64'})
            return df



class Plot_DWSUM():
    def __init__(self, fcsv):
        self.fcsv = fcsv
        self.df = None
        self.system = None
        self.temp = 0
        self.lr = 'L'
        self.div = 1

    def load_df(self, df=None):
        if df is None:
            df = pd.read_csv(self.fcsv, sep=';', header=0, skipinitialspace=True)
        self.df = df

    def load_system(self, conc=0, config_id=0, sim_ids=range(10), field=None, prefix=None):
        field = 100 if field is None else field
        def _f_sys(conc, s, prefix):
            if conc==0:
                pfx = 'pt'
                return [pfx+f'_f{field}_{i}' for i in sim_ids]
            elif conc>0:
                pfx = '1L' if prefix is None else prefix
                return [pfx+f'_{conc}%_s{s}_{i}' for i in sim_ids]
        if self.system is None:
            self.system = _f_sys(conc, config_id, prefix)
        else:
            self.system += _f_sys(conc, config_id, prefix)

    def unload_system(self):
        self.system = None

    def query(self, item='xdw', temp=260, lr='L', dv=1):
        if self.df is None:
            print('[Error]: No df loaded!')
            return
        elif self.system is None:
            print('[Error]: No system loaded!')
            return
        df = self.df
        res = []
        for s in self.system:
            val = df[(df.system==s) & (df.lr==lr) & (df.temp==temp) & (df.dv==dv)].iloc[0][item]
            res.append(eval(val))
        return np.array(res)

    def set_temp(self, temp=0):
        self.temp = temp

    def set_lr(self, lr='L'):
        self.lr = lr

    def set_div(self, div=1):
        self.div = div

    def load_item(self, item='x', groupby=None):
        """ Loading data by group(s)

        Parameters
        ----------
        item: str
            'x': position, 'v': velocity, 'r': roughness
        groupby: str
            [None]: no group, 'r': row, 'c': column

        Returns
        -------
        df: pd.Dataframe
            dataframe based on conditions
        """
        if self.df is None:
            print('[Error]: No Dataframe has been loaded! Please run `load_df` first.')
            return
        df = self.df
        temp = self.temp
        lr = self.lr
        div = self.div
        IGB = {'r': 0, 'c': 1}
        # defects from system[0]
        D = {}
        sdf = df[(df.system==self.system[0]) & (df.temp==temp) & (df.lr==lr) & (df.dv==div)]
        for _df in sdf.iloc:
            ind = eval(_df.ind)
            ig = ind[IGB[groupby]] if groupby is not None else ind
            dft = eval(_df.defects)
            n_dft = len(dft['y'])
            if ig not in D:
                D[ig] = 0
            D[ig] += n_dft if lr=='L' else 0
        V = {}
        for s in self.system:
            sdf = df[(df.system==s) & (df.temp==temp) & (df.lr==lr) & (df.dv==div)]
            for _df in sdf.iloc:
                ind = eval(_df.ind)
                ig = ind[IGB[groupby]] if groupby is not None else ind
                if item=='x':
                    val = eval(_df.xdw)
                elif item=='v':
                    val = eval(_df.vdw)
                elif item=='r':
                    val = eval(_df.rgh)
                if ig not in V:
                    V[ig] = []
                V[ig].append(val)
        return V, D

    def plot_pt_field(self, temp=None, fields=None, labels=None,
                      item='x', x0=None, xd=None, groupby='r', show_all=False, 
                      ylim=None, figsize=(6,6), fig_out=None, if_sfc=False):
        fig, ax = plt.subplots(figsize=figsize, dpi=150)
        for j,lr in enumerate(self.lr):
            for i,field in enumerate(fields):
                self.unload_system()
                self.load_system(conc=0, config_id=0, sim_ids=range(10), field=field, prefix=None)
                self.set_temp(temp = temp)
                _X, _ = self.load_item(item='x', groupby=groupby)
                X, D = self.load_item(item=item, groupby=groupby)
                for ir in X:
                    _xmean = np.mean(_X[ir], axis=0)
                    imax = None
                    if len(np.where(_xmean>70)[0])>0:
                        imax = np.where(_xmean>70)[0][0] if lr=='L' else None
                    xmean = np.mean(X[ir], axis=0)[:imax]
                    xstd = np.std(X[ir], axis=0)[:imax]
                    if x0 is not None:
                        xmean = np.concatenate([[x0], xmean])
                        xstd = np.concatenate([[0], xstd])
                    xx0 = 0
                    xind = np.arange(len(xmean))
                    ylo = list(xmean-xx0-xstd)
                    yhi = list(xmean-xx0+xstd)
                    ax.fill_between(xind*10, ylo, yhi, color='rgbmyck'[i%7], alpha=0.2)
                    if show_all:
                        for _i,_ in enumerate(X[ir]):
                            ax.plot(xind*10, np.array(_[:imax])-xx0, '.-', color='rgbmyck'[i%7])
                            ax.text(xind[-1]*10, _[:imax][-1]-xx0, _i, color='k', fontsize=14)
                    else:
                        ax.plot(xind*10, xmean-xx0, 's', ls=['-','--'][j], color='rgbmyck'[i%7])
        # defect layer
        if item=='x' and xd is not None:
            xxd = xd
            ax.axhline(y=xxd, ls='--', color='k', lw=1)
        if item!='x':
            ax.axhline(y=0, ls='-', color='k', lw=1)
        ax.tick_params(which='major', labelsize=20, length=5, width=0.8)
        ax.set_xlim([0, 100])
        if ylim is not None:
            ax.set_ylim(ylim)
        # custom legend
        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], linestyle='-', color='rgbmyck'[i%7]) for i in range(len(fields))]
        if if_sfc:
            ax.legend(custom_lines, labels, loc=0,
                      prop={'size':18, 'weight': 'normal'}, frameon=False)
        else:
            ax.legend(custom_lines, labels, loc='center left', bbox_to_anchor=(1, 0.5), 
                      prop={'size':18, 'weight': 'normal'}, frameon=False)
        # labels
        if xd is None:
            YLAB = {'x': r'$X_{\rm DW}$ (u.c.)', 'v': r'$v_{\rm DW}$ ($\AA/ps$)', 'r': r'$R_{\rm DW}$ (u.c.)'}
        else:
            YLAB = {'x': r'$\Delta X_{\rm DW}$ (u.c.)', 'v': r'$v_{\rm DW}$ ($\AA/ps$)', 'r': r'$R_{\rm DW}$ (u.c.)'}
        ax.set_xlabel(r't (ps)', fontsize=20)
        ax.set_ylabel(YLAB[item], fontsize=20)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_item_t(self, temperatures=None, labels=None, item='x', x0=None, xd=None, groupby='r',
                    ith=None, show_all=False, if_sfc=False, if_trunc=True, ylim=None, figsize=(6,6), fig_out=None):
        dt = 10 # ps
        fig, ax = plt.subplots(figsize=figsize, dpi=150)
        for j,lr in enumerate(self.lr):
            for i,temp in enumerate(temperatures):
                self.set_temp(temp = temp)
                _X, _ = self.load_item(item='x', groupby=groupby)
                X, D = self.load_item(item=item, groupby=groupby)
                for ir in X:
                    _xmean = np.mean(_X[ir], axis=0)
                    if ith is None:
                        ind_th = np.where(np.abs(_xmean-82)<=5.5)[0]
                        ith = ind_th[0] if len(ind_th)>0 else None
                    xmean = np.mean(X[ir], axis=0)
                    xstd = np.std(X[ir], axis=0)
                    if x0 is not None:
                        xmean = np.concatenate([[x0], xmean])
                        xstd = np.concatenate([[0], xstd])
                    xx0 = 0 #xd if item=='x'and xd is not None else 0
                    xind = np.arange(len(xmean))*dt
                    ylo = list(xmean-xx0-xstd)
                    yhi = list(xmean-xx0+xstd)
                    color = 'rgbmyck'[i%7]
                    ax.fill_between(xind[:ith], ylo[:ith], yhi[:ith], color=color, alpha=0.2)
                    if show_all:
                        for _i,_ in enumerate(X[ir]):
                            ax.plot(xind, np.array(_)-xx0, '.-', color=color)
                            ax.text(xind[-1], _[-1]-xx0, _i, color='k', fontsize=14)
                    else:
                        ax.plot(xind[:ith], xmean[:ith]-xx0, 's', ls='-', color=color)
                        if ith is not None and if_sfc:
                            ax.plot(xind[:ith][-1], xmean[:ith][-1]-xx0, 's', ls='-', color=color, mfc='w', mew=2)
                        if ith is not None and not if_trunc:
                            ax.plot(xind[ith-1:], xmean[ith-1:]-xx0, '.', ls=':', color=color)
        # defect layer
        if item=='x' and xd is not None:
            xxd = xd
            ax.axhline(y=xxd, ls='--', color='k', lw=1)
        if item!='x':
            ax.axhline(y=0, ls='-', color='k', lw=1)
        ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
        ax.set_xlim([0, 100])
        if ylim is not None:
            ax.set_ylim(ylim)
        # custom legend
        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], linestyle='-', color='rgbmyck'[i%7]) for i in range(len(temperatures))]
        if if_sfc:
            ax.legend(custom_lines, labels, loc=0,
                      prop={'size':18, 'weight': 'normal'}, frameon=False)
        else:
            ax.legend(custom_lines, labels, loc='center left', bbox_to_anchor=(1, 0.5), 
                      prop={'size':18, 'weight': 'normal'}, frameon=False)
        # labels
        YLAB = {'x': r'$X_{\rm DW}$ (u.c.)', 'v': r'$v_{\rm DW}$ ($\AA/ps$)', 'r': r'$R_{\rm DW}$ (u.c.)'}
        ax.set_xlabel(r't (ps)', fontsize=20)
        ax.set_ylabel(YLAB[item], fontsize=20)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_items_t(self, temperatures=None, labels=None, xd=None, groupby='r', 
                     tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
                     rlim=(0,4), show_all=False, if_sfc=False, if_trunc=True, figsize=(6,6), fig_out=None):
        # functions
        f_mean_std = lambda x: (np.mean(x, axis=0), np.std(x, axis=0))
        def plot_data(ax, X, ith=None, ixd=None, xd=None, color='k', xlab='t (ps)', ylab='',
                      dytick=1, lpos=0, xlim=(0,100), ylim=None, show_all=False):
            dt = 10 # ps
            for dv in X:
                xmean, xstd = f_mean_std(X[dv])
                if ith is None:
                    ind_th = np.where(np.abs(xmean-82)<=5.5)[0]
                    ith = ind_th[0] if len(ind_th)>0 else None
                xx0 = 0
                xind = np.arange(len(xmean))*dt
                ylo = list(xmean-xx0-xstd)
                yhi = list(xmean-xx0+xstd)
                # lines
                if not if_sfc:
                    ax.fill_between(xind[:ith], ylo[:ith], yhi[:ith], color=color, alpha=0.2)
                else:
                    ax.fill_between(xind[1:ith], ylo[1:ith], yhi[1:ith], color=color, alpha=0.2)
                if show_all:
                    for _i,_x in enumerate(X[dv]):
                        ax.plot(xind, np.array(_x)-xx0, '.-', color=color)
                        ax.text(xind[-1], _x[-1]-xx0, _i, color='k', fontsize=14)
                else:
                    if not if_sfc:
                        ax.plot(xind[:ith], xmean[:ith]-xx0, 's', ls='-', color=color)
                    else:
                        ax.plot(xind[0], xmean[0]-xx0, 's', ls='-', color=color)
                        ax.plot(xind[1:ith], xmean[1:ith]-xx0, 's', ls='-', color=color)
                    if ith is not None and if_sfc:
                        ax.plot(xind[:ith][-1], xmean[:ith][-1]-xx0, 's', ls='-', color=color, mfc='w', mew=2)
                    if ith is not None and not if_trunc:
                        ax.plot(xind[ith-1:], xmean[ith-1:]-xx0, '.', ls=':', color=color)
                # defect layer
                if xd is not None:
                    xxd = xd
                    ax.axhline(y=xxd, ls='--', color='k', lw=.8)
                if ixd is None and xd is not None:
                    if len(np.where(xmean>xd)[0])>0:
                        #ixd = np.interp(xd, xmean, xind)
                        ixd = xind[np.where(xmean>xd)[0][0]-1]
                    else:
                        ixd = None
                elif ixd is not None:
                    ax.axvline(x=ixd, ls='--', color=color, lw=1.1)
            ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
            ax.set_xlim(xlim)
            ax.set_ylim(ylim)
            ax.xaxis.set_major_locator(plt.MultipleLocator(20))
            ax.yaxis.set_major_locator(plt.MultipleLocator(dytick))
            ax.set_xlabel(rf'{xlab}', fontsize=20)
            if lpos==0:
                ax.set_ylabel(rf'{ylab}', fontsize=20)
            return ith, ixd

        # main
        LR = self.lr
        ncol = 2 if 'L' in LR and 'R' in LR else 1
        fig, ax = plt.subplots(3, ncol, sharex=True, figsize=figsize, dpi=100)
        ax = np.array(ax)[:,np.newaxis] if ncol==1 else ax
        for j,lr in enumerate(LR):
            for i,temp in enumerate(temperatures):
                self.set_temp(temp = temp)
                self.set_lr(lr = lr)
                xd = xd if lr=='L' else None
                X, D = self.load_item(item='x', groupby=groupby)
                V, _ = self.load_item(item='v', groupby=groupby)
                R, _ = self.load_item(item='r', groupby=groupby)
                color = 'rgbmyck'[i%7]
                ylab = r'$X_{\rm DW}$ (u.c.)'
                xlim = xlim_l if lr=='L' else xlim_r
                ith, ixd = plot_data(ax[0,j], X, ith=None, ixd=None, xd=xd, color=color, xlab='', ylab=ylab,
                                     dytick=10, lpos=j, xlim=tlim, ylim=xlim, show_all=show_all)
                ylab = r'$v_{\rm DW}$ ($\AA/ps$)'
                vlim = vlim_l if lr=='L' else vlim_r
                plot_data(ax[1,j], V, ith=ith, ixd=ixd, xd=None, color=color, xlab='', ylab=ylab,
                          dytick=1, lpos=j, xlim=tlim, ylim=vlim, show_all=show_all)
                ylab = r'$R_{\rm DW}$ (u.c.)'
                plot_data(ax[2,j], R, ith=ith, ixd=ixd, xd=None, color=color, xlab='t (ps)', ylab=ylab,
                          dytick=1, lpos=-1*j, xlim=tlim, ylim=rlim, show_all=show_all)
        # xticks
        if ncol==2:
            ax[2,0].tick_params(axis='x', labelrotation=90)
            ax[2,1].tick_params(axis='x', labelrotation=90)
            ax[0,1].invert_yaxis()
            ax[1,1].invert_yaxis()
        # custom legend
        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], linestyle='-', color='rgbmyck'[i%7]) for i in range(len(temperatures))]
        if if_sfc:
            _i = 0 if if_sfc is True else if_sfc
            for c in range(ncol):
                ax[_i,c].legend(custom_lines, labels, loc=0, borderpad=0, labelspacing=0.1, handletextpad=0.2,
                                prop={'size':18, 'weight': 'normal'}, frameon=False)
        else:
            ax[1,-1].legend(custom_lines, labels, loc='center left', bbox_to_anchor=(1, 0.5), 
                            prop={'size':18, 'weight': 'normal'}, frameon=False)
        # save figure
        plt.tight_layout(h_pad=0, w_pad=0.5)
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_x_t(self, temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,1,2,2.5,3,3.5,4,8], prefix=None, 
                 x0=None, xd=None, ylim=None, figsize=(6,6), fig_out=None):
        # check if 3D
        if_3d = False
        if prefix is not None:
            if_3d = True if prefix[:2]=='3D' else False
        # main
        fig, ax = plt.subplots(figsize=figsize, dpi=150)
        cmap = plt.cm.brg_r
        vmax = 0.05 if not if_3d else 0.02
        norm = mpl.colors.Normalize(vmin=0, vmax=vmax)
        lr = self.lr
        div = self.div
        area = div*(48/div)**2 if not if_3d else 164*48*48
        self.set_temp(temp = temp)
        TXT = {}
        for j,conc in enumerate(concs):
            TXT[conc] = [0, [], 'k']
            inds = config_ids if conc!=0 else [0]
            for k in inds:
                self.unload_system()
                self.load_system(conc=conc, config_id=k, sim_ids=sim_ids, prefix=prefix)
                X, D = self.load_item(item='x', groupby='r')
                for ir in X:
                    xmean = np.mean(X[ir], axis=0)
                    xstd = np.std(X[ir], axis=0)
                    xx0 = 0 #xd if xd is not None else 0
                    if x0 is not None:
                        xmean = np.concatenate([[x0], xmean])
                        xstd = np.concatenate([[0], xstd])
                    xind = np.arange(len(xmean))
                    ylo = xmean-xx0-xstd
                    yhi = xmean-xx0+xstd
                    ax.fill_between(xind*10, ylo, yhi, color=cmap(norm(D[ir]/area)), alpha=0.2)
                    ax.plot(xind*10, xmean-xx0, color=cmap(norm(D[ir]/area)), ls=['--', ':', '-.'][k%3], 
                            marker='.', label=f'{conc}:(y,{ir}):{D[ir]/area}')
                    if div==1:
                        TXT[conc][0] = xind[-1]*10*1.01
                        TXT[conc][1].append(xmean[-1]-xx0)
                        TXT[conc][2] = cmap(norm(D[ir]/area))
                        #ax.text(xind[-1]*10*1.01, xmean[-1]-xx0, f'{conc}%', color=cmap(norm(D[ir]/area)), fontweight='bold', fontsize=18)
        # fine-tune TXT position
        dy_crit = 2 # u.c.
        if div==1:
            ylocs = [np.mean(TXT[conc][1]) for conc in concs]
            ys = np.sort(ylocs)
            dy = ys - np.concatenate((ys[np.newaxis, 0],ys[:-1]))
            _dy = np.where((dy>0)*(dy<dy_crit), dy+dy_crit/2, dy)
            ys1 = ys[0] + np.cumsum(_dy)
            ylocs_n = np.zeros(len(ylocs))
            for _,_y in enumerate(ys1):
                ylocs_n[np.argsort(ylocs)[_]] = _y
            for j,conc in enumerate(concs):
                tx = TXT[conc][0]
                ty = ylocs_n[j]
                c = TXT[conc][2]
                ax.text(tx, ty-dy_crit/4, f'{conc}%', color=c, fontweight='bold', fontsize=18)
        # defect layer
        if xd is not None:
            xxd = xd
            ax.axhline(y=xxd, ls='--', color='k', lw=1)
        ax.set_xlim([0, 100])
        if ylim is not None:
            ax.set_ylim(ylim)
        ax.tick_params(which='major', labelsize=20, length=5, width=0.8)
        ax.set_xlabel(r't (ps)', fontsize=20)
        ylab = r'$X_{\rm DW}$ (u.c.)'
        ax.set_ylabel(ylab, fontsize=20)
        if div!=1:
            cbar = fig.colorbar(mpl.cm.ScalarMappable(norm=norm, cmap=cmap), ax=ax)
            cbar.ax.tick_params(labelsize=18)
            cbar.ax.get_yaxis().labelpad = 24
            cbar.ax.set_ylabel(r'Local density (u.c.$^{-2}$)', fontsize=20, rotation=270)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_v_t(self, temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,1,2,2.5,3,3.5,4,8], prefix=None, 
                 x0=None, ylim=None, figsize=(6,6), fig_out=None):
        # check if 3D
        if_3d = False
        if prefix is not None:
            if_3d = True if prefix[:2]=='3D' else False
        # main
        fig, ax = plt.subplots(figsize=figsize, dpi=150)
        cmap = plt.cm.brg_r
        vmax = 0.05 if not if_3d else 0.02
        norm = mpl.colors.Normalize(vmin=0, vmax=vmax)
        lr = self.lr
        div = self.div
        area = div*(48/div)**2 if not if_3d else 164*48*48
        self.set_temp(temp = temp)
        TXT = {}
        for j,conc in enumerate(concs):
            TXT[conc] = [0, [], 'k']
            inds = config_ids if conc!=0 else [0]
            for k in inds:
                self.unload_system()
                self.load_system(conc=conc, config_id=k, sim_ids=sim_ids, prefix=prefix)
                X, D = self.load_item(item='v', groupby='r')
                for ir in X:
                    xmean = np.mean(X[ir], axis=0)
                    xstd = np.std(X[ir], axis=0)
                    if x0 is not None:
                        xmean = np.concatenate([[x0], xmean])
                        xstd = np.concatenate([[0], xstd])
                    xind = np.arange(len(xmean))
                    ylo = xmean-xstd
                    yhi = xmean+xstd
                    ax.fill_between(xind*10, ylo, yhi, color=cmap(norm(D[ir]/area)), alpha=0.2)
                    ax.plot(xind*10, xmean, color=cmap(norm(D[ir]/area)), ls=['--', ':', '-.'][k%3], 
                            marker='.', label=f'{conc}:(y,{ir}):{D[ir]/area}')
                    if div==1:
                        TXT[conc][0] = xind[-1]*10*1.01
                        TXT[conc][1].append(xmean[-1])
                        TXT[conc][2] = cmap(norm(D[ir]/area))
                        #ax.text(xind[-1]*10*1.01, xmean[-1], f'{conc}%', color=cmap(norm(D[ir]/area)), fontweight='bold', fontsize=18)
        # fine-tune TXT position
        dy_crit = 0.125 # A/ps
        if div==1:
            ylocs = [np.mean(TXT[conc][1]) for conc in concs]
            ys = np.sort(ylocs)
            dy = ys - np.concatenate((ys[np.newaxis, 0],ys[:-1]))
            _dy = np.where((dy>0)*(dy<dy_crit), dy+dy_crit/2, dy)
            ys1 = ys[0] + np.cumsum(_dy)
            ylocs_n = np.zeros(len(ylocs))
            for _,_y in enumerate(ys1):
                ylocs_n[np.argsort(ylocs)[_]] = _y
            for j,conc in enumerate(concs):
                tx = TXT[conc][0]
                ty = ylocs_n[j]
                c = TXT[conc][2]
                ax.text(tx, ty-dy_crit/4, f'{conc}%', color=c, fontweight='bold', fontsize=18)
        # y=0
        ax.axhline(y=0, ls='-', color='k', lw=1)
        ax.set_xlim([0, 100])
        if ylim is not None:
            ax.set_ylim(ylim)
        ax.tick_params(which='major', labelsize=20, length=5, width=0.8)
        ax.set_xlabel(r't (ps)', fontsize=20)
        ylab = r'$v_{\rm DW}$ ($\AA/ps$)'
        ax.set_ylabel(ylab, fontsize=20)
        if div!=1:
            cbar = fig.colorbar(mpl.cm.ScalarMappable(norm=norm, cmap=cmap), ax=ax)
            cbar.ax.tick_params(labelsize=18)
            cbar.ax.get_yaxis().labelpad = 24
            cbar.ax.set_ylabel(r'Local density (u.c.$^{-2}$)', fontsize=20, rotation=270)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_v_ovt(self, temp=260, labels=None, xd=None, groupby='r', 
                   tlim=(0,100), vlim=(0,2), show_all=False, if_sfc=False, if_trunc=True, figsize=(6,6), fig_out=None):
        # functions
        f_mean_std = lambda x: (np.mean(x, axis=0), np.std(x, axis=0))
        def plot_data(ax, X, ith=None, ixd=None, xd=None, color='k', xlab='t (ps)', ylab='',
                      dytick=1, lpos=0, xlim=(0,100), ylim=None, show_all=False):
            dt = 10 # ps
            for dv in X:
                xmean, xstd = f_mean_std(X[dv])
                xmean, xstd = np.abs(xmean), np.abs(xstd)
                if ith is None:
                    ind_th = np.where(np.abs(xmean-82)<=5.5)[0]
                    ith = ind_th[0] if len(ind_th)>0 else None
                xx0 = 0
                xind = np.arange(len(xmean))*dt
                ylo = list(xmean-xx0-xstd)
                yhi = list(xmean-xx0+xstd)
                if ax is not None:
                    # lines
                    if not if_sfc:
                        ax.fill_between(xind[:ith], ylo[:ith], yhi[:ith], color=color, alpha=0.2)
                    else:
                        ax.fill_between(xind[1:ith], ylo[1:ith], yhi[1:ith], color=color, alpha=0.2)
                    if show_all:
                        for _i,_x in enumerate(X[dv]):
                            ax.plot(xind, np.array(_x)-xx0, '.-', color=color)
                            ax.text(xind[-1], _x[-1]-xx0, _i, color='k', fontsize=14)
                    else:
                        if not if_sfc:
                            ax.plot(xind[:ith], xmean[:ith]-xx0, 's', ls='-', color=color)
                        else:
                            ax.plot(xind[0], xmean[0]-xx0, 's', ls='-', color=color)
                            ax.plot(xind[1:ith], xmean[1:ith]-xx0, 's', ls='-', color=color)
                        if ith is not None and if_sfc:
                            ax.plot(xind[:ith][-1], xmean[:ith][-1]-xx0, 's', ls='-', color=color, mfc='w', mew=2)
                        if ith is not None and not if_trunc:
                            ax.plot(xind[ith-1:], xmean[ith-1:]-xx0, '.', ls=':', color=color)
                    # defect layer
                    if xd is not None:
                        xxd = xd
                        ax.axhline(y=xxd, ls='--', color='k', lw=.8)
                    if ixd is None and xd is not None:
                        if len(np.where(xmean>xd)[0])>0:
                            #ixd = np.interp(xd, xmean, xind)
                            ixd = xind[np.where(xmean>xd)[0][0]-1]
                        else:
                            ixd = None
                    elif ixd is not None:
                        ax.axvline(x=ixd, ls='--', color=color, lw=1.1)
                else:
                    # defect layer
                    if xd is not None:
                        xxd = xd
                    if ixd is None and xd is not None:
                        if len(np.where(xmean>xd)[0])>0:
                            #ixd = np.interp(xd, xmean, xind)
                            ixd = xind[np.where(xmean>xd)[0][0]-1]
                        else:
                            ixd = None
            if ax is not None:
                ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
                ax.set_xlim(xlim)
                ax.set_ylim(ylim)
                ax.xaxis.set_major_locator(plt.MultipleLocator(20))
                ax.yaxis.set_major_locator(plt.MultipleLocator(dytick))
                ax.set_xlabel(rf'{xlab}', fontsize=20)
                if lpos==0:
                    ax.set_ylabel(rf'{ylab}', fontsize=20)
            return ith, ixd

        # main
        LR = self.lr
        ncol = 1
        fig, ax = plt.subplots(1, ncol, sharex=True, figsize=figsize, dpi=100)
        for j,lr in enumerate(LR):
            color = 'rb'[j]
            self.set_temp(temp = temp)
            self.set_lr(lr = lr)
            xd = xd if lr=='L' else None
            X, D = self.load_item(item='x', groupby=groupby)
            V, _ = self.load_item(item='v', groupby=groupby)
            R, _ = self.load_item(item='r', groupby=groupby)
            ylab = r'$X_{\rm DW}$ (u.c.)'
            xlim = None
            ith, ixd = plot_data(None, X, ith=None, ixd=None, xd=xd, color=color, xlab='', ylab=ylab,
                                 dytick=10, lpos=j, xlim=tlim, ylim=xlim, show_all=show_all)
            ylab = r'$|v_{\rm DW}|$ ($\AA/ps$)'
            vlim = vlim
            plot_data(ax, V, ith=ith, ixd=ixd, xd=None, color=color, xlab='t (ps)', ylab=ylab,
                      dytick=1, lpos=j, xlim=tlim, ylim=vlim, show_all=show_all)

        # custom legend
        from matplotlib.lines import Line2D
        custom_lines = [Line2D([0], [0], linestyle='-', color='rb'[i]) for i,_ in enumerate('LR')]
        if if_sfc:
            ax.legend(custom_lines, labels, loc=0, borderpad=0, labelspacing=0.1, handletextpad=0.2,
                      prop={'size':18, 'weight': 'normal'}, frameon=False)
        else:
            ax.legend(custom_lines, labels, loc='center left', bbox_to_anchor=(1, 0.5), 
                      prop={'size':18, 'weight': 'normal'}, frameon=False)
        # save figure
        plt.tight_layout(h_pad=0, w_pad=0.5)
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_rgh_t(self, temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,1,2,2.5,3,3.5,4,8], prefix=None, 
                   x0=None, ylim=None, figsize=(6,6), fig_out=None):
        # check if 3D
        if_3d = False
        if prefix is not None:
            if_3d = True if prefix[:2]=='3D' else False
        # main
        fig, ax = plt.subplots(figsize=figsize, dpi=150)
        cmap = plt.cm.brg_r
        vmax = 0.05 if not if_3d else 0.02
        norm = mpl.colors.Normalize(vmin=0, vmax=vmax)
        lr = self.lr
        div = self.div
        area = div*(48/div)**2 if not if_3d else 164*48*48
        self.set_temp(temp = temp)
        TXT = {}
        for j,conc in enumerate(concs):
            TXT[conc] = [0, [], 'k']
            inds = config_ids if conc!=0 else [0]
            for k in inds:
                self.unload_system()
                self.load_system(conc=conc, config_id=k, sim_ids=sim_ids, prefix=prefix)
                X, D = self.load_item(item='r', groupby='r')
                for ir in X:
                    xmean = np.mean(X[ir], axis=0)
                    xstd = np.std(X[ir], axis=0)
                    if x0 is not None:
                        xmean = np.concatenate([[x0], xmean])
                        xstd = np.concatenate([[0], xstd])
                    xind = np.arange(len(xmean))
                    ylo = xmean-xstd
                    yhi = xmean+xstd
                    ax.fill_between(xind*10, ylo, yhi, color=cmap(norm(D[ir]/area)), alpha=0.2)
                    ax.plot(xind*10, xmean, color=cmap(norm(D[ir]/area)), ls=['--', ':', '-.'][k%3], 
                            marker='.', label=f'{conc}:(y,{ir}):{D[ir]/area}')
                    if div==1:
                        TXT[conc][0] = xind[-1]*10*1.01
                        TXT[conc][1].append(xmean[-1])
                        TXT[conc][2] = cmap(norm(D[ir]/area))
                        #ax.text(xind[-1]*10*1.01, xmean[-1], f'{conc}%', color=cmap(norm(D[ir]/area)), fontweight='bold', fontsize=18)
        # fine-tune TXT position
        dy_crit = 0.45 # u.c.
        if div==1:
            ylocs = [np.mean(TXT[conc][1]) for conc in concs]
            ys = np.sort(ylocs)
            dy = ys - np.concatenate((ys[np.newaxis, 0],ys[:-1]))
            _dy = np.where((dy>0)*(dy<dy_crit), dy+dy_crit/2, dy)
            ys1 = ys[0] + np.cumsum(_dy)
            ylocs_n = np.zeros(len(ylocs))
            for _,_y in enumerate(ys1):
                ylocs_n[np.argsort(ylocs)[_]] = _y
            for j,conc in enumerate(concs):
                tx = TXT[conc][0]
                ty = ylocs_n[j]
                c = TXT[conc][2]
                ax.text(tx, ty-dy_crit/2, f'{conc}%', color=c, fontweight='bold', fontsize=18)
        # y=0
        ax.axhline(y=0, ls='-', color='k', lw=1)
        ax.set_xlim([0, 100])
        if ylim is not None:
            ax.set_ylim(ylim)
        ax.tick_params(which='major', labelsize=20, length=5, width=0.8)
        ax.set_xlabel(r't (ps)', fontsize=20)
        ylab = r'$R_{\rm DW}$ (u.c.)'
        ax.set_ylabel(ylab, fontsize=20)
        if div!=1:
            cbar = fig.colorbar(mpl.cm.ScalarMappable(norm=norm, cmap=cmap), ax=ax)
            cbar.ax.tick_params(labelsize=18)
            cbar.ax.get_yaxis().labelpad = 24
            cbar.ax.set_ylabel(r'Local density (u.c.$^{-2}$)', fontsize=20, rotation=270)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_concs_t(self, temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,1,2,2.5,3,3.5,4,8], prefix=None,
                     x0=None, xd=None, tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
                     rlim=(0,4), if_sfc=False, figsize=(6,6), fig_out=None):
        # plot-func.
        def plot_data(ax, item='x', ixd=None, xd=None, xlim=(0,100), ylim=None, dytick=10,
                      if_xlab=True, if_ylab=True):
            dt = 10 # ps
            TXT = {}
            IXD = {} if xd is not None else None
            for j,conc in enumerate(concs):
                TXT[conc] = [0, [], 'k']
                inds = config_ids if conc!=0 else [0]
                for k in inds:
                    self.unload_system()
                    self.load_system(conc=conc, config_id=k, sim_ids=sim_ids, prefix=prefix)
                    X, D = self.load_item(item=item, groupby='r')
                    for ir in X:
                        xmean = np.mean(X[ir], axis=0)
                        xstd = np.std(X[ir], axis=0)
                        xx0 = 0#xd if xd is not None else 0
                        if x0 is not None:
                            xmean = np.concatenate([[x0], xmean])
                            xstd = np.concatenate([[0], xstd])
                        xind = np.arange(len(xmean))*dt
                        ylo = xmean-xx0-xstd
                        yhi = xmean-xx0+xstd
                        if not if_sfc:
                            ax.fill_between(xind, ylo, yhi, color=cmap(norm(D[ir]/area)), alpha=0.2, hatch=['', '||', '--'][k%3])
                            ax.plot(xind, xmean-xx0, color=cmap(norm(D[ir]/area)), ls=['--', ':', '-.'][k%3], 
                                    marker='.', label=f'{conc}:(y,{ir}):{D[ir]/area}')
                        else:
                            ax.fill_between(xind[1:], ylo[1:], yhi[1:], color=cmap(norm(D[ir]/area)), alpha=0.2, hatch=['', '||', '--'][k%3])
                            ax.plot(xind[0], xmean[0]-xx0, color=cmap(norm(D[ir]/area)), ls=['--', ':', '-.'][k%3], 
                                    marker='.', label=f'{conc}:(y,{ir}):{D[ir]/area}')
                            ax.plot(xind[1:], xmean[1:]-xx0, color=cmap(norm(D[ir]/area)), ls=['--', ':', '-.'][k%3], 
                                    marker='.', label=f'{conc}:(y,{ir}):{D[ir]/area}')
                        if div==1:
                            TXT[conc][0] = xind[-1]*1.01
                            TXT[conc][1].append(xmean[-1]-xx0)
                            TXT[conc][2] = cmap(norm(D[ir]/area))
                        # defect layer
                        if ixd is None and xd is not None:
                            if len(np.where(xmean>xd)[0])>0:
                                #_ixd = np.interp(xd, xmean, xind)
                                _ixd = xind[np.where(xmean>xd)[0][0]-1]
                            else:
                                _ixd = None
                            IXD[conc] = _ixd
                        elif ixd is not None:
                            ax.axvline(x=ixd[conc], ls='--', color=cmap(norm(D[ir]/area)), lw=1.1)
            # fine-tune TXT position
            if item=='x' and div==1:
                dy_crit = 4.1 if item=='x' else 0.3
                ylocs = [np.mean(TXT[conc][1]) for conc in concs]
                ys = np.sort(ylocs)
                dy = ys - np.concatenate((ys[np.newaxis, 0],ys[:-1]))
                if np.any(dy[1:]<dy_crit):
                    ylocs_n = [ys[-1]-dy_crit*_ for _ in range(len(ys))]
                else:
                    ylocs_n = ylocs
                for j,conc in enumerate(concs):
                    tx = TXT[conc][0]
                    ty = ylocs_n[j]
                    c = TXT[conc][2]
                    ax.text(tx, ty-dy_crit/3, f'{conc}%', color=c, fontweight='bold', fontsize=18)
            # reference line
            if xd is not None:
                xxd = xd
                ax.axhline(y=xxd, ls='--', color='k', lw=1)
            if item in 'vr':
                ax.axhline(y=0, ls='-', color='k', lw=1)
            # x/y limit
            ax.set_xlim(xlim)
            if ylim is not None:
                ax.set_ylim(ylim)
            ax.xaxis.set_major_locator(plt.MultipleLocator(20))
            ax.yaxis.set_major_locator(plt.MultipleLocator(dytick))
            ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
            if if_xlab:
                ax.set_xlabel(r't (ps)', fontsize=20)
            if if_ylab:
                if item=='x':
                    ylab = r'$X_{\rm DW}$ (u.c.)'
                elif item=='v':
                    ylab = r'$v_{\rm DW}$ ($\AA/ps$)'
                elif item=='r':
                    ylab = r'$R_{\rm DW}$ (u.c.)'
                ax.set_ylabel(ylab, fontsize=20)
            return IXD

        # check if 3D
        if_3d = False
        if prefix is not None:
            if_3d = True if prefix[:2]=='3D' else False
        # figiure layout
        items = 'xvr' if rlim!=False else 'xv'
        LR = self.lr
        ncol = 2 if 'L' in LR and 'R' in LR else 1
        fig, ax = plt.subplots(len(items), ncol, sharex=True, figsize=figsize, dpi=100)
        ax = np.array(ax)[:,np.newaxis] if ncol==1 else ax
        # colormap
        cmap = plt.cm.brg_r
        vmax = 0.05 if not if_3d else 0.02
        norm = mpl.colors.Normalize(vmin=0, vmax=vmax)
        # main
        div = self.div
        area = div*(48/div)**2 if not if_3d else 164*48*48
        for j,lr in enumerate(LR):
            self.set_lr(lr=lr)
            self.set_temp(temp = temp)
            for i,item in enumerate(items):
                if_ylab = True if j==0 else False
                if_xlab = True if i==len(items)-1 else False
                if item=='x':
                    xxd = xd if lr=='L' else None
                    ylim = xlim_l if lr=='L' else xlim_r
                    IXD = plot_data(ax[i,j], item=item, ixd=None, xd=xxd, xlim=tlim, ylim=ylim, dytick=10,
                                    if_xlab=if_xlab, if_ylab=if_ylab)
                elif item=='v':
                    ylim = vlim_l if lr=='L' else vlim_r
                    plot_data(ax[i,j], item=item, ixd=IXD, xd=None, xlim=tlim, ylim=ylim, dytick=1,
                              if_xlab=if_xlab, if_ylab=if_ylab)
                elif item=='r':
                    plot_data(ax[i,j], item=item, ixd=IXD, xd=None, xlim=tlim, ylim=rlim, dytick=1,
                              if_xlab=if_xlab, if_ylab=if_ylab)
        # reset
        self.set_lr(lr=LR)
        # xticks
        if ncol>=2:
            ax[-1,0].tick_params(axis='x', labelrotation=90)
            ax[-1,1].tick_params(axis='x', labelrotation=90)
            ax[0,1].invert_yaxis()
            ax[1,1].invert_yaxis()
        plt.tight_layout(h_pad=0.5, w_pad=0.5)
        if div!=1:
            cbar = fig.colorbar(mpl.cm.ScalarMappable(norm=norm, cmap=cmap), ax=ax[:,-1], shrink=0.8)
            cbar.ax.tick_params(labelsize=18)
            cbar.ax.get_yaxis().labelpad = 24
            cbar.ax.set_ylabel(r'Local density (u.c.$^{-2}$)', fontsize=20, rotation=270)
        # save figure
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')

    def plot_confs_t(self, config_ids=[0,1], sim_ids=range(10), conc=1, prefix=None,
                     temperatures=None, labels=None, xd=None, groupby='r', 
                     tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
                     rlim=(0,4), show_all=False, if_sfc=False, if_trunc=True, figsize=(6,6), fig_out=None):
        # functions
        f_mean_std = lambda x: (np.mean(x, axis=0), np.std(x, axis=0))
        def plot_data(ax, X, ith=None, ixd=None, xd=None, marker='o', color='k', xlab='t (ps)', ylab='',
                      dytick=1, lpos=0, xlim=(0,100), ylim=None, show_all=False):
            dt = 10 # ps
            for dv in X:
                xmean, xstd = f_mean_std(X[dv])
                if ith is None:
                    ind_th = np.where(np.abs(xmean-82)<=5.5)[0]
                    ith = ind_th[0] if len(ind_th)>0 else None
                xx0 = 0
                xind = np.arange(len(xmean))*dt
                ylo = list(xmean-xx0-xstd)
                yhi = list(xmean-xx0+xstd)
                # lines
                if not if_sfc:
                    ax.fill_between(xind[:ith], ylo[:ith], yhi[:ith], color=color, alpha=0.2)
                else:
                    ax.fill_between(xind[1:ith], ylo[1:ith], yhi[1:ith], color=color, alpha=0.2)
                if show_all:
                    for _i,_x in enumerate(X[dv]):
                        ax.plot(xind, np.array(_x)-xx0, '.-', color=color)
                        ax.text(xind[-1], _x[-1]-xx0, _i, color='k', fontsize=14)
                else:
                    if not if_sfc:
                        ax.plot(xind[:ith], xmean[:ith]-xx0, marker=marker, ls='-', color=color)
                    else:
                        ax.plot(xind[0], xmean[0]-xx0, marker=marker, ls='-', color=color)
                        ax.plot(xind[1:ith], xmean[1:ith]-xx0, marker=marker, ls='-', color=color)
                    if ith is not None and if_sfc:
                        ax.plot(xind[:ith][-1], xmean[:ith][-1]-xx0, marker=marker, ls='-', color=color, mfc='w', mew=2)
                    if ith is not None and not if_trunc:
                        ax.plot(xind[ith-1:], xmean[ith-1:]-xx0, '.', ls=':', color=color)
                # defect layer
                if xd is not None:
                    xxd = xd
                    ax.axhline(y=xxd, ls='--', color='k', lw=.8)
                if ixd is None and xd is not None:
                    if len(np.where(xmean>xd)[0])>0:
                        #ixd = np.interp(xd, xmean, xind)
                        ixd = xind[np.where(xmean>xd)[0][0]-1]
                    else:
                        ixd = None
                elif ixd is not None:
                    ax.axvline(x=ixd, ls='--', color=color, lw=1.1)
            ax.tick_params(which='major', labelsize=18, length=5, width=0.8)
            ax.set_xlim(xlim)
            ax.set_ylim(ylim)
            ax.xaxis.set_major_locator(plt.MultipleLocator(20))
            ax.yaxis.set_major_locator(plt.MultipleLocator(dytick))
            ax.set_xlabel(rf'{xlab}', fontsize=20)
            if lpos==0:
                ax.set_ylabel(rf'{ylab}', fontsize=20)
            return ith, ixd

        # main
        LR = self.lr
        ncol = 2 if 'L' in LR and 'R' in LR else 1
        fig, ax = plt.subplots(3, ncol, sharex=True, figsize=figsize, dpi=100)
        ax = np.array(ax)[:,np.newaxis] if ncol==1 else ax
        for k,conf in enumerate(config_ids):
            self.unload_system()
            self.load_system(conc=conc, config_id=conf, sim_ids=sim_ids, prefix=prefix)
            for j,lr in enumerate(LR):
                for i,temp in enumerate(temperatures):
                    self.set_temp(temp = temp)
                    self.set_lr(lr = lr)
                    xd = xd if lr=='L' else None
                    X, D = self.load_item(item='x', groupby=groupby)
                    V, _ = self.load_item(item='v', groupby=groupby)
                    R, _ = self.load_item(item='r', groupby=groupby)
                    marker='o^s*PX'[i%6]
                    color='rgbmyck'[k%7]
                    ylab = r'$X_{\rm DW}$ (u.c.)'
                    xlim = xlim_l if lr=='L' else xlim_r
                    ith, ixd = plot_data(ax[0,j], X, ith=None, ixd=None, xd=xd, marker=marker, color=color, xlab='', ylab=ylab,
                                         dytick=10, lpos=j, xlim=tlim, ylim=xlim, show_all=show_all)
                    ylab = r'$v_{\rm DW}$ ($\AA/ps$)'
                    vlim = vlim_l if lr=='L' else vlim_r
                    plot_data(ax[1,j], V, ith=ith, ixd=ixd, xd=None, marker=marker, color=color, xlab='', ylab=ylab,
                              dytick=1, lpos=j, xlim=tlim, ylim=vlim, show_all=show_all)
                    ylab = r'$R_{\rm DW}$ (u.c.)'
                    plot_data(ax[2,j], R, ith=ith, ixd=ixd, xd=None, marker=marker, color=color, xlab='t (ps)', ylab=ylab,
                              dytick=1, lpos=-1*j, xlim=tlim, ylim=rlim, show_all=show_all)
        # xticks
        if ncol==2:
            ax[2,0].tick_params(axis='x', labelrotation=90)
            ax[2,1].tick_params(axis='x', labelrotation=90)
            ax[0,1].invert_yaxis()
            ax[1,1].invert_yaxis()
        # custom legend
        from matplotlib.lines import Line2D
        custom_lines = []
        for k in range(len(config_ids)):
            for i in range(len(temperatures)):
                marker='o^s*PX'[i%6]
                color='rgbmyck'[k%7]
                custom_lines.append( Line2D([0], [0], marker=marker, linestyle='-', color=color) )
        if if_sfc:
            _i = 0 if if_sfc is True else if_sfc
            for c in range(ncol):
                ax[_i,c].legend(custom_lines, labels, loc=0, borderpad=0, labelspacing=0.1, handletextpad=0.2,
                                prop={'size':18, 'weight': 'normal'}, frameon=False)
        else:
            fig.legend(custom_lines, labels, loc='center left', bbox_to_anchor=(1, 0.5),
                       prop={'size':18, 'weight': 'normal'}, frameon=False)
        # save figure
        plt.tight_layout(h_pad=0, w_pad=0.5)
        if fig_out!=None:
            plt.savefig(fig_out, dpi=fig.dpi, bbox_inches='tight')
        else:
            plt.show()
        plt.close('all')
