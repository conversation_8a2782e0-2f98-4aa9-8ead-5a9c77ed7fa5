#!/usr/bin/env python
# coding: utf-8
# author: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
# source: https://gitlab.ruhr-uni-bochum.de/icams-sfc/exploremultidomain

# python version: 3.8 or higher

'''
Define your domain seeds (variables called "Domain", imagine a seed where a specific domain starts to grow) and the electric field in xyz direction (variables called "Props") in the __main__ function in the domain_rectangle.py.
'''
import argparse
from typing import Any, List, Tuple, Dict, Iterator, Optional
from dataclasses import dataclass, astuple, field
from collections import Counter
import numpy as np


Vec3 = Tuple[int, int, int]


@dataclass(frozen=True)
class Props:
    x: float
    y: float
    z: float

    def __iter__(self) -> Iterator[Any]:
        return iter(astuple(self))

    def __repr__(self):
        return f"(Prop {self.x} {self.y} {self.z})"


@dataclass(frozen=True)
class Domain:
    seed: Vec3
    props: Props

    def __repr__(self):
        return f"(Domain {self.seed} {self.props})"


@dataclass
class Point:
    domain: Domain
    boundary: Optional[float] = None

    def __repr__(self):
        return f"(Point {self.domain} {self.boundary})"


@dataclass
class System:
    size: Vec3
    points: Dict[Vec3, Point]

    def __len__(self) -> int:
        return len(self.points)

    def __iter__(self) -> Iterator[Tuple[Vec3, Point]]:
        return iter(self.points.items())

    def __getitem__(self, coord: Vec3) -> Point:
        return self.points[coord]

    def find_neighbors(self, coord: Vec3, d: int) -> List[Tuple[Vec3, Point]]:
        """right now, it only works for d = 1"""

        """
        the points at the boundary of the system
        (e.g. (0,0,0), (SIZE,SIZE,SIZE)) are just not considered
        as neighbors of the point. Because of this the points at
        the system boundary will never be found/set as domain boundary.
        If it is intended to make these points as domain boundary,
        then we can just write these points to the .defects in the end.
        """
        """
        # if x==0 or y==0 or z==0 or x==size or y==size or z==size:
        #     return [[x+d,y,z],[x-d,y,z],[x,y+d,z],[x,y-d,z],[x,y,z+d],[x,y,z-d]]

        * think how to deal with point at the boundary of the system
        under periodic boundary condition : probably not needed
        """

        x, y, z = coord
        coords: List[Vec3] = [
            (x - d, y, z),
            (x, y - d, z),
            (x, y, z - d),
            (x + d, y, z),
            (x, y + d, z),
            (x, y, z + d),
        ]

        return [
            (coord, self[coord])
            for coord in coords
            if all(c >= 0 for c in coord)
                and all(coord[i] < self.size[i] for i in range(len(self.size)))
        ]

    def find_most_common_grain(self, coord: Vec3, d: int) -> Tuple[Domain, float]:
        """float: is the percentage of the majority domain"""

        neighbors: List[Tuple[Vec3, Point]] = self.find_neighbors(coord, d)

        neighbor_grains: List[Domain] = [n[1].domain for n in neighbors]

        neighbor_grain_count = Counter(neighbor_grains)

        max_grain = neighbor_grain_count.most_common(1)[0]

        return (max_grain[0], max_grain[1] / sum(neighbor_grain_count.values()))

    def find_boundary(self, coord: Vec3, d: int) -> Tuple[Domain, float]:
        return self.find_most_common_grain(coord, d)


@dataclass
class DomainWalls:
    """ Generate Domain Walls
    [Ref.]: P. Marton, I. Rychetsky, and J. Hlinka, Phys. Rev. B 81, 144125 (2011)

    Parameters
    ----------
    size: tuple(3)
        (size_x, size_y, size_z)
    wall: str
        name of DW in Ref., e.g. 
            'T180_001', 'T180_011', 'T90'
            'O180_1-10', 'O180_001', 'O90', 'O60', 'O120'
            'R180_1-10', 'R180_-211', 'R109', 'R71'
            'custom'
    domains: Dict[Vec3, Point]
        domains for system with specified size
    """
    wall: str = field(default=None)
    size: Vec3 = field(default=(12,12,12), metadata={'unit': 'u.c.'})
    domains: Dict[Vec3, Point] = field(default= None)

    def __repr__(self):
        return f"(DomainWalls {self.wall} {self.size} {self.f_out})"

    def get_grains(self, wall: str) -> List[Domain]:
        lx, ly, lz = self.size
        hlx, hly, hlz = lx//2, ly//2, lz//2
        DWs = {'T180_001': [ Domain((0,0,0), Props(-1,0,0)),
                             Domain((0,0,hlz), Props(1,0,0)),
                             Domain((0,0,lz), Props(-1,0,0)) ],
               'T180_011': [ Domain((0,0,0), Props(1,0,0)), 
                             Domain((0,hly-1,hlz-1), Props(-1,0,0)),
                             Domain((0,hly+1,hlz+1), Props(1,0,0)),
                             Domain((0,ly-1,lz-1), Props(-1,0,0)) ],
               'T90': [ Domain((0,ly-1,0), Props(1,0,0)), 
                        Domain((hlx-1,hly+1,0), Props(0,-1,0)),
                        Domain((hlx+1,hly-1,0), Props(1,0,0)),
                        Domain((lx-1,0,0), Props(0,-1,0)) ],
               'O180_1-10': [ Domain((0,ly-1,0), Props(1/np.sqrt(2),1/np.sqrt(2),0)),
                              Domain((hlx-1,hly+1,0), Props(-1/np.sqrt(2),-1/np.sqrt(2),0)),
                              Domain((hlx+1,hly-1,0), Props(1/np.sqrt(2),1/np.sqrt(2),0)),
                              Domain((lx-1,0,0), Props(-1/np.sqrt(2),-1/np.sqrt(2),0)) ],
               'O180_001': [ Domain((0,0,0), Props(-1/np.sqrt(2),-1/np.sqrt(2),0)),
                             Domain((0,0,hlz), Props(1/np.sqrt(2),1/np.sqrt(2),0)),
                             Domain((0,0,lz), Props(-1/np.sqrt(2),-1/np.sqrt(2),0)) ],
               'O90': [ Domain((0,0,0), Props(1/np.sqrt(2),-1/np.sqrt(2),0)),
                        Domain((hlx,0,0), Props(1/np.sqrt(2),1/np.sqrt(2),0)),
                        Domain((lx,0,0), Props(1/np.sqrt(2),-1/np.sqrt(2),0)) ],
               'O60': [ Domain((0,0,0), Props(0,0,0)), 
                        Domain((lx,ly,lz), Props(0,0,0)) ], # undefined
               'O120': [ Domain((0,0,0), Props(1/np.sqrt(2),1/np.sqrt(2),0)), 
                         Domain((hlx-1,0,hlz-1), Props(0,-1/np.sqrt(2),1/np.sqrt(2))),
                         Domain((hlx+1,0,hlz+1), Props(1/np.sqrt(2),1/np.sqrt(2),0)),
                         Domain((lx-1,0,lz-1), Props(0,-1/np.sqrt(2),1/np.sqrt(2))) ],
               'R180_1-10': [ Domain((0,ly-1,0), Props(1/np.sqrt(3),1/np.sqrt(3),1/np.sqrt(3))), 
                              Domain((hlx-1,hly+1,0), Props(-1/np.sqrt(3),-1/np.sqrt(3),-1/np.sqrt(3))),
                              Domain((hlx+1,hly-1,0), Props(1/np.sqrt(3),1/np.sqrt(3),1/np.sqrt(3))),
                              Domain((lx-1,0,0), Props(-1/np.sqrt(3),-1/np.sqrt(3),-1/np.sqrt(3))) ],
               'R180_-211': [ Domain((lx,0,0), Props(1/np.sqrt(3),1/np.sqrt(3),1/np.sqrt(3))), 
                              Domain((0,hly,hlz), Props(-1/np.sqrt(3),-1/np.sqrt(3),-1/np.sqrt(3))) ], # tocheck
               'R109': [ Domain((0,0,0), Props(-1/np.sqrt(3),-1/np.sqrt(3),1/np.sqrt(3))), 
                         Domain((0,0,hlz), Props(1/np.sqrt(3),1/np.sqrt(3),1/np.sqrt(3))),
                         Domain((0,0,lz), Props(-1/np.sqrt(3),-1/np.sqrt(3),1/np.sqrt(3))) ],
               'R71': [ Domain((0,0,0), Props(1/np.sqrt(3),1/np.sqrt(3),1/np.sqrt(3))), 
                        Domain((hlx-1,0,hlz-1), Props(1/np.sqrt(3),-1/np.sqrt(3),1/np.sqrt(3))),
                        Domain((hlx+1,0,hlz+1), Props(1/np.sqrt(3),1/np.sqrt(3),1/np.sqrt(3))),
                        Domain((lx-1,0,lz-1), Props(1/np.sqrt(3),-1/np.sqrt(3),1/np.sqrt(3))) ],
              }
        return DWs.get(wall, None)

    def find_closest_grain(self, grains: List[Domain], coord: Vec3) -> Domain:
        def distance(p1: Vec3, p2: Vec3) -> np.floating:
            return np.linalg.norm(np.fromiter(p1, int) - np.fromiter(p2, int))
    
        return min(
            map(lambda domain: (domain, distance(domain.seed, coord)), grains),
            key=lambda x: float(x[1]),
        )[0]

    def generate_coords(self, size: Vec3) -> List[Vec3]:
        size_x, size_y, size_z = map(range, size)
        return [(x, y, z) for x in size_x for y in size_y for z in size_z]
        
    def find_boundaries(self, size: Vec3, grains: List[Domain]) -> Dict[Vec3, Point]:
        coords: List[Vec3] = self.generate_coords(size)
        system: System = System(
            size,
            {coord: Point(self.find_closest_grain(grains, coord), None) for coord in coords},
        )
    
        boundaries = {
            coord: Point(*system.find_boundary(coord, 1)) for coord in system.points.keys()
        }
    
        return boundaries
    
    def create_domains(self, f_name: str = 'bto', f_ext: str = None, scale: float = 1.) -> Dict[Vec3, Point]:
        """ Create domains or output for Feram

        Parameters
        ----------
        f_name: str
            file name w/o extension, default: 'bto'
        f_ext: str
            output extension, e.g. 'localfield', 'defects'
        scale: float
            scaling factor for each grain's property, default: 1.
        """
        def create_localfield(system: Dict[Vec3, Point]):
            with open(f"{f_name}.localfield", "w") as f:
                for coord, point in system.items():
                    x, y, z = coord
                    px, py, pz = point.domain.props
                    f.write(f"{x} {y} {z} {px} {py} {pz}\n")
    
        def create_defects(system: Dict[Vec3, Point]):
            with open(f"{f_name}.defects", "w") as f:
                for coord, point in system.items():
                    x, y, z = coord
                    px, py, pz = point.domain.props
        
                    if point.boundary != None and point.boundary < 1:
                        f.write(f"{x} {y} {z} {px} {py} {pz}\n")

        grains = self.get_grains(self.wall)
        if scale!=1:
            grains_scale = []
            for domain in grains:
                x, y, z = domain.seed
                px, py, pz = domain.props
                px_s, py_s, pz_s = px*scale, py*scale, pz*scale
                grains_scale.append(Domain((x,y,z), Props(px_s, py_s, pz_s)))
            grains = grains_scale

        self.domains = self.find_boundaries(self.size, grains)
        if f_ext == 'localfield':
            create_localfield(self.domains)
        elif f_ext == 'defects':
            create_defects(self.domains)
        return self.domains


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Create Domain Walls.')
    parser.add_argument('wall', metavar='w', type=str, nargs=1, 
                        help='wall type')
    parser.add_argument('--size', dest='size', nargs='+', type=int, default=None, 
                        help='size of retangule, e.g. 12 or 12 12 12 (default=None)')
    parser.add_argument('-c', dest='scale', nargs=1, type=float, default=1, 
                        help='scaling factor for each grain''s property (default=1)')
    choices = ['localfield', 'defects']
    parser.add_argument('-o', dest='options', type=str, default='localfield', choices=choices,
                        help='output file extension, e.g. [localfield], defects')
    args = parser.parse_args()
    wall = args.wall[0]
    size = args.size
    scale = args.scale[0]
    if size is not None:
        if len(size)==1:
            size = tuple([int(size[0])]*3)
        elif len(size) not in [1,3]:
            print('[Error]: Incorrect arguments for --size!')
    ext = args.options
    DW = DomainWalls(wall=wall, size=size)
    system = DW.create_domains(f_name='bto', f_ext=ext, scale=scale)
