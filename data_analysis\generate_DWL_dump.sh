#!/bin/sh
export PATH="$HOME/Documents/anaconda3/envs/py38/bin/":$PATH
export PYTHONPATH="$HOME/storage/gitlab_rub/fedas/src/":$PYTHONPATH

FILE=$1

echo ">>> Start `date`"

python3 -c "
import numpy as np
from analyze_feram import Dipo_Analysis

kernel = (1,1,11) # (nz,ny,nx)
knl = np.ones(kernel)
key = {'uy': lambda u: 0.1 if u>=0 else -0.1, 'ux': lambda u: 0, 'uz': lambda u: 0}

DA = Dipo_Analysis('${FILE}')
DA.load_data(is_dump=True, iframe=':')
Size = DA.Size
DA.select_data(i_list=range(82)) # only left DW
res = DA.find_DW_surf(norm=0, ui=1, kernel=knl, key=key, display=False)
TXT = []
for i in res:
    txt = 'ITEM: TIMESTEP\n'
    txt += f'{i}\nITEM: NUMBER OF ATOMS\n'
    lz,ly = res[i].shape
    xmean = np.mean(res[i])
    n = len(res[0].ravel())
    txt += f'{n}\nITEM: BOX BOUNDS pp pp pp\n'
    txt += f'0 {Size[i][0]}\n0 {Size[i][1]}\n0 {Size[i][2]}\n'
    txt += 'ITEM: ATOMS id type xu yu zu dh\n'
    k = 1
    for z in range(lz):
        for y in range(ly):
            x = res[i][z,y]
            txt += f'{k} 0 {x} {y} {z} {x-xmean}\n'
            k+=1
    TXT.append(txt)

with open('dump_dw', 'w') as fw:
    for T in TXT:
        fw.write(T)
"
echo ">>> Finish `date`"
