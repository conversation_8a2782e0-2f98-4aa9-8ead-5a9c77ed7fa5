import os
import argparse
from custom_csv import Plot_DWSUM

# input file
parser = argparse.ArgumentParser()
parser.add_argument('infile', metavar='infile', type=str, nargs=1, help='filename')
args = parser.parse_args()
infile = args.infile[0]

# main
ext = '.png'
P = Plot_DWSUM(infile)
P.load_df()

# pristine
temperatures = [240, 260, 280]
labels = [f'{temp}K' for temp in temperatures]
P.set_div(div=1)
P.unload_system()
P.load_system(conc=0, config_id=0, sim_ids=range(10), prefix=None)
P.set_lr(lr='L')
P.plot_item_t(temperatures, labels=labels, item='x', x0=None, xd=None, 
              groupby='r', show_all=False, if_sfc=True, ylim=(None,82), figsize=(5,5), fig_out='dw_pt_xt'+ext)
P.plot_item_t(temperatures, labels=labels, item='v', x0=None, xd=None, 
              groupby='r', show_all=False, if_sfc=True, ylim=(0,3.5), figsize=(5,5), fig_out='dw_pt_vt'+ext)
P.plot_item_t(temperatures, labels=labels, item='r', x0=None, xd=None, 
              groupby='r', show_all=False, if_sfc=True, ylim=(0,4.5), figsize=(5,5), fig_out='dw_pt_rt'+ext)
P.unload_system()
P.load_system(conc=0, config_id=0, prefix=None)
P.set_lr(lr='L')
P.plot_items_t(temperatures, labels, xd=None, groupby='r',
               tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
               rlim=(0,4.5), show_all=False, if_sfc=2, figsize=(6,6), fig_out='dw_pt_t'+ext)

# rand 3D
temperatures = [240, 260, 280]
labels = [f'{temp}K' for temp in temperatures]
P.set_div(div=1)
## 0.5%
P.unload_system()
P.load_system(conc=0.5, config_id=0, sim_ids=range(10), prefix='3D')
P.set_lr(lr='L')
P.plot_item_t(temperatures, labels=labels, item='x', x0=None, xd=None, 
              groupby='r', show_all=False, if_sfc=True, ylim=(None,82), figsize=(5,5), fig_out='dw_r3d.5p_xt'+ext)
P.plot_item_t(temperatures, labels=labels, item='v', x0=None, xd=None, 
               groupby='r', show_all=False, if_sfc=True, ylim=(0,3.5), figsize=(5,5), fig_out='dw_r3d.5p_vt'+ext)
P.plot_item_t(temperatures, labels=labels, item='r', x0=None, xd=None, 
               groupby='r', show_all=False, if_sfc=True, ylim=(0,4.5), figsize=(5,5), fig_out='dw_r3d.5p_rt'+ext)
P.unload_system()
P.load_system(conc=0.5, config_id=0, prefix='3D')
P.set_lr(lr='L')
P.plot_items_t(temperatures, labels, xd=None, groupby='r',
               tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
               rlim=(0,4.5), show_all=False, if_sfc=True, figsize=(6,6), fig_out='dw_r3d.5p_t'+ext)
## 1%
P.unload_system()
P.load_system(conc=1, config_id=0, sim_ids=range(10), prefix='3D')
P.set_lr(lr='L')
P.plot_item_t(temperatures, labels=labels, item='x', x0=None, xd=None, 
              groupby='r', show_all=False, if_sfc=True, ylim=(None,82), figsize=(5,5), fig_out='dw_r3d1p_xt'+ext)
P.plot_item_t(temperatures, labels=labels, item='v', x0=None, xd=None, 
               groupby='r', show_all=False, if_sfc=True, ylim=(0,3.5), figsize=(5,5), fig_out='dw_r3d1p_vt'+ext)
P.plot_item_t(temperatures, labels=labels, item='r', x0=None, xd=None, 
               groupby='r', show_all=False, if_sfc=True, ylim=(0,4.5), figsize=(5,5), fig_out='dw_r3d1p_rt'+ext)
P.unload_system()
P.load_system(conc=1, config_id=0, prefix='3D')
P.set_lr(lr='L')
P.plot_items_t(temperatures, labels, xd=None, groupby='r',
               tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
               rlim=(0,4.5), show_all=False, if_sfc=True, figsize=(6,6), fig_out='dw_r3d1p_t'+ext)
## 2%
P.unload_system()
P.load_system(conc=2, config_id=0, prefix='3D')
P.set_lr(lr='L')
P.plot_items_t(temperatures, labels, xd=None, groupby='r',
               tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
               rlim=(0,4.5), show_all=False, if_sfc=True, figsize=(6,6), fig_out='dw_r3d2p_t'+ext)
## comparison (3D)
P.set_lr(lr='L')
P.plot_concs_t(temp=260, config_ids=[0], sim_ids=range(10), concs=[0,0.5,1,2], prefix='3D',
               x0=None, xd=None, tlim=(0,100), xlim_l=(None,None), xlim_r=(82,None), vlim_l=(None,2.), vlim_r=(-2.,0), 
               rlim=(0,4.5), if_sfc=True, figsize=(6,6), fig_out='dw_r3dall_t'+ext)
P.plot_x_t(temp=260, config_ids=[0], sim_ids=range(10), concs=[0,0.5,1,2], prefix='3D',
           figsize=(5,5), fig_out='dw_r3dall_xt'+ext)
P.plot_v_t(temp=260, config_ids=[0], sim_ids=range(10), concs=[0,0.5,1,2], prefix='3D',
           figsize=(5,5), fig_out='dw_r3dall_vt'+ext)
P.plot_rgh_t(temp=260, config_ids=[0], sim_ids=range(10), concs=[0,0.5,1,2], prefix='3D',
             ylim=(0,None), figsize=(5,5), fig_out='dw_r3dall_rgh'+ext)
## ud sensitivity for 1%
P.set_lr(lr='L')
config_ids = [0, 3, 4]
Uid = {0: 0.09, 3: 0.11, 4: 0.15} # ud in Ang.
for temp in [240, 260, 280]:
    labels = []
    for conf in config_ids:
        labels.append(fr'{Uid[conf]}$\AA$')
    P.unload_system()
    P.plot_confs_t(config_ids=config_ids, sim_ids=range(10), conc=1, prefix='3D',
                   temperatures=[temp], labels=labels, xd=None, groupby='r',
                   tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
                   rlim=(0,4.5), show_all=False, if_sfc=True, figsize=(6,6), fig_out=f'dw_r3d1p_udt_{temp}K'+ext)

# 1L
temperatures = [240, 260, 280]
labels = [f'{temp}K' for temp in temperatures]
P.set_div(div=1)
P.unload_system()
P.set_lr(lr='L')
P.load_system(conc=3.5, config_id=0, sim_ids=range(5), prefix=None)
P.plot_item_t(temperatures, labels=labels, item='x', x0=None, xd=50, 
              groupby='r', show_all=False, if_sfc=True, ylim=(None,82), figsize=(5,5), fig_out='dw_1L3.5p_xt'+ext)
P.plot_item_t(temperatures, labels=labels, item='v', x0=None, xd=None, 
              groupby='r', show_all=False, if_sfc=True, ylim=(0,3.5), figsize=(5,5), fig_out='dw_1L3.5p_vt'+ext)
P.plot_item_t(temperatures, labels=labels, item='r', x0=None, xd=None, 
               groupby='r', show_all=False, if_sfc=True, ylim=(0,4.5), figsize=(5,5), fig_out='dw_1L3.5p_rt'+ext)
P.unload_system()
P.load_system(conc=3.5, config_id=0, prefix=None)
P.set_lr(lr='L')
P.plot_items_t(temperatures, labels, xd=49.5, groupby='r',
               tlim=(0,100), xlim_l=(None,82), xlim_r=(82,None), vlim_l=(0,3.5), vlim_r=(-3.5,0), 
               rlim=(0,4.5), show_all=False, if_sfc=True, figsize=(6,6), fig_out='dw_1L3.5p_t'+ext)
## comparison (1L)
P.set_lr(lr='L')
P.plot_concs_t(temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,2,3.5,4,7], prefix=None,
               x0=None, xd=49.5, tlim=(0,100), xlim_l=(None,None), xlim_r=(82,None), vlim_l=(None,2.), vlim_r=(-2.,0), 
               rlim=(0,4.5), if_sfc=True, figsize=(6,6), fig_out='dw_1Lall_t'+ext)
P.plot_x_t(temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,2,3.5,4,7], prefix=None, 
           x0=None, xd=50, ylim=(None,82), figsize=(5,5), fig_out='dw_1Lall_xt'+ext)
P.plot_v_t(temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,2,3.5,4,7], prefix=None, 
           x0=None, ylim=(None,3.5), figsize=(5,5), fig_out='dw_1Lall_vt'+ext)
P.plot_rgh_t(temp=260, config_ids=[0,1], sim_ids=range(10), concs=[0,2,3.5,4,7], prefix=None, 
             x0=None, ylim=(0,None), figsize=(5,5), fig_out='dw_1Lall_rgh'+ext)
for conc in [0,3.5,7]:
    for div in [1,3,12]:
        sim_ids = range(10) if conc==0 else range(5)
        P.set_div(div=div)
        P.plot_x_t(temp=260, config_ids=[0], sim_ids=sim_ids, concs=[conc], prefix=None,
                   x0=None, xd=50, figsize=(6,6), fig_out=f'dw_1L{conc}p_div{div}_xt'+ext)

# move to figures/
os.system('mkdir figures/')
os.system('mv *.png *.pdf figures/')
