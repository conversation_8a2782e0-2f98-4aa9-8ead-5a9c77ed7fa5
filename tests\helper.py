import os
import numpy as np


class Helper():
    """ Helper functions for testing """
    def __init__(self):
        """
        Attributes
        ----------
        """
        return

    def create_mod(self, L=10, r_Ba=0.7, seed=0, f_out='example.modulation', overwrite=False):
        """ create modulation file

        Parameters
        ----------
        L: int
            size of simulation box
        r_Ba: float
            ratio of Ba in BST
        seed: int
            random seed
        f_out: str
            output file name

        Returns
        -------
        None
        """
        if f_out!=None:
            if os.path.exists(f_out):
                if overwrite:
                    print(f'[Warning]: {f_out} is overwritten!')
                else:
                    print(f'[Warning]: {f_out} exists!')
                    return
        ntot = L*L*L
        NSites = {'Ba': int(ntot*r_Ba), 'Sr': ntot-int(ntot*r_Ba)}
        np.random.seed(seed)
        sites = [8 for _ in range(NSites['Ba'])]+[-8 for _ in range(NSites['Sr'])]
        np.random.shuffle(sites)
        with open(f_out, 'w') as fw:
            for k in range(L):
                for j in range(L):
                    for i in range(L):
                        fw.write('{} {} {} {}\n'.format(i,j,k,sites.pop()))
                    
    def create_domains(self, L=10, mode='sd', rand=False, fmt='coord', f_out='example_sd.coord',
                       overwrite=False, display=False):
        """ create coord/dipoRavg file
        
        Parameters
        ----------
        mode: str
            ['sd']: single domain, 't180'
        fmt: str
            format; 'coord', 'dipoRavg'
        f_out: str
            output file name
        display: bool
            display output
            
        Returns
        -------
        None
        
        Examples
        --------
        >>> H = Helper()
        >>> H.create_domains(mode='sd', fmt='coord', f_out=None, display=True)
          0   0   0  3.954404e-03 -1.440211e-02  1.012833e-01  ...  1.098578e+00  9.947240e-01  1.015693e+00
        ...
          9   9   9 -1.089818e-02 -1.636711e-02  8.791081e-02  ...  1.098578e+00  9.947240e-01  1.015693e+00
        >>> H.create_domains(mode='sd', fmt='dipoRavg', f_out=None, display=True)
          0   0   0 -8.884265e-03  2.010933e-03  1.158264e-01 
        ...
          9   9   9  5.073801e-03  2.946248e-03  9.726748e-02 
        """
        if f_out!=None:
            if os.path.exists(f_out):
                if overwrite:
                    print(f'[Warning]: {f_out} is overwritten!')
                else:
                    print(f'[Warning]: {f_out} exists!')
                    return

        np.random.seed(0)
        rng = np.random.default_rng()
        size = L**3
        if rand:
            ux = rng.normal(loc=0., scale=0.001, size=size)
            uy = rng.normal(loc=0., scale=0.001, size=size)
            uz = rng.normal(loc=0.1, scale=0.01, size=size)
        else:
            ux = np.zeros(size)
            uy = np.zeros(size)
            uz = np.ones(size)*0.1

        # extra items
        if fmt=='coord':
            rnd_ary = rng.normal(loc=1, scale=0.1, size=12)
            extra_items = ' '.join([str(f'{_: 2.6e}') for _ in rnd_ary])
        elif fmt=='dipoRavg':
            extra_items = ''

        text = ''
        if mode=='sd':
            i=0
            for iz in np.arange(L):
                for iy in np.arange(L):
                    for ix in np.arange(L):
                        text+=f'{ix: 3d} {iy: 3d} {iz: 3d} {ux[i]: 2.6e} {uy[i]: 2.6e} {uz[i]: 2.6e} '
                        text+=extra_items
                        text+='\n'
                        i+=1
        elif mode=='t180':
            i=0
            for iz in np.arange(L):
                for iy in np.arange(L):
                    for ix in np.arange(L):
                        if ix<L//2:
                            text+=f'{ix: 3d} {iy: 3d} {iz: 3d} {ux[i]: 2.6e} {uy[i]: 2.6e} {uz[i]: 2.6e} '
                        else:
                            text+=f'{ix: 3d} {iy: 3d} {iz: 3d} {ux[i]: 2.6e} {uy[i]: 2.6e} {uz[i]*(-1): 2.6e} '
                        text+=extra_items
                        text+='\n'
                        i+=1

        if f_out!=None:
            with open(f_out, 'w') as fw:
                fw.write(text)

        if display:
            print(text)

    def generate_defects(self, L=10, n=1, mode='rand', seed=0, f_out='example.defects', overwrite=False, display=False):
        """ create coord/dipoRavg file
        
        Parameters
        ----------
        mode: str
            ['rand'], 'stripe'
        f_out: str
            output file name
        overwrite: bool
            to be overwritten or not
        display: bool
            display output
            
        Returns
        -------
        None
        """
        if f_out!=None:
            if os.path.exists(f_out):
                if overwrite:
                    print(f'[Warning]: {f_out} is overwritten!')
                else:
                    print(f'[Warning]: {f_out} exists!')
                    return

        np.random.seed(seed)
        size = L**3
        sites = np.arange(size)
        np.random.shuffle(sites)
        dsites = sites[:n]

        text = ''
        if mode=='rand':
            i=0
            for iz in np.arange(L):
                for iy in np.arange(L):
                    for ix in np.arange(L):
                        if i in dsites:
                            text+=f'{ix: 3d} {iy: 3d} {iz: 3d} 0 0 0'
                            text+='\n'
                        i+=1
        elif mode=='stripe':
            i=0
            for iz in np.arange(L):
                for iy in np.arange(L):
                    for ix in np.arange(L):
                        if ix<L//2 and iz==L//2 and i<n:
                            text+=f'{ix: 3d} {iy: 3d} {iz: 3d} 0 0 0'
                            text+='\n'
                            i+=1

        if f_out!=None:
            with open(f_out, 'w') as fw:
                fw.write(text)

        if display:
            print(text)

    def create_hl(self, mode=0, f_out='example.hl'):
        """ Creating hl file
        
        Parameters
        ----------
        f_out: str
            output file for hl
        
        Returns
        -------
        hl_ary: np.array
            hl data array
        """
        if mode in [0,5]:
            Step = np.arange(2500,25001,2500)
            T = 100*np.ones(len(Step))
            Ex = np.arange(10)
            Ey = 0*np.ones(len(Step))
            Ez = np.arange(10)
            ee = 0*np.ones(len(Step))
            ux = [-1.0]*5+[-0.5,0.0,0.5]+[1.0]*2
            uy = uz = 0*np.ones(len(Step))
            eng = [-1*s for s in range(len(Step))]
        elif mode==1:
            Step = [0,1,2,3,4,5,6,7,8]
            T = 200*np.ones(len(Step))
            Ex = [-4,-3,-2,-1,0,1,2,3,4]
            Ey = 0*np.ones(len(Step))
            Ez = 0*np.ones(len(Step))
            ee = 0*np.ones(len(Step))
            ux = [-2,-2,-2,-2,-2,0,2,2,2]
            uy = uz = 0*np.ones(len(Step))
        elif mode==2:
            Step = [0,1,2,3,4,5,6,7,8]
            T = 200*np.ones(len(Step))
            Ex = [-4,-3,-2,-1,0,1,2,3,4]
            Ey = 0*np.ones(len(Step))
            Ez = 0*np.ones(len(Step))
            ee = 0*np.ones(len(Step))
            ux = [-2,-2,-2,0,2,2,2,2,2]
            uy = uz = 0*np.ones(len(Step))
        elif mode==3:
            Step = [0,1,2,3,4,5,6,7,8]
            T = 300*np.ones(len(Step))
            Ex = [-4,-3,-2,-1,0,1,2,3,4]
            Ey = 0*np.ones(len(Step))
            Ez = 0*np.ones(len(Step))
            ee = 0*np.ones(len(Step))
            ux = [-2,-2,-2,-2,-2,-2,0,2,2]
            uy = uz = 0*np.ones(len(Step))
        elif mode==4:
            Step = [0,1,2,3,4,5,6,7,8]
            T = 300*np.ones(len(Step))
            Ex = [-4,-3,-2,-1,0,1,2,3,4]
            Ey = 0*np.ones(len(Step))
            Ez = 0*np.ones(len(Step))
            ee = 0*np.ones(len(Step))
            ux = [-2,-2,-2,-2,0,2,2,2,2]
            uy = uz = 0*np.ones(len(Step))
        with open(f_out,'w') as fw:
            for i,s in enumerate(Step):
                line = '{:010d} {:.1f} {:.3f} {:.3f} {:.3f} '.format(s,T[i],Ex[i],Ey[i],Ez[i])
                line += '{} {} {} {} {} {} {} {} {} '.format(ee[i],ee[i],ee[i],ee[i],ee[i],ee[i],ux[i],uy[i],uz[i])
                if mode in [5]:
                    line += ' {}'.format(eng[i])*21
                fw.write(line+'\n')

    def create_dump(self, mode=0, f_out='dump_example'):
        """ Creating dump file
        
        Parameters
        ----------
        mode: int
            [0]: 3 frames
            1: 6 frames
            2: 10 frames with moving DW
        f_out: str
            output file for hl
        
        Returns
        -------
        dump_ary: np.array
            dump data array
        """
        if mode==0:
            nsteps = 3
            Lx, Ly, Lz = 2, 2, 2
            n = Lx*Ly*Lz
            ux = np.array([-1.0]*3+[-0.5,0.0,0.5]+[1.0]*2).reshape(Lz,Ly,Lx)
            uy = uz = 0*np.ones(n).reshape(Lz,Ly,Lx)
            SUM = []
            for i in range(nsteps):
                SUM.append([ux, uy, uz])
        elif mode==1:
            nsteps = 6
            Lx, Ly, Lz = 3, 3, 3
            n = Lx*Ly*Lz
            ux = np.array([-1.0]*10+[0]*7+[1.0]*10).reshape(Lz,Ly,Lx)
            uy = uz = 0*np.ones(n).reshape(Lz,Ly,Lx)
            SUM = []
            for i in range(nsteps):
                SUM.append([ux, uy, uz])
        elif mode==2:
            wall_pos = [3, 6, 8, 9, 10, 11, 12, 15, 16, 18]
            Lx, Ly, Lz = 20, 3, 3
            n = Lx*Ly*Lz
            SUM = []
            for i in wall_pos:
                ux = uz = np.zeros((Lz, Ly, Lx))
                uy = np.ones((Lz, Ly, Lx))
                uy[:,:,i:] = -1
                SUM.append([ux, uy, uz])
        with open(f_out,'w') as fw:
            for i, (ux,uy,uz) in enumerate(SUM):
                fw.write('ITEM: TIMESTEP\n')
                fw.write(f'{i}\t step {i}\n')
                fw.write('ITEM: NUMBER OF ATOMS\n')
                fw.write(f'{n}\n')
                fw.write('ITEM: BOX BOUNDS pp pp pp\n')
                fw.write(f'0 {Lx}\n0 {Ly}\n0 {Lz}\n')
                fw.write('ITEM: ATOMS id type v_mod xu yu zu fx fy fz\n')
                nz, ny, nx = np.shape(ux)
                j = 0
                for z in range(nz):
                    for y in range(ny):
                        for x in range(nx):
                            line = f'{j+1} 0 0 {x} {y} {z} '
                            line += f'{ux[z,y,x]:.6f} {uy[z,y,x]:.6f} {uz[z,y,x]:.6f}\n'
                            fw.write(line)
                            j+=1
